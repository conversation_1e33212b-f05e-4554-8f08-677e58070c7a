{"compilerOptions": {"target": "esnext", "module": "esnext", "strict": false, "jsx": "preserve", "importHelpers": true, "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "sourceMap": true, "skipLibCheck": true, "baseUrl": ".", "types": ["webpack-env", "@dcloudio/types", "miniprogram-api-typings", "mini-types"], "paths": {"@/*": ["./src/*"]}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"]}, "exclude": ["node_modules", "unpackage", "src/**/*.nvue"]}