const CopyWebpackPlugin = require("copy-webpack-plugin");

module.exports = {
  chainWebpack: config => {
    // 关闭 TS 类型校验
    config.plugins.delete('fork-ts-checker')

    // 关闭 ESLint 校验
    config.module.rules.delete('eslint')
  },
  lintOnSave: false,
  configureWebpack: {
    plugins: [
      new CopyWebpackPlugin([
        {
          from: "src/pages/**/wxcomponents/**/*",
          to: "[path][name].[ext]",
          transformPath(targetPath) {
            let result = targetPath.replace(/^src\//gi, "").replace(/^src\\/gi, "");
            return result;
          },
        },
      ]),
    ],
  },
};
