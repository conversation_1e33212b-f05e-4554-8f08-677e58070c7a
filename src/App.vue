<script lang="ts">
import Vue from "vue";
import { Component } from "vue-property-decorator";

@Component({
  name: "app",
})
export default class App extends Vue {
  onLaunch(options: any): void {
    // 小程序更新
    const updateManager = wx.getUpdateManager();
    updateManager.onCheckForUpdate(function (res) {
      // 请求完新版本信息的回调
      console.error(`小程序客户端有更新${res.hasUpdate}`);
    });
    updateManager.onUpdateReady(function () {
      wx.showModal({
        title: "更新提示",
        content: "新版本已经准备好，是否重启应用？",
        success: function (res) {
          if (res.confirm) {
            // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
            updateManager.applyUpdate();
          }
        },
      });
    });
    updateManager.onUpdateFailed(function () {
      // 新版本下载失败
      console.error(`小程序更新失败`);
    });
    wx.setInnerAudioOption({
      obeyMuteSwitch: false,
      success: function (res) {
        console.log("开启静音模式下播放音乐的功能");
      },

      fail: function (err) {
        console.log("静音设置失败");
      },
    });
  }
  onShow(): void {
    console.log("App Show");
  }
  onHide(): void {
    console.log("App Hide");
  }
}
</script>

<style lang="scss">
/*每个页面公共css */
@import url("./static/css/common.scss");
page {
  width: 100%;
  height: 100%;
  .van-cell {
    border-radius: 10px !important;
    background-color: #f1f1f1 !important;
  }
  .van-button {
    border-radius: 10px !important;
    .van-button--info {
      background: linear-gradient(90deg, rgba(60, 122, 233, 1) 0%, rgba(87, 144, 255, 1) 100%) !important;
    }
    button {
      width: 100%;
    }
  }
  .van-dialog__button {
    padding: 10px 0;
  }
  .van-divider {
    color: #999999 !important;
    width: 100%;
  }
  .van-button--info {
    background: linear-gradient(90deg, rgba(60, 122, 233, 1) 0%, rgba(87, 144, 255, 1) 100%) !important;
  }
  .van-switch--on {
    background-color: #0054d9;
  }
  .van-checkbox__icon--checked {
    background-color: #0054d9;
    border-color: #0054d9;
  }
  .van-tabs__line {
    background-color: #0289ea;
  }
  .van-sidebar-item--selected {
    border-color: #0289ea;
  }
}
</style>
