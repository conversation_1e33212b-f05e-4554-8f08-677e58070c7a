// envVersion: 'develop',    //开发版
// envVersion: 'trial',          //体验版
// envVersion: 'release',     //正式版

const envVersion = __wxConfig.envVersion;
// const envVersion = "release";
const getStorageEnv = () => {
  const canSwitchEnv = process.env.VUE_APP_ENV_SWITCH === "on" ? true : false;
  if (!canSwitchEnv) {
    return "";
  }
  return (uni.getStorageSync("currentEnv") || {}).value || "0";
};
export { envVersion, getStorageEnv };
