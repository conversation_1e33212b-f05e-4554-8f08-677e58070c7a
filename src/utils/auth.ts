/**
 * 用户认证工具类
 * 统一处理用户登录状态检查和登录流程
 */
import { handleLogin } from "@/api/auth.api";
import { AccountType } from "@/constants/enum/account-type.enum";

export interface UserInfo {
  id: string;
  name: string;
  phone: string;
  avatar?: string;
  openid?: string;
}

export interface WxUserInfo {
  nickName: string;
  avatarUrl: string;
  gender: number;
  country: string;
  province: string;
  city: string;
  language: string;
}

export class AuthManager {
  private static instance: AuthManager;

  private constructor() {
    console.log("AuthManager 初始化");

    // 监听web-auth登录成功事件
    uni.$on("userLoginSuccess", (userInfo: UserInfo) => {
      console.log("AuthManager - 接收到登录成功事件:", userInfo);
      // 可以在这里做一些额外的处理，比如更新全局状态
    });
  }

  public static getInstance(): AuthManager {
    if (!AuthManager.instance) {
      AuthManager.instance = new AuthManager();
    }
    return AuthManager.instance;
  }

  /**
   * 获取用户ID，如果未登录则弹窗提示登录
   * @param showLoginDialog 是否显示登录弹窗，默认为true
   * @returns 用户ID或null
   */
  public async getUserId(showLoginDialog = true): Promise<string | null> {
    try {
      const userInfo = uni.getStorageSync("userInfo");

      if (userInfo && userInfo.id) {
        console.log("获取到用户ID:", userInfo.id);
        return String(userInfo.id);
      }

      // 没有用户信息时的处理
      console.warn("未找到用户信息，需要登录");

      if (showLoginDialog) {
        return await this.showLoginDialog();
      }

      return null;
    } catch (error) {
      console.error("获取用户信息失败:", error);

      if (showLoginDialog) {
        return await this.showLoginDialog();
      }

      return null;
    }
  }

  /**
   * 显示登录弹窗
   * @returns Promise<string | null> 用户ID或null
   */
  private showLoginDialog(): Promise<string | null> {
    return new Promise((resolve) => {
      uni.showModal({
        title: "登录提示",
        content: "您还未登录，是否立即登录？",
        confirmText: "立即登录",
        cancelText: "暂不登录",
        success: async (res) => {
          if (res.confirm) {
            // 用户选择登录
            const userId = await this.performLogin();
            resolve(userId);
          } else {
            // 用户取消登录
            console.log("用户取消登录");
            resolve(null);
          }
        },
        fail: () => {
          console.error("显示登录弹窗失败");
          resolve(null);
        },
      });
    });
  }

  /**
   * 执行登录流程
   * @returns Promise<string | null> 用户ID或null
   */
  private async performLogin(): Promise<string | null> {
    try {
      // 检查是否已有微信用户信息
      const wxUserInfo = uni.getStorageSync("wxUserInfo");

      if (!wxUserInfo) {
        // 没有微信用户信息，需要进行微信授权
        console.log("AuthManager - 没有微信用户信息，跳转到web授权页面");
        return await this.navigateToWebAuth();
      } else {
        // 已有微信用户信息，检查登录状态
        const token = uni.getStorageSync("token");
        const userInfo = uni.getStorageSync("userInfo");

        if (userInfo && token && token.expires > Date.now()) {
          // 已登录且token有效
          console.log("AuthManager - 用户已登录，token有效");
          return String(userInfo.id);
        } else {
          // 需要重新登录
          console.log("AuthManager - 需要重新登录");
          const newUserInfo = await handleLogin();
          uni.setStorageSync("userInfo", newUserInfo);
          return String(newUserInfo.id);
        }
      }
    } catch (error) {
      console.error("登录流程失败:", error);

      // 登录失败时的处理
      uni.showToast({
        title: "登录失败，请重试",
        icon: "none",
        duration: 2000,
      });
      return null;
    }
  }

  /**
   * 获取微信用户信息
   * @returns Promise<WxUserInfo | null>
   */
  private getUserProfile(): Promise<WxUserInfo | null> {
    return new Promise((resolve) => {
      if (typeof wx === "undefined") {
        resolve(null);
        return;
      }

      wx.getUserProfile({
        desc: "用于完善用户资料",
        success: (res) => {
          console.log("获取微信用户信息成功:", res.userInfo);
          resolve(res.userInfo);
        },
        fail: (error) => {
          console.error("获取微信用户信息失败:", error);
          resolve(null);
        },
      });
    });
  }
  /**
   * 检查用户是否已登录
   * @returns boolean
   */
  public isLoggedIn(): boolean {
    try {
      const userInfo = uni.getStorageSync("userInfo");
      return !!(userInfo && userInfo.id);
    } catch (error) {
      console.error("检查登录状态失败:", error);
      return false;
    }
  }

  /**
   * 获取当前用户信息
   * @returns UserInfo | null
   */
  public getCurrentUser(): UserInfo | null {
    try {
      const userInfo = uni.getStorageSync("userInfo");
      return userInfo || null;
    } catch (error) {
      console.error("获取当前用户信息失败:", error);
      return null;
    }
  }

  /**
   * 跳转到web授权页面进行登录
   * @returns Promise<string | null>
   */
  public async navigateToWebAuth(): Promise<string | null> {
    return new Promise((resolve) => {
      // 监听登录成功事件
      const handleLoginSuccess = (userInfo: UserInfo) => {
        console.log("AuthManager - web授权登录成功:", userInfo);
        uni.$off('userLoginSuccess', handleLoginSuccess);
        resolve(userInfo.id);
      };

      // 注册事件监听
      uni.$on('userLoginSuccess', handleLoginSuccess);

      // 跳转到web授权页面
      uni.navigateTo({
        url: "/pages/auth/web-auth",
        success: () => {
          console.log("AuthManager - 成功跳转到web授权页面");
        },
        fail: (error) => {
          console.error("AuthManager - 跳转到web授权页面失败:", error);
          uni.$off('userLoginSuccess', handleLoginSuccess);
          resolve(null);
        }
      });

      // 设置超时处理（5分钟）
      setTimeout(() => {
        uni.$off('userLoginSuccess', handleLoginSuccess);
        // 不自动resolve，让用户自己决定
      }, 300000);
    });
  }

  /**
   * 退出登录
   */
  public logout(): void {
    try {
      uni.removeStorageSync("userInfo");
      uni.removeStorageSync("wxUserInfo");

      console.log("用户已退出登录");

      uni.showToast({
        title: "已退出登录",
        icon: "success",
        duration: 2000,
      });
    } catch (error) {
      console.error("退出登录失败:", error);
    }
  }

  /**
   * 强制登录（用于需要登录才能访问的页面）
   * @returns Promise<string | null>
   */
  public async requireLogin(): Promise<string | null> {
    const userId = await this.getUserId(true);

    if (!userId) {
      // 如果用户拒绝登录，跳转到首页
      uni.switchTab({
        url: "/pages/home/<USER>",
      });
    }

    return userId;
  }

  /**
   * 数据操作前的登录检查（用于添加购物车、收藏等操作）
   * @param actionName 操作名称，用于提示信息
   * @returns Promise<string | null>
   */
  public async requireLoginForAction(actionName = "此操作"): Promise<string | null> {
    // 先静默检查是否已登录
    const userId = await this.getUserId(false);
    if (userId) {
      return userId;
    }

    // 未登录时显示操作相关的登录提示
    return new Promise((resolve) => {
      uni.showModal({
        title: "登录提示",
        content: `${actionName}需要登录，是否立即登录？`,
        confirmText: "立即登录",
        cancelText: "取消",
        success: async (res) => {
          if (res.confirm) {
            // 用户选择登录
            const loginUserId = await this.getUserId(true);
            resolve(loginUserId);
          } else {
            // 用户取消登录
            resolve(null);
          }
        },
        fail: () => {
          resolve(null);
        },
      });
    });
  }
}

// 导出单例实例
export const authManager = AuthManager.getInstance();

// 导出便捷方法
export const getUserId = (showLoginDialog = true) => authManager.getUserId(showLoginDialog);
export const isLoggedIn = () => authManager.isLoggedIn();
export const getCurrentUser = () => authManager.getCurrentUser();
export const logout = () => authManager.logout();
export const requireLogin = () => authManager.requireLogin();
export const requireLoginForAction = (actionName = "此操作") => authManager.requireLoginForAction(actionName);
export const navigateToWebAuth = () => authManager.navigateToWebAuth();
