/**
 * 退款工具类
 * 封装退款相关的业务逻辑和工具方法
 */

import { applyRefund, checkRefundEligibility, RefundApplyParams } from "@/api/refund.api";
import { RefundEligibilityResult, REFUND_STATUS_MAP, REFUND_REASONS } from "@/types/refund.types";
import { Order } from "@/types/mall.types";

class RefundManager {
  /**
   * 检查订单是否可以申请退款
   */
  static async checkRefundEligibility(order: Order, customerId: string): Promise<RefundEligibilityResult> {
    try {
      const response = await checkRefundEligibility(order.id, customerId);
      if (response.code === 0) {
        return response.data;
      }

      // 如果后端接口不可用，使用前端逻辑判断
      return this.checkRefundEligibilityLocal(order);
    } catch (error) {
      console.error("检查退款资格失败:", error);
      // 降级到本地检查
      return this.checkRefundEligibilityLocal(order);
    }
  }

  /**
   * 本地退款资格检查（降级方案）
   */
  static checkRefundEligibilityLocal(order: Order): RefundEligibilityResult {
    const result: RefundEligibilityResult = {
      eligible: false,
      reason: "",
      autoApprove: false,
    };

    // 检查订单状态
    if (order.orderStatus !== "completed") {
      result.reason = "只有已完成的订单才能申请退款";
      return result;
    }

    // 检查支付方式（只支持微信支付）
    if (order.paymentMethod !== "WECHAT") {
      result.reason = "当前只支持微信支付订单的退款";
      return result;
    }

    // 检查时间限制（15天内）
    const orderTime = new Date(order.createTime || order.payTime || "").getTime();
    const now = Date.now();
    const daysDiff = (now - orderTime) / (1000 * 60 * 60 * 24);

    if (daysDiff > 15) {
      result.reason = "超过15天退款期限";
      return result;
    }

    // 检查金额（小额订单可自动审核）
    const autoApproveThreshold = 100; // 100元以下自动审核
    result.eligible = true;
    result.autoApprove = (order.totalAmount || 0) <= autoApproveThreshold;

    return result;
  }

  /**
   * 显示退款申请对话框
   */
  static showRefundDialog(order: Order, customerId: string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      uni.showActionSheet({
        itemList: ["申请退款", "查看退款规则"],
        success: (res) => {
          if (res.tapIndex === 0) {
            // 申请退款
            this.navigateToRefundApply(order, customerId)
              .then(() => resolve(true))
              .catch(reject);
          } else if (res.tapIndex === 1) {
            // 查看退款规则
            this.showRefundRules();
            resolve(false);
          }
        },
        fail: () => {
          resolve(false);
        },
      });
    });
  }

  /**
   * 快速申请退款（适用于简单场景）
   */
  static async quickApplyRefund(order: Order, customerId: string, reason: string, description?: string): Promise<{ success: boolean; message: string; data?: any }> {
    try {
      const requestData: RefundApplyParams = {
        orderId: order.id,
        customerId: customerId,
        refundType: "FULL",
        refundReason: reason,
        refundDescription: description,
        refundImages: [],
      };

      const response = await applyRefund(requestData);

      if (response.code === 0) {
        return {
          success: true,
          message: "退款申请提交成功",
          data: response.data,
        };
      } else {
        return {
          success: false,
          message: response.msg || "退款申请失败",
        };
      }
    } catch (error) {
      console.error("快速申请退款失败:", error);
      return {
        success: false,
        message: "网络错误，请重试",
      };
    }
  }

  /**
   * 跳转到退款申请页面
   */
  static async navigateToRefundApply(order: Order, customerId: string): Promise<void> {
    // 先检查退款资格
    const eligibility = await this.checkRefundEligibility(order, customerId);

    if (!eligibility.eligible) {
      uni.showToast({
        title: eligibility.reason || "不符合退款条件",
        icon: "none",
        duration: 2000,
      });
      throw new Error(eligibility.reason);
    }

    // 跳转到退款申请页面
    const orderData = encodeURIComponent(JSON.stringify(order));
    uni.navigateTo({
      url: `/pages/refund/refund-apply?orderData=${orderData}&customerId=${customerId}`,
    });
  }

  /**
   * 显示退款确认对话框
   */
  static showRefundConfirmDialog(order: Order, onConfirm: (reason: string) => void): void {
    // 显示退款原因选择
    uni.showActionSheet({
      itemList: REFUND_REASONS,
      success: (res) => {
        const selectedReason = REFUND_REASONS[res.tapIndex];

        // 确认退款
        uni.showModal({
          title: "确认退款",
          content: `确定要申请退款吗？\n退款原因：${selectedReason}\n退款金额：¥${order.totalAmount}`,
          confirmText: "确认申请",
          cancelText: "取消",
          success: (modalRes) => {
            if (modalRes.confirm) {
              onConfirm(selectedReason);
            }
          },
        });
      },
    });
  }

  /**
   * 显示退款规则
   */
  static showRefundRules(): void {
    const rules = ["1. 只支持微信支付订单的退款", "2. 订单完成后7天内可申请退款", "3. 100元以下订单自动审核通过", "4. 退款将原路退回到您的微信账户", "5. 退款处理时间为1-7个工作日"].join("\n");

    uni.showModal({
      title: "退款规则",
      content: rules,
      showCancel: false,
      confirmText: "知道了",
    });
  }

  /**
   * 跳转到退款详情页面
   */
  static navigateToRefundDetail(refundId: string, customerId: string): void {
    uni.navigateTo({
      url: `/pages/refund/refund-detail?refundId=${refundId}&customerId=${customerId}`,
    });
  }

  /**
   * 跳转到退款列表页面
   */
  static navigateToRefundList(customerId: string): void {
    uni.navigateTo({
      url: `/pages/refund/refund-list?customerId=${customerId}`,
    });
  }

  /**
   * 格式化金额显示
   */
  static formatAmount(amount: number): string {
    return `¥${amount.toFixed(2)}`;
  }

  /**
   * 格式化时间显示
   */
  static formatTime(time: string): string {
    if (!time) return "-";

    const date = new Date(time);
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (days === 0) {
      const hours = Math.floor(diff / (1000 * 60 * 60));
      if (hours === 0) {
        const minutes = Math.floor(diff / (1000 * 60));
        return `${minutes}分钟前`;
      }
      return `${hours}小时前`;
    } else if (days < 7) {
      return `${days}天前`;
    }

    return date.toLocaleDateString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
    });
  }

  /**
   * 获取退款状态配置
   */
  static getStatusConfig(status: string) {
    return REFUND_STATUS_MAP[status as keyof typeof REFUND_STATUS_MAP] || { label: "未知状态", color: "#999" };
  }

  /**
   * 判断是否可以查看退款详情
   */
  static canViewRefundDetail(orderStatus: string): boolean {
    return ["refund_processing", "refund_success", "refund_failed", "refund"].includes(orderStatus);
  }
}

// 导出类和单例
export { RefundManager };
export const refundManager = RefundManager;
