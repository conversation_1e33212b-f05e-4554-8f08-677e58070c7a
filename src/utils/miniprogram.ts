/**
 * @Description: 微信确认收款
 * <AUTHOR>
 * @date 2025/5/7
 * @time 23:25
 */
const getMoney = (item: any) => {
  // 判断平台
  if (wx.canIUse("requestMerchantTransfer")) {
    wx.requestMerchantTransfer({
      mchId: item.mchId, // 商户号
      appId: item.appId, // 商户绑定的AppID（企业号corpid即为此AppID），由微信生成，可在公众号后台查看
      package: item.package, // 对应发起转账接口应答参数中的 package_info（仅当转账单据状态为WAIT_USER_CONFIRM: 待收款用户确认时才返回），用于唤起用户确认收款页面。
      success(res: any) {
        console.log("用户确认收款成功", res);
        uni.showToast({
          title: "收款成功",
          icon: "success",
        });
      },
      fail(res: any) {
        console.error("用户确认收款失败", res);
        uni.showToast({
          title: "收款失败",
          icon: "error",
        });
      },
      complete(res: any) {
        console.log("请求完成", res);
      },
    });
  } else {
    uni.showModal({
      content: "当前微信版本过低，请升级至最新版本后重试",
      showCancel: false,
    });
  }
};

const wechatLogin = (): Promise<any> => {
  return new Promise((resolve) => {
    uni.login({
      success: resolve,
      fail: (err) => {
        console.error("[微信登录失败]", err);
        resolve({}); // 返回空对象，由调用方处理失败
      },
    });
  });
};

export { getMoney, wechatLogin };
