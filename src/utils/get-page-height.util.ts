export const getLayoutHeights = () => {
    // 同步获取胶囊按钮位置和系统信息
    const menuButton = uni.getMenuButtonBoundingClientRect()
    const systemInfo = uni.getSystemInfoSync()

    // 计算导航栏高度（胶囊高度 + 上下安全间距）
    const navHeight = menuButton.height + (menuButton.top - systemInfo.statusBarHeight) * 2
    // 计算顶部总高度（状态栏 + 导航栏）
    const topHeight = navHeight + systemInfo.statusBarHeight

    // 获取页面可用高度（总窗口高度 - 顶部总高度）
    const windowHeight = systemInfo.windowHeight
    const contentHeight = windowHeight - topHeight

    return {
        topHeight,        // 顶部总高度
        contentHeight    // 页面可用高度
    }
}