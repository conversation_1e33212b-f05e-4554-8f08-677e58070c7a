/* eslint-disable */
class DateFormatUtil {
    /**
     * 日期时间格式自定义
     * @param {number|string} value 日期时间值时间戳或时间格式
     * @param {string} format 输出的时间格式
     * @returns {string} 定义好的时间格式
     */
    formatDateTime(value: any, format = 'YYYY-MM-dd'): string {
        const reg = /(YY|YYYY|MM|dd|HH|mm|ss|ww|WW)/g;
        if (!value) {
            return ''
        }
        if (!reg.test(format)) {
            throw new Error(`The defined format ${format} is incorrect,Please enter 'YYYY or YY' for year,MM for month, dd for day,ww or WW for week,HH for hours,mm for minutes,ss for seconds.`)
        }
        if (typeof value === 'string' && value.length === 8) {
            value = [value.substr(0, 4), value.substr(4, 2), value.substr(6, 2)].join('/')
        } else if (!isNaN(Number(value))) {
            value = Number(value);
        }
        const time = new Date(value);
        if (String(time) === 'Invalid Date') {
            throw new Error(`Time format unrecognized by ${value}, Please enter time stamp or legal time format.`);
        }
        const dayMap = ['日', '一', '二', '三', '四', '五', '六'];
        const year = String(time.getFullYear());
        const month = time.getMonth() + 1 > 9 ? String(time.getMonth() + 1) : `0${String(time.getMonth() + 1)}`;
        const date = time.getDate() > 9 ? String(time.getDate()) : `0${String(time.getDate())}`;
        const day = time.getDay();
        const week: any = day !== 0 ? day : '日';
        const hours = time.getHours() > 9 ? String(time.getHours()) : `0${String(time.getHours())}`;
        const minutes = time.getMinutes() > 9 ? String(time.getMinutes()) : `0${time.getMinutes()}`;
        const seconds = time.getSeconds() > 9 ? String(time.getSeconds()) : `0${String(time.getSeconds())}`;
        let str = '';
        if (/YYYY|YY/g.test(format)) {
            format.replace(/YYYY|YY/g, (reg, index, value) => {
                if (reg === 'YY') {
                    str = value.replace('YY', year.slice(2))
                }
                if (reg === 'YYYY') {
                    str = value.replace('YYYY', year)
                }
                return str
            })
        } else {
            str = format
        }
        str = str.replace(/MM/g, month);
        str = str.replace(/dd/g, date);
        str = str.replace(/HH/g, hours);
        str = str.replace(/mm/g, minutes);
        str = str.replace(/ss/g, seconds);
        str = str.replace(/ww/g, week);
        str = str.replace(/WW/g, dayMap[day]);
        return str
    }

    toChinesNum = (num: any) => {
        let changeNum = ["零", "一", "二", "三", "四", "五", "六", "七", "八", "九"];
        let unit = ["", "十", "百", "千", "万"];
        num = parseInt(num);
        let getWan = (temp: any) => {
            let strArr = temp.toString().split("").reverse();
            let newNum = "";
            for (var i = 0; i < strArr.length; i++) {
                newNum = (i == 0 && strArr[i] == 0 ? "" : i > 0 && strArr[i] == 0 && strArr[i - 1] == 0 ? "" : changeNum[strArr[i]] + (strArr[i] == 0 ? unit[0] : unit[i])) + newNum;
            }
            return newNum;
        };
        let overWan = Math.floor(num / 10000);
        let noWan: any = num % 10000;
        if (noWan.toString().length < 4) {
            noWan = "0" + noWan;
        }
        return overWan ? getWan(overWan) + "万" + getWan(noWan) : getWan(num);
    };
}
const dateFormat: DateFormatUtil = new DateFormatUtil();
export { dateFormat };
