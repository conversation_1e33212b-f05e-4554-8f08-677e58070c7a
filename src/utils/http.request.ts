import { Result } from "@/model/domain/Result";
import { HeaderType } from "@/constants/enum/header-type.enum";
import { Code } from "@/constants/enum/code.enum";

const Data = {
  url: "",
  method: "",
  data: {},
};
/**
 * @Desc GET请求
 */
export function get(url: string, query = {}, headerType: string, header?: any): Promise<Result> {
  const fullUrl = urlQueryConvert(url, query);
  Data.url = fullUrl;
  Data.method = "GET";
  Data.data = query;
  return new Promise((resolve, reject) => {
    uni.request({
      url: fullUrl,
      method: "GET",
      dataType: "json",
      header: {
        ...creatHeader(headerType, header || {}),
      },
      timeout: 60000,
      complete: (res: any) => {
        console.log("GET服务响应：", res);
        console.log("");
        console.log("--------------START------------");
        console.log("请求地址：", fullUrl);
        console.log("请求参数：", query);
        console.log("返回数据：", JSON.stringify(res.data));
        console.log("---------------END-------------");
        console.log("接口调用结束：", res);
        const respond = handlerRespond(res);

        if (respond.code == Code.OK.code) {
          resolve(respond);
        } else if (respond.code == Code.ERROR.code) {
          resolve(respond);
        } else {
          reject(respond);
        }
      },
    });
  });
}
/**
 * @Desc POST请求
 */
export function post(url: string, data = {}, headerType: string, header?: any, query?: any): Promise<Result> {
  Data.method = "POST";
  Data.data = data;
  if (query) {
    url = urlQueryConvert(url, query);
  }
  return new Promise((resolve, reject) => {
    uni.request({
      url: url,
      method: "POST",
      dataType: "json",
      data: data,
      header: {
        ...creatHeader(headerType, header || {}),
      },
      timeout: 60000,
      complete: (res: any) => {
        console.log("POST服务响应：", res);
        console.log("");
        console.log("--------------START------------");
        console.log("请求地址：", url);
        console.log("请求参数：", data);
        console.log("返回数据：", JSON.stringify(res.data));
        console.log("---------------END-------------");
        console.log("");
        const respond = handlerRespond(res);

        if (respond.code == Code.OK.code) {
          resolve(respond);
        } else if (respond.code == Code.ERROR.code) {
          resolve(respond);
        } else {
          reject(respond);
        }
      },
    });
  });
}

/**
 * @Desc 处理接口响应返回
 */
const handlerRespond = function (result: any): Result {
  let response;
  if (result.statusCode == 200) {
    let data: any = result.data;
    if (typeof data == "string") {
      data = JSON.parse(data);
    }
    response = new Result(data);

    // if (!isLogin()) {
    //   response = Result.error("登录失效");
    // }
  } else {
    console.error(result);
    response = Result.error(result.msg);
  }
  return response;
};
/**
 * @Desc 创建请求头
 */
const creatHeader = (headerType: string, header: any) => {
  const reqHeader: any = {
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  switch (headerType) {
    case HeaderType.AUTH.code:
      // eslint-disable-next-line no-case-declarations
      const tokenObj = uni.getStorageSync("token");
      // eslint-disable-next-line no-case-declarations
      const token = tokenObj?.value || "";
      reqHeader["Token"] = token;
      break;
    case HeaderType.FORM.code:
      // eslint-disable-next-line no-case-declarations
      const tokenObjForm = uni.getStorageSync("token");
      // eslint-disable-next-line no-case-declarations
      const tokenForm = tokenObjForm?.value || "";
      reqHeader["Content-Type"] = "multipart/form-data";
      reqHeader["Token"] = tokenForm;
      break;
  }
  return {
    ...header,
    ...reqHeader,
  };
};
/**
 * @desc url请求参数组装
 */
export const urlQueryConvert = (url: string, query: any) => {
  let connectiveSymbol = "";
  if (url.indexOf("?") !== -1) {
    connectiveSymbol = "&";
  } else {
    connectiveSymbol = "?";
  }
  if (query) {
    Object.keys(query).forEach((key, idx) => {
      const val = query[key];
      if (idx === 0) {
        if (val != null && val !== "null" && val !== "undefined") {
          url += connectiveSymbol + key + "=" + val;
        } else {
          url += connectiveSymbol + key + "=";
        }
      } else {
        if (val != null && val !== "null" && val !== "undefined") {
          url += "&" + key + "=" + val;
        } else {
          url += "&" + key + "=";
        }
      }
    });
  }
  return url;
};
/**
 * @Desc  登录状态
 */
const isLogin = (): boolean => {
  return !!uni.getStorageSync("token");
};
