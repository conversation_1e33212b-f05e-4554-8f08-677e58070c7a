/**
 * 递归解析嵌套 JSON 字符串，并将非布尔/null值转为字符串
 * @param value - 待解析的值（可以是字符串或对象）
 * @returns 解析并转换后的对象
 */
export function deepParseJSON(value: any): any {
  // 1. 如果是字符串，尝试解析为 JSON
  if (typeof value === "string") {
    try {
      const parsed = JSON.parse(value);
      return deepParseJSON(parsed); // 递归处理解析后的值
    } catch {
      // 若无法解析，则作为原始字符串返回
      return value;
    }
  }

  // 2. 如果是对象，递归处理每个属性
  if (value && typeof value === "object") {
    if (Array.isArray(value)) {
      return value.map((item) => deepParseJSON(item));
    } else {
      const result: Record<string, any> = {};
      for (const key in value) {
        if (Object.prototype.hasOwnProperty.call(value, key)) {
          result[key] = deepParseJSON(value[key]);
        }
      }
      return result;
    }
  }

  // 3. 保留布尔值和 null，其他值转为字符串
  if (typeof value === "boolean" || value === null) {
    return value;
  }

  return String(value);
}
