// 缓存前缀
const COURSE_CACHE_KEY = "daily_course_cache_";

// 存储缓存（次日4点过期）
export function setDailyCache(key: string, data: any) {
  const now = new Date();
  const today4AM = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 4);
  // 如果当前时间早于今天4点，则过期时间为今天4点，否则为明天4点
  const expireTime = now < today4AM ? today4AM : new Date(today4AM.getTime() + 86400000);
  uni.setStorageSync(COURSE_CACHE_KEY + key, {
    data: data,
    expireTime: expireTime.getTime(),
  });
}

// 读取缓存（自动判断过期 并清除过期缓存）
export function getDailyCache(key: string): any | null {
  const cachedData = uni.getStorageSync(COURSE_CACHE_KEY + key);
  if (!cachedData) return null;

  const now = new Date().getTime();
  if (now < cachedData.expireTime) {
    return cachedData.data;
  } else {
    uni.removeStorageSync(COURSE_CACHE_KEY + key); // 清除过期缓存
    return null;
  }
}
