/**
 * 微信支付工具类
 * 参考文档：https://pay.weixin.qq.com/doc/v3/merchant/4012791898
 */

import { Code } from "../constants/enum/code.enum";

// 微信支付参数接口
export interface WechatPayParams {
  appId: string;
  timeStamp: string;
  nonceStr: string;
  package: string;
  signType: string;
  paySign: string;
}

// 支付结果接口
export interface PaymentResult {
  success: boolean;
  errMsg?: string;
  errCode?: number;
}

// API响应接口
interface ApiResponse {
  code: number;
  msg?: string;
  data?: any;
}

/**
 * 检查微信支付环境
 * @returns 是否支持微信支付
 */
export const checkWechatPayEnvironment = (): boolean => {
  try {
    // #ifdef MP-WEIXIN
    // 检查是否在微信小程序环境中
    if (typeof uni !== "undefined" && uni.requestPayment) {
      console.log("微信支付 - 环境检查通过：微信小程序环境");
      return true;
    }
    // #endif

    // #ifndef MP-WEIXIN
    console.warn("微信支付 - 环境检查失败：当前环境不支持微信支付");
    return false;
    // #endif

    console.warn("微信支付 - 环境检查失败：uni.requestPayment 方法不可用");
    return false;
  } catch (error) {
    console.error("微信支付 - 环境检查异常:", error);
    return false;
  }
};

export default {
  checkWechatPayEnvironment,
};
