const BEHAVIOR_TYPE = "BehaviorTypeEnum";

const BehaviorTypeEnum = {
  // CUSTOMER_REGISTRATION: { code: 1, name: '客户注册' },
  CAMP_ENROLLMENT: { code: 2, name: "训练营营期报名" },
  CAMP_VIDEO_COURSE_LEARNING: { code: 3, name: "训练营视频课学习" },
  POST_COURSE_ANSWERING: { code: 4, name: "课后答题" },
  RECEIVE_RED_PACKET: { code: 5, name: "领取奖励" },
  ADD_WECHAT_ENTERPRISE: { code: 6, name: "添加企微" },
  DELETE_WECHAT_ENTERPRISE: { code: 7, name: "删除企微" },
  JOIN_GROUP_CHAT: { code: 8, name: "加入群聊" },
  EXIT_GROUP_CHAT: { code: 9, name: "退出群聊" },
};

const BEHAVIOR_TYPE_OPTIONS = [
  // { label: BehaviorTypeEnum.CUSTOMER_REGISTRATION.name, value: BehaviorTypeEnum.CUSTOMER_REGISTRATION.code },
  { label: BehaviorTypeEnum.CAMP_ENROLLMENT.name, value: BehaviorTypeEnum.CAMP_ENROLLMENT.code },
  { label: BehaviorTypeEnum.CAMP_VIDEO_COURSE_LEARNING.name, value: BehaviorTypeEnum.CAMP_VIDEO_COURSE_LEARNING.code },
  { label: BehaviorTypeEnum.POST_COURSE_ANSWERING.name, value: BehaviorTypeEnum.POST_COURSE_ANSWERING.code },
  { label: BehaviorTypeEnum.RECEIVE_RED_PACKET.name, value: BehaviorTypeEnum.RECEIVE_RED_PACKET.code },
  { label: BehaviorTypeEnum.ADD_WECHAT_ENTERPRISE.name, value: BehaviorTypeEnum.ADD_WECHAT_ENTERPRISE.code },
  { label: BehaviorTypeEnum.DELETE_WECHAT_ENTERPRISE.name, value: BehaviorTypeEnum.DELETE_WECHAT_ENTERPRISE.code },
  { label: BehaviorTypeEnum.JOIN_GROUP_CHAT.name, value: BehaviorTypeEnum.JOIN_GROUP_CHAT.code },
  { label: BehaviorTypeEnum.EXIT_GROUP_CHAT.name, value: BehaviorTypeEnum.EXIT_GROUP_CHAT.code },
];
export { BEHAVIOR_TYPE, BehaviorTypeEnum, BEHAVIOR_TYPE_OPTIONS };
