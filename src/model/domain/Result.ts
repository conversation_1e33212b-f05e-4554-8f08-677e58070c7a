import { Code } from "@/constants/enum/code.enum";

export class Result<T = any> {
  code?: number;
  msg?: string;
  data?: T;
  [key: string]: any;
  constructor(
    options: {
      code?: number;
      msg?: string;
      data?: T;
      [key: string]: any;
    } = {}
  ) {
    this.code = options.code;
    this.msg = options.msg || "";
    this.data = options.data;
    for (const key in options) {
      this[key] = options[key];
    }
  }

  static ok(data?: any, msg = Code.OK.name) {
    return new Result({ code: Code.OK.code, msg: msg, data: data });
  }

  static error(msg = Code.ERROR.name) {
    return new Result({ code: Code.ERROR.code, msg: msg });
  }
}
