<template>
  <div class="register-page">
    <!-- 顶部导航栏 -->
    <van-nav-bar title="微客申请" left-arrow @click-left="goHome" />

    <!-- 顶部 Banner -->
    <div class="banner">
      <img src="/static/banner.png" alt="推广得奖励" />
    </div>

    <!-- 申请表单 -->
    <div class="form-container">
      <h3>请填写申请资料</h3>
      <van-cell-group inset>
        <van-field :value="form.name" @change="nameChange" label="姓名" placeholder="请输入姓名" required />
        <van-field :value="form.accountId" @change="accountIdChange" label="登录账号" placeholder="请设置登录账号" required maxlength="16" @input="onAccountInput" :error-message="accountIdError" :error="!!accountIdError" />
        <van-field :value="form.phone" label="手机" placeholder="点击获取手机号" required readonly clickable>
          <template #button>
            <van-button open-type="getPhoneNumber" @getphonenumber="onGetPhoneNumber" plain type="primary" size="small">获取手机号</van-button>
          </template>
        </van-field>
        <van-field :value="form.company" label="所属公司" required readonly />
        <van-field :value="form.salesGroup" is-link readonly required label="销售组" placeholder="请选择销售组" @click-input="show = true" />
      </van-cell-group>
      <van-popup :show="show" round position="bottom">
        <van-picker show-toolbar title="标题" :columns="salesGroups" @cancel="onClose" @confirm="onConfirmGroup" />
      </van-popup>
    </div>

    <!-- 提交按钮 -->
    <div class="submit-btn">
      <van-button type="primary" block round @click="submit">立即申请</van-button>
    </div>
    <privacy-modal></privacy-modal>
    <van-popup :show="showProfilePopup" round position="center">
      <div class="popup-content">
        <h3>请授权微信信息</h3>
        <input type="nickname" class="weui-input" placeholder="请输入昵称" v-model="form.nickName" />
        <button class="avatar-wrapper" open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
          <img :src="form.avatarUrl || '/static/default-avatar.png'" alt="头像" />
        </button>
        <van-button class="close-btn" @click="closePopup" type="primary" block round>确认授权</van-button>
      </div>
    </van-popup>
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import { Component } from "vue-property-decorator";
import NavigationBar from "@/components/common/NavigationBar.vue";
import { getCompanyGroup } from "@/api/company.api.ts";
import { getAppId, handleLogin } from "@/api/auth.api";
import { getUserPhone } from "@/api/user.api";
import { updateWxUser } from "@/api/system-user.api";
import { deleteCustomer } from "@/api/customer.api";
import { Code } from "@/constants/enum/code.enum";
import PrivacyModal from "@/components/privacy-modal/PrivacyModal.vue";
import { AccountType } from "@/constants/enum/account-type.enum";

@Component({
  name: "register",
  components: { NavigationBar, PrivacyModal },
})
export default class Register extends Vue {
  form = {
    name: "",
    accountId: "",
    phone: "",
    company: "",
    companyId: null,
    salesGroup: "",
    salesGroupId: null,
    columnId: null,
    headquartersId: null,
    avatarUrl: "",
    nickName: "",
  };
  salesGroups: any[] = [];
  show = false;
  showProfilePopup = false;

  accountIdError = "";

  /**
   * 检查用户类型
   * @returns boolean 如果是客户类型返回true，否则返回false
   */
  checkUserType(): boolean {
    const userInfo = uni.getStorageSync("userInfo");
    if (userInfo && userInfo.userType === AccountType.CORPORATION.code) {
      // 如果是客户类型，显示提示弹窗
      uni.showModal({
        title: "提示",
        content: "您的账号是客户账号，需要删除后才能继续操作，请谨慎操作",
        confirmText: "确认",
        cancelText: "取消",
        success: (res) => {
          if (res.confirm) {
            // 调用删除客户接口
            this.deleteCustomerAccount();
          } else {
            // 用户取消，返回上一页
            uni.navigateBack();
          }
        },
      });
      return true;
    }
    return false;
  }

  onLoad(options: any) {
    console.log("扫码进入参数：", options);
    // 存储扫码参数
    if (options.companyId) {
      this.form.companyId = options.companyId;
      this.form.headquartersId = options.headquartersId;
      this.form.columnId = options.columnId;
    }
  }

  onShow() {
    if (!uni.getStorageSync("wxUserInfo")) {
      // 获取微信用户信息
      uni.navigateTo({ url: "/pages/auth/web-auth" });
    } else {
      // 获取 token 并检查有效期
      const item = uni.getStorageSync("token");
      if (item && item.expires > Date.now()) {
        // token 有效
        if (!this.checkUserType()) {
          this.getCompanyGroup();
        }
      } else {
        handleLogin(AccountType.PERSONAL.code).then(() => {
          if (!this.checkUserType()) {
            this.getCompanyGroup();
          }
        });
      }
    }
  }

  /**
   * 删除客户账号
   */
  deleteCustomerAccount() {
    const userInfo = uni.getStorageSync("userInfo");

    // 确保有必要的参数
    if (!userInfo || !userInfo.id || !userInfo.unionId) {
      uni.showToast({
        title: "用户信息不完整，无法删除账号",
        icon: "none",
        duration: 2000,
      });
      return;
    }

    // 保存当前的扫码参数，以便在重新登录后恢复
    const scanParams = {
      companyId: this.form.companyId,
      headquartersId: this.form.headquartersId,
      columnId: this.form.columnId,
    };

    // 调用删除接口，传递正确的参数
    deleteCustomer({
      customerId: userInfo.id,
      unionId: userInfo.unionId,
    })
      .then((res) => {
        const { code, msg } = res;
        if (code === Code.OK.code) {
          // 删除成功，清除缓存
          uni.removeStorageSync("userInfo");
          uni.removeStorageSync("token");

          // 重新加载页面
          uni.showToast({
            title: "账号已删除，请重新注册",
            icon: "none",
            duration: 2000,
          });

          // 重新登录并加载页面
          handleLogin(AccountType.PERSONAL.code).then(() => {
            // 恢复扫码参数
            this.form.companyId = scanParams.companyId;
            this.form.headquartersId = scanParams.headquartersId;
            this.form.columnId = scanParams.columnId;

            this.getCompanyGroup();
          });
        } else {
          uni.showToast({
            title: msg || "删除账号失败",
            icon: "none",
            duration: 2000,
          });
        }
      })
      .catch((error) => {
        console.error("删除账号失败:", error);
        uni.showToast({
          title: "删除账号失败，请重试",
          icon: "none",
          duration: 2000,
        });
      });
  }
  onChooseAvatar(e: any) {
    const { avatarUrl } = e.detail;
    this.form.avatarUrl = avatarUrl;
  }
  closePopup() {
    if (!this.form.nickName || !this.form.avatarUrl) {
      uni.showToast({
        title: "请完成昵称和头像授权",
        icon: "none",
      });
      return;
    }
    this.showProfilePopup = false;
  }
  nameChange(e: any) {
    this.form.name = e.detail;
  }
  accountIdChange(e: any) {
    this.form.accountId = e.detail;
  }
  onAccountInput(e: any) {
    const raw = e.detail;
    const cleaned = raw.replace(/[^a-zA-Z0-9]/g, "");
    this.form.accountId = cleaned.slice(0, 16);
    if (/[^a-zA-Z0-9]/.test(raw)) {
      this.accountIdError = "账号只能包含字母和数字";
    } else {
      this.accountIdError = "";
    }
  }
  /**
   * 获取手机号
   * @param e
   */
  async onGetPhoneNumber(e) {
    try {
      const { encryptedData, iv } = e.detail;
      if (!encryptedData || !iv) {
        uni.showToast("获取手机号失败");
        return;
      }

      // 2. 获取微信临时登录凭证 (code)
      const loginRes = await this.getWechatLoginCode();
      if (!loginRes.code) {
        uni.showToast({ title: "微信登录失败，请重试", icon: "none" });
        return;
      }

      // 3. 调用后端接口解密手机号
      await getUserPhone({
        encryptedData: encodeURIComponent(encryptedData),
        iv: encodeURIComponent(iv),
        vxCode: loginRes.code,
        appId: getAppId(),
      }).then((resp) => {
        console.log(resp);
        const { code, data, msg } = resp;
        if (code == Code.OK.code) {
          this.form.phone = data;
        } else {
          uni.showToast({ title: msg, icon: "error" });
        }
      });
    } catch (error) {
      console.error("获取手机号失败:", error);
      uni.showToast("获取手机号失败");
    }
  }
  // 封装微信登录逻辑
  getWechatLoginCode() {
    return new Promise((resolve) => {
      uni.login({
        success: resolve,
        fail: (err) => {
          console.error("[微信登录失败]", err);
          resolve({}); // 返回空对象，由调用方处理失败
        },
      });
    });
  }

  /**
   * 根据公司获取销售组
   */
  getCompanyGroup() {
    const companyId = this.form.companyId || 51001;
    getCompanyGroup({ id: companyId })
      .then((response) => {
        this.form.company = response.data.name;
        this.form.companyId = response.data.id;
        const groups = response.data.salesGroups || [];
        this.salesGroups = groups.map((item: any) => ({
          text: item.name,
          value: item.id,
        }));
      })
      .catch((error) => {
        console.error("错误:", error);
      });
  }
  onClose() {
    this.show = false;
  }
  onConfirmGroup(pickerValue: any) {
    this.form.salesGroup = pickerValue.detail.value.text;
    this.form.salesGroupId = pickerValue.detail.value.value;
    this.show = false;
  }
  submit() {
    console.log(this.form);
    // 检查表单数据是否完整
    if (!this.form || !this.form.name || !this.form.accountId || !this.form.phone || !this.form.companyId || !this.form.salesGroupId) {
      wx.showToast({
        title: "请填写完整信息",
        icon: "none",
        duration: 2000,
      });
      return;
    }
    this.updateWxUser();
  }

  async updateWxUser() {
    try {
      const userInfo = uni.getStorageSync("userInfo");
      // 这里添加调用后端API的代码
      await updateWxUser({
        name: this.form.name,
        accountId: this.form.accountId,
        userId: userInfo.id,
        phone: this.form.phone,
        headquartersId: this.form.headquartersId,
        columnId: this.form.columnId,
        companyId: this.form.companyId,
        salesGroupId: this.form.salesGroupId,
      }).then((res) => {
        console.log(res);
        const { code, data, msg } = res;
        if (code === Code.OK.code) {
          data.userType = "1";
          uni.setStorageSync("userInfo", data);
          wx.showToast({
            title: "提交成功,等待管理员审核！",
            icon: "success",
            duration: 3000,
          });
          // 跳转首页
          uni.reLaunch({
            url: `/pages/index/index`,
          });
        } else {
          uni.showModal({
            title: "提示",
            confirmText: "前往首页",
            cancelText: "重新填写",
            content: msg || "提交失败",
            success: function (res) {
              if (res.confirm) {
                // 跳转首页
                uni.reLaunch({
                  url: `/pages/index/index`,
                });
              } else if (res.cancel) {
                console.log("用户点击取消");
              }
            },
          });
        }
      });
    } catch (error) {
      console.error("提交失败:", error);
      wx.showToast({
        title: error.message || "提交失败",
        icon: "none",
        duration: 2000,
      });
    }
  }
}
</script>

<style scoped>
.register-page {
  background-color: #fff;
  min-height: 100vh;
  text-align: center;
}
.banner img {
  width: 100%;
  display: block;
}
.form-container {
  padding: 20px;
}
.submit-btn {
  padding: 20px;
}
::v-deep .van-cell {
  margin-bottom: 5px;
}

.popup-content {
  text-align: center;
  padding: 30px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  max-width: 90%;
  margin: auto;
}

.popup-content h3 {
  font-size: 18px;
  margin-bottom: 20px;
  color: #333;
}

.popup-content input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 6px;
  margin-bottom: 20px;
  font-size: 16px;
}

.avatar-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  width: 80px;
  height: 80px;
  margin: 0 auto;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid #007aff;
  background-color: #f2f2f2;
}

.avatar-wrapper img {
  width: 100%;
  height: 100%;
  object-fit: cover; /* Ensure the image retains its aspect ratio */
}

.popup-content .van-button {
  background-color: #007aff;
  border-radius: 25px;
  padding: 12px;
  font-size: 16px;
}

.popup-content .van-button:active {
  background-color: #005bb5;
}

.close-btn {
  margin-top: 20px;
}
</style>
