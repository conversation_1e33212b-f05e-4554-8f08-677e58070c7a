<template>
  <view class="home-page" :style="{ height: contentHeight  + 'px', paddingTop: topHeight + 'px' }">
    <navigation-bar style="width: 100%; z-index: 99" text="首页" :showText="true" />
    <!-- 搜索栏 -->
    <view class="search-section">
      <view class="search-bar" @click="goSearch">
        <van-icon name="search" size="16" color="#999" />
        <text class="search-placeholder">搜索商品</text>
      </view>
      <view class="message-icon" @click="goNotifications">
        <van-icon name="chat-o" size="20" color="#333" />
        <view v-if="unreadCount > 0" class="message-badge">{{ unreadCount > 99 ? "99+" : unreadCount }}</view>
      </view>
    </view>

    <scroll-view scroll-y class="home-content" refresher-enabled :refresher-triggered="refreshing" @refresherrefresh="onRefresh">
      <!-- 轮播图 -->
      <view class="banner-section">
        <swiper class="banner-swiper" indicator-dots indicator-color="rgba(255,255,255,0.5)" indicator-active-color="#fff" autoplay circular interval="3000">
          <swiper-item v-for="(banner, index) in banners" :key="index" @click="goBannerDetail(banner)">
            <image :src="banner.image" class="banner-image" mode="aspectFill" />
          </swiper-item>
        </swiper>
      </view>

      <!-- 通知栏 -->
      <view class="notice-section" v-if="notices.length > 0">
        <van-icon name="volume-o" size="16" color="#ff4444" />
        <swiper class="notice-swiper" vertical autoplay circular interval="2000">
          <swiper-item v-for="(notice, index) in notices" :key="index" @click="goNoticeDetail(notice)">
            <text class="notice-text">{{ notice.content }}</text>
          </swiper-item>
        </swiper>
      </view>

      <!-- 快捷入口 -->
      <view class="quick-nav-section">
        <view class="nav-grid">
          <view v-for="(nav, index) in quickNavs" :key="index" class="nav-item" @click="goNavPage(nav)">
            <view class="nav-icon" :style="{ backgroundColor: nav.bgColor }">
              <van-icon :name="nav.icon" size="24" color="#fff" />
            </view>
            <text class="nav-text">{{ nav.name }}</text>
          </view>
        </view>
      </view>

      <!-- 推荐商品 -->
      <view class="recommend-section">
        <view class="section-header">
          <text class="section-title">为你推荐</text>
          <text class="more-btn" @click="goCategory">更多 ></text>
        </view>

        <!-- 加载状态 -->
        <view v-if="loading" class="loading-section">
          <van-loading size="24px" />
          <text class="loading-text">加载中...</text>
        </view>

        <!-- 商品网格 -->
        <view v-else class="product-grid">
          <view v-for="(product, index) in recommendProducts" :key="index" class="product-item" @click="goProductDetail(product)">
            <image :src="product.image" class="product-image" mode="aspectFill" />
            <view class="product-info">
              <text class="product-name">{{ product.name }}</text>
              <text class="product-desc">{{ product.description }}</text>
              <view class="product-bottom">
                <text class="product-price">¥{{ product.price }}</text>
              </view>
              <view class="product-tags" v-if="product.tags && product.tags.length > 0">
                <text v-for="(tag, tagIndex) in product.tags.slice(0, 2)" :key="tagIndex" class="product-tag">
                  {{ tag }}
                </text>
                <text v-if="product.tags.length > 2" class="product-tag-more">+{{ product.tags.length - 2 }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 底部占位 -->
      <view class="bottom-placeholder"></view>
    </scroll-view>
  </view>
</template>

<script lang="ts">
import Vue from "vue";
import { Component } from "vue-property-decorator";
import NavigationBar from "@/components/common/NavigationBar.vue";
import { getRecommendProducts } from "@/api/mall.api";
import { Code } from "@/constants/enum/code.enum";
import {getIndexData} from "@/api/app.api";
import {getLayoutHeights} from "@/utils/get-page-height.util";

interface Banner {
  id: string;
  image: string;
  title: string;
  url: string;
}

interface Notice {
  id: string;
  content: string;
  url: string;
}

interface QuickNav {
  id: string;
  name: string;
  icon: string;
  bgColor: string;
  url: string;
}

interface Product {
  id: string;
  name: string;
  description?: string;
  subtitle?: string;
  price: number;
  image: string;
  tags?: string[];
  seckillPrice?: number;
  originalPrice?: number;
  progress?: number;
}

@Component({
  name: "index",
  components: {
    NavigationBar,
  },
})
export default class Index extends Vue {
  unreadCount = 3;
  loading = false;
  refreshing = false;

  topHeight = 88;
  contentHeight = 0;

  banners: Banner[] = [];

  notices: Notice[] = [];

  quickNavs: QuickNav[] = [
    {
      id: "1",
      name: "新鲜水果",
      icon: "gift-o",
      bgColor: "#ff6b6b",
      url: "/pages/category/category?type=fruit",
    },
    {
      id: "2",
      name: "蔬菜蛋品",
      icon: "flower-o",
      bgColor: "#4ecdc4",
      url: "/pages/category/category?type=vegetable",
    },
    {
      id: "3",
      name: "肉禽水产",
      icon: "shopping-cart-o",
      bgColor: "#45b7d1",
      url: "/pages/category/category?type=meat",
    },
    {
      id: "4",
      name: "热门推荐",
      icon: "hot-o",
      bgColor: "#f9ca24",
      url: "/pages/category/category?type=hot",
    },
    {
      id: "5",
      name: "优惠券",
      icon: "coupon-o",
      bgColor: "#6c5ce7",
      url: "/pages/coupons/coupons",
    },
    {
      id: "6",
      name: "积分商城",
      icon: "gold-coin-o",
      bgColor: "#fd79a8",
      url: "/pages/points/points",
    },
    {
      id: "7",
      name: "会员中心",
      icon: "diamond-o",
      bgColor: "#fdcb6e",
      url: "/pages/membership/membership",
    },
    {
      id: "8",
      name: "客服中心",
      icon: "service-o",
      bgColor: "#74b9ff",
      url: "/pages/service/service",
    },
  ];

  recommendProducts: Product[] = [
    {
      id: "rec_1",
      name: "新鲜红富士苹果",
      description: "脆甜多汁，营养丰富",
      subtitle: "当季新鲜",
      price: 12.8,
      image: "/static/images/test.jpeg",
      tags: ["新鲜", "当季"],
    },
    {
      id: "rec_2",
      name: "优质香蕉",
      description: "进口香蕉，香甜可口",
      subtitle: "进口精选",
      price: 8.5,
      image: "/static/images/test.jpeg",
      tags: ["进口", "精选"],
    },
    {
      id: "rec_3",
      name: "精选橙子",
      description: "维C丰富，酸甜适中",
      subtitle: "维C之王",
      price: 15.2,
      image: "/static/images/test.jpeg",
      tags: ["维C", "健康"],
    },
    {
      id: "rec_4",
      name: "新鲜草莓",
      description: "当季草莓，鲜美多汁",
      subtitle: "当季新鲜",
      price: 28.8,
      image: "/static/images/test.jpeg",
      tags: ["当季", "新鲜"],
    },
    {
      id: "rec_5",
      name: "有机牛奶",
      description: "纯天然有机，营养丰富",
      subtitle: "有机认证",
      price: 25.0,
      image: "/static/images/test.jpeg",
      tags: ["有机", "营养"],
    },
    {
      id: "rec_6",
      name: "进口车厘子",
      description: "进口车厘子，甜度极佳",
      subtitle: "进口精品",
      price: 89.9,
      image: "/static/images/test.jpeg",
      tags: ["进口", "精品"],
    },
  ];

  async onLoad(): Promise<void> {
    // 确保有默认推荐商品数据
    if (this.recommendProducts.length === 0) {
      this.initDefaultRecommendProducts();
    }

    // await this.loadHomeData();
  }

  onShow() {
    const { topHeight, contentHeight } = getLayoutHeights();
    this.topHeight = topHeight;
    this.contentHeight = contentHeight;
    // 页面显示时刷新数据
    this.loadHomeData();
  }

  // 下拉刷新
  async onRefresh() {
    this.refreshing = true;
    try {
      await this.loadHomeData();
      uni.showToast({
        title: "刷新成功",
        icon: "success",
        duration: 1500,
      });
    } catch (error) {
      uni.showToast({
        title: "刷新失败",
        icon: "none",
        duration: 1500,
      });
    } finally {
      this.refreshing = false;
    }
  }

  async loadHomeData() {
    try {
      // 并行加载首页数据
      await Promise.all([this.loadRecommendProducts(), this.loadBanners(), this.loadNotices()]);
    } catch (error) {
      console.error("加载首页数据失败:", error);
    }
  }

  // 加载轮播图数据
  async loadBanners() {
    try {
      const response = await getIndexData({index: 1});
      if (response.code === Code.OK.code) {
        if (response.data && Array.isArray(response.data) && response.data.length > 0) {
          this.banners = response.data.map(item => {
            return {
              id: String(item.id),
              image: item.imageUrl,
              title: item.title,
              url: item.linkUrl,
            };
          });
        }
      } else {
        console.error("加载轮播图失败:", response.msg);
        uni.showToast({
          title: response.msg || "加载轮播图失败",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("加载轮播图失败:", error);
    }
  }

  // 加载通知数据
  async loadNotices() {
    try {
      const response = await getIndexData({index: 2});
      if (response.code === Code.OK.code) {
        if (response.data && Array.isArray(response.data) && response.data.length > 0) {
          this.notices = response.data.map(item => {
            return {
              id: String(item.id),
              content: item.messageText,
              url: item.linkUrl,
            };
          })
        }
      } else {
        console.error("加载通知失败:", response.msg);
        uni.showToast({
          title: response.msg || "加载通知失败",
          icon: "none",
        });
      }
      console.log("加载通知数据");
    } catch (error) {
      console.error("加载通知失败:", error);
    }
  }

  // 加载推荐商品
  async loadRecommendProducts() {
    this.loading = true;
    try {
      const response = await getRecommendProducts(6); // 首页显示6个推荐商品

      if (response.code === Code.OK.code) {
        const products = response.data || [];

        if (Array.isArray(products) && products.length > 0) {
          // 转换数据格式
          this.recommendProducts = products.map((product: any) => ({
            id: String(product.id),
            name: product.name,
            description: product.subtitle || product.description || "",
            subtitle: product.subtitle,
            price: product.price,
            image: product.image,
            tags: this.parseProductTags(product.tags),
          }));

        } else {
          this.initDefaultRecommendProducts();
        }
      } else {
        this.initDefaultRecommendProducts();
        uni.showToast({
          title: response.msg || "获取推荐商品失败",
          icon: "none",
        });
      }
    } catch (error) {
      this.initDefaultRecommendProducts();
      uni.showToast({
        title: "网络错误，显示默认商品",
        icon: "none",
      });
    } finally {
      this.loading = false;
    }
  }

  // 初始化默认推荐商品（当API失败时使用）
  initDefaultRecommendProducts() {
    // 如果当前已有数据，不重复设置
    if (this.recommendProducts && this.recommendProducts.length > 0) {
      return;
    }

    this.recommendProducts = [
      {
        id: "rec_1",
        name: "新鲜红富士苹果",
        description: "脆甜多汁，营养丰富",
        subtitle: "当季新鲜",
        price: 12.8,
        image: "/static/images/test.jpeg",
        tags: ["新鲜", "当季"],
      },
      {
        id: "rec_2",
        name: "优质香蕉",
        description: "进口香蕉，香甜可口",
        subtitle: "进口精选",
        price: 8.5,
        image: "/static/images/test.jpeg",
        tags: ["进口", "精选"],
      },
      {
        id: "rec_3",
        name: "精选橙子",
        description: "维C丰富，酸甜适中",
        subtitle: "维C之王",
        price: 15.2,
        image: "/static/images/test.jpeg",
        tags: ["维C", "健康"],
      },
      {
        id: "rec_4",
        name: "新鲜草莓",
        description: "当季草莓，鲜美多汁",
        subtitle: "当季新鲜",
        price: 28.8,
        image: "/static/images/test.jpeg",
        tags: ["当季", "新鲜"],
      },
      {
        id: "rec_5",
        name: "有机牛奶",
        description: "纯天然有机，营养丰富",
        subtitle: "有机认证",
        price: 25.0,
        image: "/static/images/test.jpeg",
        tags: ["有机", "营养"],
      },
      {
        id: "rec_6",
        name: "进口车厘子",
        description: "进口车厘子，甜度极佳",
        subtitle: "进口精品",
        price: 89.9,
        image: "/static/images/test.jpeg",
        tags: ["进口", "精品"],
      },
    ];
  }

  // 解析商品标签
  parseProductTags(tags: string | null): string[] {
    if (!tags) return [];
    try {
      let parsedTags: string[] = [];

      if (typeof tags === "string") {
        // 尝试JSON解析
        try {
          parsedTags = JSON.parse(tags);
        } catch {
          // 如果JSON解析失败，尝试按逗号分割
          parsedTags = tags
            .split(",")
            .map((tag) => tag.trim())
            .filter((tag) => tag.length > 0);
        }
      } else if (Array.isArray(tags)) {
        parsedTags = tags;
      }

      // 过滤和清理标签
      return parsedTags
        .filter((tag) => tag && typeof tag === "string" && tag.trim().length > 0)
        .map((tag) => tag.trim())
        .slice(0, 3); // 最多显示3个标签
    } catch (error) {
      console.warn("解析商品标签失败:", tags, error);
      return [];
    }
  }

  // 页面跳转方法
  goSearch() {
    uni.navigateTo({
      url: "/pages/product/product-list",
    });
  }

  goNotifications() {
    uni.navigateTo({
      url: "/pages/notifications/notifications",
    });
  }

  goBannerDetail(banner: Banner) {
    if (banner.id) {
      uni.navigateTo({
        url: `/pages/product/product-detail?id=${banner.id}`,
      });
    }
  }

  goNoticeDetail(notice: Notice) {
    if (notice.url) {
      uni.navigateTo({
        url: notice.url,
      });
    }
  }

  goNavPage(nav: QuickNav) {
    if (!nav.url) return;

    // 处理特殊页面跳转
    switch (nav.id) {
      case "8": // 客服中心
        this.goCustomerService();
        break;
      default:
        // 处理正常页面跳转
        if (nav.url.includes("/pages/category/category") || nav.url.includes("/pages/index/index")) {
          uni.switchTab({
            url: nav.url.split("?")[0],
          });
        } else {
          // 检查页面是否存在
          const existingPages = ["/pages/coupons/coupons", "/pages/points/points", "/pages/membership/membership"];

          const targetPage = nav.url.split("?")[0];
          if (existingPages.includes(targetPage)) {
            uni.navigateTo({
              url: nav.url,
            });
          } else {
            uni.showToast({
              title: `${nav.name}功能开发中`,
              icon: "none",
            });
          }
        }
        break;
    }
  }

  // 客服中心功能
  goCustomerService() {
    uni.showModal({
      title: "联系客服",
      content: "请选择联系方式",
      showCancel: true,
      cancelText: "取消",
      confirmText: "拨打电话",
      success: (res) => {
        if (res.confirm) {
          uni.makePhoneCall({
            phoneNumber: "400-1805185",
            fail: () => {
              uni.showToast({
                title: "拨打失败，请手动拨打400-1805185",
                icon: "none",
                duration: 3000,
              });
            },
          });
        }
      },
    });
  }

  goCategory() {
    uni.switchTab({
      url: "/pages/category/category",
    });
  }

  goProductDetail(product: Product) {
    uni.navigateTo({
      url: `/pages/product/product-detail?id=${product.id}`,
    });
  }
}
</script>

<style lang="scss" scoped>
.home-page {
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

/* 搜索栏样式 */
.search-section {
  background-color: #fff;
  padding: 10px 15px;
  display: flex;
  align-items: center;
  gap: 15px;
}

.search-bar {
  flex: 1;
  background-color: #f5f5f5;
  border-radius: 20px;
  padding: 8px 15px;
  display: flex;
  align-items: center;
  gap: 8px;
  height: 40px;
  box-sizing: border-box;
}

.search-placeholder {
  font-size: 14px;
  color: #999;
}

.message-icon {
  position: relative;
  padding: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.message-badge {
  position: absolute;
  top: 0;
  right: 0;
  background-color: #ff4444;
  color: #fff;
  font-size: 8px;
  padding: 2px 6px;
  border-radius: 10px;
  height: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

.home-content {
  flex: 1;
  overflow-y: auto;
}

/* 轮播图样式 */
.banner-section {
  margin: 10px 15px 20px;
  border-radius: 12px;
  overflow: hidden;
  height: 180px;
}

.banner-swiper {
  height: 100%;
}

.banner-image {
  width: 100%;
  height: 100%;
}

/* 通知栏样式 */
.notice-section {
  background-color: #fff;
  margin: 0 15px 20px;
  border-radius: 8px;
  padding: 10px 15px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.notice-swiper {
  flex: 1;
  height: 20px;
}

.notice-text {
  font-size: 12px;
  color: #666;
  line-height: 20px;
}

/* 快捷入口样式 */
.quick-nav-section {
  background-color: #fff;
  margin: 0 15px 20px;
  border-radius: 12px;
  padding: 20px 15px;
}

.nav-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.nav-item {
  width: 22%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
  transition: transform 0.2s ease;
}

.nav-item:active {
  transform: scale(0.95);
}

.nav-icon {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.nav-text {
  font-size: 12px;
  color: #333;
  text-align: center;
}

/* 推荐商品样式 */
.recommend-section {
  background-color: #fff;
  margin: 0 15px 20px;
  border-radius: 12px;
  padding: 20px 15px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-title {
  font-size: 16px;
  color: #333;
  font-weight: bold;
}

.more-btn {
  font-size: 12px;
  color: #999;
}

.loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
}

.loading-text {
  font-size: 14px;
  color: #999;
  margin-top: 10px;
}

.product-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  padding: 0 5px;
}

.product-item {
  width: calc(50% - 5px);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  background-color: #fff;
  display: flex;
  flex-direction: column;
  height: 244px; /* 调整总高度，适应新的布局 */
}

.product-image {
  width: 100%;
  height: 120px;
  flex-shrink: 0;
}

.product-info {
  padding: 8px;
  display: flex;
  flex-direction: column;
  height: 114px; /* 调整高度，适应新的文字高度 */
  justify-content: space-between;
}

.product-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  margin-bottom: 4px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.3;
  height: 38px; /* 调整高度，确保2行文字完整显示 */
  flex: 0 0 38px;
}

.product-desc {
  font-size: 12px;
  color: #999;
  margin-bottom: 6px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.3;
  height: 16px; /* 调整高度，确保文字完整显示 */
  flex: 0 0 16px;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-bottom {
  flex: 0 0 auto;
  margin-bottom: 2px;
}

.product-price {
  font-size: 16px;
  color: #ff4444;
  font-weight: bold;
  line-height: 1.2;
  margin-bottom: 4px;
}

.product-tags {
  display: flex;
  flex-wrap: nowrap; /* 不换行，避免挤压 */
  gap: 4px;
  align-items: center;
  overflow: hidden; /* 隐藏超出部分 */
  height: 18px; /* 固定标签区域高度 */
}

.product-tag {
  background-color: #f0f8ff;
  color: #007aff;
  font-size: 10px;
  padding: 1px 5px;
  border-radius: 8px;
  white-space: nowrap;
  max-width: 50px;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.1;
  border: 1px solid #e6f3ff;
  flex-shrink: 0; /* 防止被压缩 */
  height: 16px;
  display: flex;
  align-items: center;
}

.product-tag-more {
  background-color: #f5f5f5;
  color: #999;
  font-size: 10px;
  padding: 1px 5px;
  border-radius: 8px;
  white-space: nowrap;
  line-height: 1.1;
  border: 1px solid #e5e5e5;
  flex-shrink: 0; /* 防止被压缩 */
  height: 16px;
  display: flex;
  align-items: center;
}

.bottom-placeholder {
  height: 20px;
}
</style>
