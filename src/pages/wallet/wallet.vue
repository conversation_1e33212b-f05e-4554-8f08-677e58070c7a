<template>
  <view class="wallet-page" :style="{ height: contentHeight  + 'px', paddingTop: topHeight + 'px' }">
    <navigation-bar style="width: 100%; z-index: 99" :showIcon="true" :showText="true" text="我的余额"></navigation-bar>

    <view class="wallet-content">
      <!-- 余额卡片 -->
      <view class="balance-card">
        <view class="card-bg">
          <view class="card-overlay"></view>
        </view>

        <view class="card-content">
          <view class="balance-info">
            <text class="balance-label">账户余额（元）</text>
            <text class="balance-amount">{{ userBalance.toFixed(2) }}</text>
            <text class="balance-tip">余额可用于购买商品和服务</text>
          </view>

          <!--          <view class="balance-actions">-->
          <!--            <view class="action-btn" @click="recharge">-->
          <!--              <van-icon name="plus" size="20" color="#fff" />-->
          <!--              <text class="action-text">充值</text>-->
          <!--            </view>-->
          <!--            <view class="action-btn" @click="withdraw">-->
          <!--              <van-icon name="minus" size="20" color="#fff" />-->
          <!--              <text class="action-text">提现</text>-->
          <!--            </view>-->
          <!--          </view>          <view class="balance-actions">-->
          <!--            <view class="action-btn" @click="recharge">-->
          <!--              <van-icon name="plus" size="20" color="#fff" />-->
          <!--              <text class="action-text">充值</text>-->
          <!--            </view>-->
          <!--            <view class="action-btn" @click="withdraw">-->
          <!--              <van-icon name="minus" size="20" color="#fff" />-->
          <!--              <text class="action-text">提现</text>-->
          <!--            </view>-->
          <!--          </view>-->
        </view>
      </view>

      <!-- 快捷金额 -->
      <view class="quick-amounts">
        <view class="section-title">
          <text class="title-text">快捷充值</text>
        </view>

        <view class="amounts-grid">
          <view v-for="(amount, index) in quickAmounts" :key="index" class="amount-item" :class="selectedAmount === amount ? 'selected' : ''" @click="selectAmount(amount)">
            <text class="amount-value">{{ amount }}</text>
            <text class="amount-unit">元</text>
          </view>
        </view>

        <view class="custom-amount">
          <input v-model="customAmount" class="amount-input" placeholder="输入其他金额" type="number" @input="onCustomAmountInput" />
        </view>

        <view class="recharge-btn">
          <van-button type="primary" block @click="confirmRecharge"> 立即充值 {{ selectedAmount || customAmount || 0 }}元 </van-button>
        </view>
      </view>

      <!-- 交易记录 -->
      <view class="transaction-section">
        <view class="section-title">
          <text class="title-text">交易记录</text>
          <text class="more-btn" @click="goTransactionHistory">查看全部 ></text>
        </view>

        <view class="transaction-list">
          <view v-for="(record, index) in transactionRecords" :key="index" class="transaction-item">
            <view class="transaction-info">
              <van-icon :name="record.icon" size="20" :color="record.iconColor" />
              <view class="transaction-details">
                <text class="transaction-title">{{ record.title }}</text>
                <text class="transaction-time">{{ record.time }}</text>
              </view>
            </view>

            <view class="transaction-amount">
              <text class="amount-text" :class="record.type"> {{ record.type === "income" ? "+" : "-" }}{{ record.amount }} </text>
              <text class="amount-status" :class="record.status">{{ record.statusText }}</text>
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <view v-if="transactionRecords.length === 0" class="empty-state">
          <van-empty description="暂无交易记录" />
        </view>
      </view>
    </view>

    <!-- 充值确认弹窗 -->
    <van-popup :show="showRechargePopup" position="bottom" round @close="showRechargePopup = false">
      <view class="recharge-popup">
        <view class="popup-header">
          <text class="popup-title">确认充值</text>
          <van-icon name="cross" size="20" @click="showRechargePopup = false" />
        </view>

        <view class="popup-content">
          <view class="recharge-info">
            <text class="recharge-label">充值金额</text>
            <text class="recharge-amount">¥{{ rechargeAmount }}</text>
          </view>

          <view class="payment-methods">
            <text class="payment-title">选择支付方式</text>
            <view v-for="(method, index) in paymentMethods" :key="index" class="payment-item" :class="selectedPayment === method.id ? 'selected' : ''" @click="selectPayment(method.id)">
              <view class="payment-info">
                <van-icon :name="method.icon" size="24" :color="method.color" />
                <text class="payment-name">{{ method.name }}</text>
              </view>
              <van-icon name="success" size="16" color="#007aff" v-if="selectedPayment === method.id" />
            </view>
          </view>
        </view>

        <view class="popup-footer">
          <van-button type="default" @click="showRechargePopup = false">取消</van-button>
          <van-button type="primary" @click="processPayment">确认支付</van-button>
        </view>
      </view>
    </van-popup>
  </view>
</template>

<script lang="ts">
import Vue from "vue";
import { Component } from "vue-property-decorator";
import NavigationBar from "@/components/common/NavigationBar.vue";
import { getWalletInfo, rechargeWallet, getTransactionHistory } from "@/api/mall-extended.api";
import { authManager } from "@/utils/auth";
import { Code } from "@/constants/enum/code.enum";
import {getLayoutHeights} from "@/utils/get-page-height.util";

interface TransactionRecord {
  id: string;
  title: string;
  time: string;
  amount: number;
  type: "income" | "expense";
  status: "success" | "pending" | "failed";
  statusText: string;
  icon: string;
  iconColor: string;
}

interface PaymentMethod {
  id: string;
  name: string;
  icon: string;
  color: string;
}

@Component({
  name: "wallet",
  components: { NavigationBar },
})
export default class Wallet extends Vue {
  userBalance = 0;
  selectedAmount = 0;
  customAmount = "";
  showRechargePopup = false;
  rechargeAmount = 0;
  selectedPayment = "wechat";
  customerId = "";
  loading = false;
  transactionLoading = false;
  currentPage = 1;
  pageSize = 20;
  hasMore = true;

  topHeight = 88;
  contentHeight = 0;

  quickAmounts = [50, 100, 200, 500, 1000, 2000];

  paymentMethods: PaymentMethod[] = [
    {
      id: "wechat",
      name: "微信支付",
      icon: "wechat",
      color: "#07c160",
    },
    {
      id: "alipay",
      name: "支付宝",
      icon: "alipay",
      color: "#1677ff",
    },
    {
      id: "bank",
      name: "银行卡",
      icon: "credit-pay",
      color: "#ff6b35",
    },
  ];

  transactionRecords: TransactionRecord[] = [];

  async onLoad() {
    // 获取用户ID
    const userId = await this.getUserId();
    if (userId) {
      this.customerId = userId;
      await this.loadWalletInfo();
      await this.loadTransactionHistory();
    } else {
      // 用户未登录，跳转到首页
      uni.switchTab({
        url: '/pages/index/index'
      });
    }
  }

  onShow() {
    const { topHeight, contentHeight } = getLayoutHeights();
    this.topHeight = topHeight;
    this.contentHeight = contentHeight;
  }

  async getUserId(): Promise<string | null> {
    return await authManager.getUserId(true);
  }

  // 加载钱包信息
  async loadWalletInfo() {
    if (!this.customerId) return;

    this.loading = true;
    try {
      console.log("钱包页面 - 开始加载钱包信息");
      const response = await getWalletInfo(this.customerId);
      console.log("钱包页面 - 钱包信息API响应:", response);

      if (response.code === Code.OK.code) {
        const walletInfo = response.data || {};
        this.userBalance = walletInfo.balance || 0;
        console.log("钱包页面 - 更新余额:", this.userBalance);
      } else {
        console.error("钱包页面 - 获取钱包信息失败:", response.msg);
        uni.showToast({
          title: response.msg || "获取钱包信息失败",
          icon: "none"
        });
      }
    } catch (error) {
      console.error("钱包页面 - 加载钱包信息异常:", error);
      uni.showToast({
        title: "网络错误，请重试",
        icon: "none"
      });
    } finally {
      this.loading = false;
    }
  }

  // 加载交易记录
  async loadTransactionHistory() {
    if (!this.customerId) return;

    this.transactionLoading = true;
    try {
      console.log("钱包页面 - 开始加载交易记录");
      const response = await getTransactionHistory({
        customerId: this.customerId,
        pageNum: this.currentPage,
        pageSize: this.pageSize
      });
      console.log("钱包页面 - 交易记录API响应:", response);

      if (response.code === Code.OK.code) {
        const transactions = response.data?.records || response.data || [];
        console.log("钱包页面 - 交易记录数据:", transactions);

        // 转换数据格式
        const convertedTransactions = transactions.map((item: any) => ({
          id: String(item.id),
          title: item.description || item.title || "交易记录",
          time: this.formatDateTime(item.transactionTime || item.createdAt),
          amount: Math.abs(item.amount || 0),
          type: (item.amount || 0) >= 0 ? "income" : "expense",
          status: this.mapTransactionStatus(item.status),
          statusText: this.getStatusText(item.status),
          icon: this.getTransactionIcon(item.type),
          iconColor: this.getIconColor(item.type, item.status),
        }));

        if (this.currentPage === 1) {
          this.transactionRecords = convertedTransactions;
        } else {
          this.transactionRecords.push(...convertedTransactions);
        }

        // 检查是否还有更多数据
        this.hasMore = transactions.length === this.pageSize;

        console.log("钱包页面 - 转换后的交易记录:", this.transactionRecords);
      } else {
        console.error("钱包页面 - 获取交易记录失败:", response.msg);
        if (this.currentPage === 1) {
          uni.showToast({
            title: response.msg || "获取交易记录失败",
            icon: "none"
          });
        }
      }
    } catch (error) {
      console.error("钱包页面 - 加载交易记录异常:", error);
      if (this.currentPage === 1) {
        uni.showToast({
          title: "网络错误，请重试",
          icon: "none"
        });
      }
    } finally {
      this.transactionLoading = false;
    }
  }

  selectAmount(amount: number) {
    this.selectedAmount = amount;
    this.customAmount = "";
  }

  onCustomAmountInput() {
    this.selectedAmount = 0;
  }

  recharge() {
    this.confirmRecharge();
  }

  withdraw() {
    uni.showModal({
      title: "提现功能",
      content: "提现功能正在开发中，敬请期待",
      showCancel: false,
    });
  }

  confirmRecharge() {
    const amount = this.selectedAmount || parseFloat(this.customAmount) || 0;

    if (amount <= 0) {
      uni.showToast({ title: "请选择或输入充值金额", icon: "none" });
      return;
    }

    if (amount > 10000) {
      uni.showToast({ title: "单次充值金额不能超过10000元", icon: "none" });
      return;
    }

    this.rechargeAmount = amount;
    this.showRechargePopup = true;
  }

  selectPayment(paymentId: string) {
    this.selectedPayment = paymentId;
  }

  async processPayment() {
    if (!this.customerId) {
      uni.showToast({ title: "用户信息异常，请重新登录", icon: "none" });
      return;
    }

    if (this.rechargeAmount <= 0) {
      uni.showToast({ title: "请选择充值金额", icon: "none" });
      return;
    }

    uni.showLoading({ title: "支付中..." });

    try {
      console.log("钱包页面 - 开始充值:", {
        customerId: this.customerId,
        amount: this.rechargeAmount,
        paymentMethod: this.selectedPayment
      });

      const response = await rechargeWallet({
        customerId: this.customerId,
        amount: this.rechargeAmount,
        paymentMethod: this.selectedPayment,
        description: `账户充值 ¥${this.rechargeAmount}`
      });

      console.log("钱包页面 - 充值API响应:", response);

      if (response.code === Code.OK.code) {
        // 充值成功，重新加载钱包信息和交易记录
        await this.loadWalletInfo();
        await this.loadTransactionHistory();

        // 关闭弹窗
        this.showRechargePopup = false;

        // 重置选择
        this.selectedAmount = 0;
        this.customAmount = "";

        uni.showToast({ title: "充值成功", icon: "success" });
      } else {
        uni.showToast({
          title: response.msg || "充值失败，请重试",
          icon: "none"
        });
      }
    } catch (error) {
      console.error("钱包页面 - 充值异常:", error);
      uni.showToast({
        title: "网络错误，请重试",
        icon: "none"
      });
    } finally {
      uni.hideLoading();
    }
  }

  // 格式化日期时间
  formatDateTime(dateStr: string): string {
    if (!dateStr) return "";
    const date = new Date(dateStr);
    return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
  }

  // 映射交易状态
  mapTransactionStatus(status: string): "success" | "pending" | "failed" {
    switch (status) {
      case "SUCCESS":
      case "COMPLETED":
        return "success";
      case "PENDING":
      case "PROCESSING":
        return "pending";
      case "FAILED":
      case "CANCELLED":
        return "failed";
      default:
        return "success";
    }
  }

  // 获取状态文本
  getStatusText(status: string): string {
    switch (status) {
      case "SUCCESS":
      case "COMPLETED":
        return "交易成功";
      case "PENDING":
      case "PROCESSING":
        return "处理中";
      case "FAILED":
        return "交易失败";
      case "CANCELLED":
        return "已取消";
      default:
        return "交易成功";
    }
  }

  // 获取交易图标
  getTransactionIcon(type: string): string {
    switch (type) {
      case "RECHARGE":
        return "plus";
      case "PAYMENT":
        return "shopping-cart-o";
      case "REFUND":
        return "arrow-left";
      case "WITHDRAW":
        return "minus";
      default:
        return "exchange";
    }
  }

  // 获取图标颜色
  getIconColor(type: string, status: string): string {
    if (status === "FAILED" || status === "CANCELLED") {
      return "#ff4444";
    }
    if (status === "PENDING" || status === "PROCESSING") {
      return "#fa8c16";
    }

    switch (type) {
      case "RECHARGE":
      case "REFUND":
        return "#52c41a";
      case "PAYMENT":
      case "WITHDRAW":
        return "#ff4444";
      default:
        return "#1890ff";
    }
  }

  goTransactionHistory() {
    uni.showToast({ title: "交易记录详情页面开发中", icon: "none" });
  }
}
</script>

<style lang="scss" scoped>
.wallet-page {
  background-color: #f5f5f5;
}

.wallet-content {
  padding: 15px;
}

/* 余额卡片样式 */
.balance-card {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  margin-bottom: 20px;
  height: 180px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
}

.card-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
}

.card-content {
  position: relative;
  z-index: 2;
  padding: 25px;
  height: 100%;
  flex-direction: column;
  justify-content: space-between;
  color: #fff;
}

.balance-info {
  text-align: center;
}

.balance-label {
  font-size: 14px;
  opacity: 0.9;
  margin-bottom: 8px;
  display: block;
}

.balance-amount {
  font-size: 36px;
  font-weight: bold;
  margin-bottom: 5px;
  display: block;
}

.balance-tip {
  font-size: 12px;
  opacity: 0.8;
}

.balance-actions {
  display: flex;
  justify-content: center;
  gap: 40px;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.action-text {
  font-size: 12px;
  color: #fff;
}

/* 通用区块样式 */
.quick-amounts,
.transaction-section {
  background-color: #fff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 15px;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.title-text {
  font-size: 18px;
  color: #333;
  font-weight: bold;
}

.more-btn {
  font-size: 14px;
  color: #007aff;
}

/* 快捷金额样式 */
.amounts-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 15px;
}

.amount-item {
  width: calc(42.33% - 8px);
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 12px;
  text-align: center;
  transition: all 0.3s ease;

  &.selected {
    border-color: #007aff;
    background-color: #f0f8ff;
  }
}

.amount-value {
  font-size: 16px;
  color: #333;
  font-weight: bold;
  margin-right: 2px;
}

.amount-unit {
  font-size: 12px;
  color: #666;
}

.custom-amount {
  margin-bottom: 20px;
}

.amount-input {
  width: 100%;
  height: 44px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 0 15px;
  font-size: 16px;

  &::placeholder {
    color: #999;
  }
}

/* 交易记录样式 */
.transaction-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.transaction-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.transaction-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.transaction-details {
  margin-left: 12px;
}

.transaction-title {
  font-size: 14px;
  color: #333;
  margin-bottom: 3px;
  display: block;
}

.transaction-time {
  font-size: 12px;
  color: #999;
}

.transaction-amount {
  text-align: right;
}

.amount-text {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 3px;
  display: block;

  &.income {
    color: #52c41a;
  }

  &.expense {
    color: #ff4444;
  }
}

.amount-status {
  font-size: 12px;

  &.success {
    color: #52c41a;
  }

  &.pending {
    color: #fa8c16;
  }

  &.failed {
    color: #ff4444;
  }
}

.empty-state {
  padding: 50px 20px;
  text-align: center;
}

/* 充值弹窗样式 */
.recharge-popup {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.popup-title {
  font-size: 18px;
  color: #333;
  font-weight: bold;
}

.popup-content {
  margin-bottom: 30px;
}

.recharge-info {
  text-align: center;
  margin-bottom: 25px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.recharge-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  display: block;
}

.recharge-amount {
  font-size: 24px;
  color: #333;
  font-weight: bold;
}

.payment-methods {
  margin-top: 20px;
}

.payment-title {
  font-size: 16px;
  color: #333;
  font-weight: bold;
  margin-bottom: 15px;
  display: block;
}

.payment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  margin-bottom: 10px;
  transition: all 0.3s ease;

  &.selected {
    border-color: #007aff;
    background-color: #f0f8ff;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.payment-info {
  display: flex;
  align-items: center;
}

.payment-name {
  font-size: 14px;
  color: #333;
  margin-left: 10px;
}

.popup-footer {
  display: flex;
  gap: 15px;

  .van-button {
    flex: 1;
  }
}
</style>
