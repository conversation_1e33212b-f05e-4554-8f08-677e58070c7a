<template>
  <view class="favorites-page" :style="{ height: contentHeight  + 'px', paddingTop: topHeight + 'px' }">
    <navigation-bar style="width: 100%; z-index: 99" :showIcon="true" :showText="true" text="我的收藏"></navigation-bar>

    <view class="favorites-content">
      <!-- 收藏分类 -->
      <view class="category-tabs">
        <view v-for="(tab, index) in categoryTabs" :key="index" class="tab-item" :class="activeTab === index ? 'active' : ''" @click="switchTab(index)">
          <text class="tab-text">{{ tab.name }}</text>
          <view v-if="tab.count > 0" class="tab-badge">{{ tab.count }}</view>
        </view>
      </view>

      <!-- 加载状态 -->
      <view v-if="loading" class="loading-state">
        <van-loading type="spinner" color="#007aff" />
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 收藏列表 -->
      <view class="favorites-list" v-else>
        <view v-for="(item, index) in currentFavorites" :key="index" class="favorite-item" @click="goItemDetail(item)">
          <image :src="item.image" class="item-image" mode="aspectFill" />
          <view class="item-info">
            <text class="item-name">{{ item.name }}</text>
            <text class="item-desc">{{ item.description }}</text>
            <view class="item-bottom">
              <text class="item-price">¥{{ item.price }}</text>
              <text class="item-time">{{ item.favoriteTime }}</text>
            </view>
          </view>
          <view class="item-actions">
            <van-icon name="delete-o" size="20" color="#999" @click.stop="removeFavorite(item, index)" />
          </view>
        </view>

        <!-- 空状态 -->
        <view v-if="!loading && currentFavorites.length === 0" class="empty-state">
          <van-empty description="暂无收藏" />
          <van-button type="primary" @click="goShopping">去逛逛</van-button>
        </view>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-actions" v-if="currentFavorites.length > 0">
      <view class="select-all">
        <van-checkbox :value="allSelected" @change="toggleAllSelect"> 全选 </van-checkbox>
      </view>
      <view class="action-buttons">
        <van-button type="default" :disabled="selectedItems.length === 0" @click="batchRemove"> 删除({{ selectedItems.length }}) </van-button>
        <van-button type="primary" :disabled="selectedItems.length === 0" @click="addToCart"> 加入购物车({{ selectedItems.length }}) </van-button>
      </view>
    </view>
  </view>
</template>

<script lang="ts">
import Vue from "vue";
import { Component } from "vue-property-decorator";
import NavigationBar from "@/components/common/NavigationBar.vue";
import { getFavoriteList, removeFavorite, batchRemoveFavorites, getFavoriteCount } from "@/api/mall.api";
import { FavoriteProduct } from "@/types/mall.types";
import { Code } from "@/constants/enum/code.enum";
import { authManager } from "../../utils/auth";
import {getLayoutHeights} from "@/utils/get-page-height.util";

interface FavoriteItem {
  id: string;
  name: string;
  description: string;
  price: number;
  image: string;
  favoriteTime: string;
  type: string;
  selected: boolean;
}

@Component({
  name: "favorites",
  components: { NavigationBar },
})
export default class Favorites extends Vue {
  activeTab = 0;
  loading = false;
  customerId = "";
  topHeight = 88;
  contentHeight = 0;

  categoryTabs = [
    { name: "全部", count: 0 },
    { name: "商品", count: 0 },
    { name: "店铺", count: 0 },
  ];

  allFavorites: FavoriteItem[] = [];

  // 获取用户ID（静默获取，不弹窗）
  async getUserId(): Promise<string | null> {
    return await authManager.getUserId(false);
  }

  // 加载收藏列表
  async loadFavoriteList() {
    if (this.customerId === "") {
      return;
    }

    this.loading = true;
    try {
      const response = await getFavoriteList(this.customerId, 1, 100);
      if (response.code === Code.OK.code) {
        const favoriteProducts: FavoriteProduct[] = response.data.records || [];

        // 转换数据格式
        this.allFavorites = favoriteProducts.map((product) => ({
          id: product.id,
          name: product.name,
          description: product.subtitle || product.description || "",
          price: product.price,
          image: product.image,
          favoriteTime: this.formatTime(product.favoriteTime),
          type: "product",
          selected: false,
        }));

        this.updateTabCounts();
      } else {
        uni.showToast({
          title: response.msg || "加载收藏列表失败",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("加载收藏列表失败:", error);
      uni.showToast({
        title: "网络错误，请重试",
        icon: "none",
      });
    } finally {
      this.loading = false;
    }
  }

  // 格式化时间
  formatTime(timeStr: string): string {
    try {
      const time = new Date(timeStr);
      const now = new Date();
      const diff = now.getTime() - time.getTime();

      const days = Math.floor(diff / (1000 * 60 * 60 * 24));
      const hours = Math.floor(diff / (1000 * 60 * 60));
      const minutes = Math.floor(diff / (1000 * 60));

      if (days > 0) {
        return `${days}天前`;
      } else if (hours > 0) {
        return `${hours}小时前`;
      } else if (minutes > 0) {
        return `${minutes}分钟前`;
      } else {
        return "刚刚";
      }
    } catch (error) {
      return timeStr;
    }
  }

  get currentFavorites() {
    if (this.activeTab === 0) {
      return this.allFavorites;
    } else if (this.activeTab === 1) {
      return this.allFavorites.filter((item) => item.type === "product");
    } else {
      return this.allFavorites.filter((item) => item.type === "shop");
    }
  }

  get selectedItems() {
    return this.currentFavorites.filter((item) => item.selected);
  }

  get allSelected() {
    return this.currentFavorites.length > 0 && this.currentFavorites.every((item) => item.selected);
  }

  async onLoad() {
    // 获取用户ID
    const userId = await this.getUserId();
    if (userId) {
      this.customerId = userId;
      await this.loadFavoriteList();
    } else {
      // 未登录，显示登录提示并跳转到用户中心
      uni.showToast({
        title: "请先登录查看收藏",
        icon: "none",
      });
      setTimeout(() => {
        uni.switchTab({
          url: '/pages/user-center/user-center'
        });
      }, 1500);
    }
  }

  onShow() {
    const { topHeight, contentHeight } = getLayoutHeights();
    this.topHeight = topHeight;
    this.contentHeight = contentHeight;
  }

  switchTab(index: number) {
    this.activeTab = index;
    // 切换tab时清除选择状态
    this.allFavorites.forEach((item) => (item.selected = false));
  }

  updateTabCounts() {
    this.categoryTabs[0].count = this.allFavorites.length;
    this.categoryTabs[1].count = this.allFavorites.filter((item) => item.type === "product").length;
    this.categoryTabs[2].count = this.allFavorites.filter((item) => item.type === "shop").length;
  }

  toggleAllSelect() {
    const newSelectState = !this.allSelected;
    this.currentFavorites.forEach((item) => {
      item.selected = newSelectState;
    });
  }

  goItemDetail(item: FavoriteItem) {
    if (item.type === "product") {
      uni.navigateTo({
        url: `/pages/product/product-detail?id=${item.id}`,
      });
    } else {
      uni.showToast({ title: "店铺详情功能开发中", icon: "none" });
    }
  }

  async removeFavorite(item: FavoriteItem, index: number) {
    uni.showModal({
      title: "取消收藏",
      content: "确定要取消收藏这个商品吗？",
      success: async (res) => {
        if (res.confirm) {
          try {
            const response = await removeFavorite(this.customerId, item.id);
            if (response.code === Code.OK.code) {
              uni.showToast({ title: "已取消收藏", icon: "success" });
              await this.loadFavoriteList(); // 重新加载收藏列表
            } else {
              uni.showToast({
                title: response.msg || "取消收藏失败",
                icon: "none",
              });
            }
          } catch (error) {
            console.error("取消收藏失败:", error);
            uni.showToast({
              title: "网络错误，请重试",
              icon: "none",
            });
          }
        }
      },
    });
  }

  async batchRemove() {
    if (this.selectedItems.length === 0) return;

    uni.showModal({
      title: "批量删除",
      content: `确定要删除选中的${this.selectedItems.length}个收藏吗？`,
      success: async (res) => {
        if (res.confirm) {
          try {
            const selectedIds = this.selectedItems.map((item) => item.id);
            const response = await batchRemoveFavorites(this.customerId, selectedIds);
            if (response.code === Code.OK.code) {
              uni.showToast({ title: "删除成功", icon: "success" });
              await this.loadFavoriteList(); // 重新加载收藏列表
            } else {
              uni.showToast({
                title: response.msg || "批量删除失败",
                icon: "none",
              });
            }
          } catch (error) {
            console.error("批量删除失败:", error);
            uni.showToast({
              title: "网络错误，请重试",
              icon: "none",
            });
          }
        }
      },
    });
  }

  addToCart() {
    if (this.selectedItems.length === 0) return;

    const productItems = this.selectedItems.filter((item) => item.type === "product");
    if (productItems.length === 0) {
      uni.showToast({ title: "请选择商品", icon: "none" });
      return;
    }

    uni.showToast({ title: `已添加${productItems.length}个商品到购物车`, icon: "success" });
    // 清除选择状态
    this.allFavorites.forEach((item) => (item.selected = false));
  }

  goShopping() {
    uni.switchTab({
      url: "/pages/index/index",
    });
  }
}
</script>

<style lang="scss" scoped>
.favorites-page {
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.favorites-content {
  flex: 1;
}

.category-tabs {
  background-color: #fff;
  display: flex;
  padding: 0 10px;
  border-bottom: 1px solid #f0f0f0;
}

.tab-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15px 3px;
  position: relative;
  min-width: 0;

  &.active {
    .tab-text {
      color: #007aff;
      font-weight: bold;
    }

    &::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 24px;
      height: 2px;
      background-color: #007aff;
    }
  }
}

.tab-text {
  font-size: 14px;
  color: #666;
  text-align: center;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.tab-badge {
  position: absolute;
  top: 8px;
  right: 15px;
  background-color: #ff4444;
  color: #fff;
  font-size: 10px;
  padding: 1px 5px;
  border-radius: 8px;
  min-width: 14px;
  height: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  transform: scale(0.9);
}

.favorites-list {
  padding: 0 15px;
  flex: 1;
}

.favorite-item {
  background-color: #fff;
  border-radius: 12px;
  margin-bottom: 15px;
  padding: 15px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.item-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  margin-right: 15px;
}

.item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 80px;
}

.item-name {
  font-size: 16px;
  color: #333;
  font-weight: 500;
  margin-bottom: 5px;
}

.item-desc {
  font-size: 12px;
  color: #999;
  margin-bottom: 10px;
}

.item-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.item-price {
  font-size: 16px;
  color: #ff4444;
  font-weight: bold;
}

.item-time {
  font-size: 12px;
  color: #999;
}

.item-actions {
  margin-left: 15px;
  padding: 10px;
}

.loading-state {
  padding: 50px 20px;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.loading-text {
  font-size: 14px;
  color: #666;
}

.empty-state {
  padding: 50px 20px;
  text-align: center;

  .van-button {
    margin-top: 20px;
    width: 200px;
  }
}

.bottom-actions {
  background-color: #fff;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid #eee;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.06);
}

.select-all {
  flex-shrink: 0;
}

.action-buttons {
  display: flex;
  gap: 15px;

  .van-button {
    min-width: 100px;
  }
}
</style>
