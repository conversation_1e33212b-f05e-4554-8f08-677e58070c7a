<template>
  <view class="footprints-page" :style="{ height: contentHeight  + 'px', paddingTop: topHeight + 'px' }">
    <navigation-bar style="width: 100%; z-index: 99" :showIcon="true" :showText="true" text="我的足迹"></navigation-bar>

    <view class="footprints-content">
      <!-- 统计信息 -->
      <view class="stats-section">
        <view class="stats-item">
          <text class="stats-number">{{ totalFootprints }}</text>
          <text class="stats-label">浏览商品</text>
        </view>
        <view class="stats-item">
          <text class="stats-number">{{ todayFootprints }}</text>
          <text class="stats-label">今日浏览</text>
        </view>
        <view class="stats-item">
          <text class="stats-number">{{ favoriteCount }}</text>
          <text class="stats-label">已收藏</text>
        </view>
      </view>

      <!-- 操作栏 -->
      <view class="action-bar">
        <view class="filter-buttons">
          <text v-for="(filter, index) in timeFilters" :key="index" class="filter-btn" :class="activeFilter === index ? 'active' : ''" @click="switchFilter(index)">
            {{ filter.name }}
          </text>
        </view>
        <text class="clear-btn" @click="clearFootprints">清空</text>
      </view>

      <!-- 足迹列表 -->
      <view class="footprints-list">
        <view v-for="(group, groupIndex) in groupedFootprints" :key="groupIndex" class="date-group">
          <view class="date-header">
            <text class="date-text">{{ group.date }}</text>
            <text class="count-text">共{{ group.items.length }}个商品</text>
          </view>

          <scroll-view class="products-scroll" scroll-x show-scrollbar="false">
            <view class="products-horizontal">
              <view v-for="(item, index) in group.items" :key="index" class="product-item" @click="goProductDetail(item)">
                <image :src="item.image" class="product-image" mode="aspectFill" />
                <view class="product-info">
                  <text class="product-name">{{ item.name }}</text>
                  <text class="product-price">¥{{ item.price }}</text>
                  <text class="visit-time">{{ item.visitTime }}</text>
                </view>
                <view class="product-actions">
                  <van-icon :name="item.isFavorite ? 'star' : 'star-o'" size="16" :color="item.isFavorite ? '#ff4444' : '#999'" @click.stop="toggleFavorite(item)" />
                  <van-icon name="delete-o" size="16" color="#999" @click.stop="removeFootprint(item, groupIndex, index)" />
                </view>
              </view>
            </view>
          </scroll-view>
        </view>

        <!-- 空状态 -->
        <view v-if="groupedFootprints.length === 0" class="empty-state">
          <van-empty description="暂无浏览记录" />
          <van-button type="primary" @click="goShopping">去逛逛</van-button>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts">
import Vue from "vue";
import { Component } from "vue-property-decorator";
import NavigationBar from "@/components/common/NavigationBar.vue";
import { getFootprintList, removeFootprint as removeFootprintAPI, clearFootprints as clearFootprintsAPI } from "@/api/mall-extended.api";
import { authManager } from "@/utils/auth";
import { Code } from "@/constants/enum/code.enum";
import {getLayoutHeights} from "@/utils/get-page-height.util";

interface FootprintItem {
  id: string;
  name: string;
  price: number;
  image: string;
  visitTime: string;
  visitDate: string;
  isFavorite: boolean;
}

interface FootprintGroup {
  date: string;
  items: FootprintItem[];
}

@Component({
  name: "footprints",
  components: { NavigationBar },
})
export default class Footprints extends Vue {
  activeFilter = 0;
  loading = false;
  customerId = "";
  currentPage = 1;
  pageSize = 20;
  hasMore = true;

  topHeight = 88;
  contentHeight = 0;

  timeFilters = [
    { name: "全部", days: 0 },
    { name: "今天", days: 1 },
    { name: "最近3天", days: 3 },
    { name: "最近7天", days: 7 },
  ];

  allFootprints: FootprintItem[] = [];

  get filteredFootprints() {
    if (this.activeFilter === 0) {
      return this.allFootprints;
    }

    const days = this.timeFilters[this.activeFilter].days;
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days + 1);
    const cutoffDateStr = cutoffDate.toISOString().split("T")[0];

    return this.allFootprints.filter((item) => item.visitDate >= cutoffDateStr);
  }

  get groupedFootprints(): FootprintGroup[] {
    const groups: { [key: string]: FootprintItem[] } = {};

    this.filteredFootprints.forEach((item) => {
      const date = this.formatDate(item.visitDate);
      if (!groups[date]) {
        groups[date] = [];
      }
      groups[date].push(item);
    });

    return Object.keys(groups)
      .sort((a, b) => new Date(b).getTime() - new Date(a).getTime())
      .map((date) => ({
        date: this.getDateLabel(date),
        items: groups[date].sort((a, b) => b.visitTime.localeCompare(a.visitTime)),
      }));
  }

  get totalFootprints() {
    return this.allFootprints.length;
  }

  get todayFootprints() {
    const today = new Date().toISOString().split("T")[0];
    return this.allFootprints.filter((item) => item.visitDate === today).length;
  }

  get favoriteCount() {
    return this.allFootprints.filter((item) => item.isFavorite).length;
  }

  switchFilter(index: number) {
    this.activeFilter = index;
  }

  formatDate(dateStr: string) {
    return dateStr;
  }

  getDateLabel(dateStr: string) {
    const today = new Date().toISOString().split("T")[0];
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayStr = yesterday.toISOString().split("T")[0];

    if (dateStr === today) {
      return "今天";
    } else if (dateStr === yesterdayStr) {
      return "昨天";
    } else {
      const date = new Date(dateStr);
      return `${date.getMonth() + 1}月${date.getDate()}日`;
    }
  }

  toggleFavorite(item: FootprintItem) {
    item.isFavorite = !item.isFavorite;
    uni.showToast({
      title: item.isFavorite ? "已添加到收藏" : "已取消收藏",
      icon: "success",
    });
  }

  removeFootprint(item: FootprintItem, groupIndex: number, itemIndex: number) {
    uni.showModal({
      title: "删除记录",
      content: "确定要删除这条浏览记录吗？",
      success: (res) => {
        if (res.confirm) {
          const allIndex = this.allFootprints.findIndex((fp) => fp.id === item.id);
          if (allIndex > -1) {
            this.allFootprints.splice(allIndex, 1);
            uni.showToast({ title: "删除成功", icon: "success" });
          }
        }
      },
    });
  }

  async onLoad() {
    // 获取用户ID
    const userId = await this.getUserId();
    if (userId) {
      this.customerId = userId;
      await this.loadFootprints();
    } else {
      // 用户未登录，跳转到首页
      uni.switchTab({
        url: '/pages/index/index'
      });
    }
  }

  onShow() {
    const { topHeight, contentHeight } = getLayoutHeights();
    this.topHeight = topHeight;
    this.contentHeight = contentHeight;
  }

  async getUserId(): Promise<string | null> {
    return await authManager.getUserId(true);
  }

  // 加载浏览足迹
  async loadFootprints() {
    if (!this.customerId) return;

    this.loading = true;
    try {
      console.log("浏览足迹页面 - 开始加载足迹数据");
      const response = await getFootprintList(this.customerId, this.currentPage, this.pageSize);
      console.log("浏览足迹页面 - API响应:", response);

      if (response.code === Code.OK.code) {
        const footprints = response.data?.records || response.data || [];
        console.log("浏览足迹页面 - 足迹数据:", footprints);

        // 转换数据格式
        const convertedFootprints = footprints.map((item: any) => ({
          id: String(item.productId || item.id),
          name: item.productName || item.name || "未知商品",
          price: item.productPrice || item.price || 0,
          image: item.productImage || item.image || "/static/images/default-product.png",
          visitTime: this.formatTime(item.visitTime || item.createdAt),
          visitDate: this.formatDate(item.visitTime || item.createdAt),
          isFavorite: false, // 默认未收藏，后续可以批量检查
        }));

        if (this.currentPage === 1) {
          this.allFootprints = convertedFootprints;
        } else {
          this.allFootprints.push(...convertedFootprints);
        }

        // 检查是否还有更多数据
        this.hasMore = footprints.length === this.pageSize;

        console.log("浏览足迹页面 - 转换后的足迹:", this.allFootprints);
      } else {
        console.error("浏览足迹页面 - 获取足迹失败:", response.msg);
        if (this.currentPage === 1) {
          uni.showToast({
            title: response.msg || "获取浏览记录失败",
            icon: "none"
          });
        }
      }
    } catch (error) {
      console.error("浏览足迹页面 - 加载足迹异常:", error);
      if (this.currentPage === 1) {
        uni.showToast({
          title: "网络错误，请重试",
          icon: "none"
        });
      }
    } finally {
      this.loading = false;
    }
  }

  async clearFootprints() {
    if (this.allFootprints.length === 0) {
      uni.showToast({ title: "暂无记录可清空", icon: "none" });
      return;
    }

    uni.showModal({
      title: "清空足迹",
      content: "确定要清空所有浏览记录吗？",
      success: async (res) => {
        if (res.confirm) {
          try {
            const response = await clearFootprintsAPI(this.customerId);
            if (response.code === Code.OK.code) {
              this.allFootprints = [];
              uni.showToast({ title: "清空成功", icon: "success" });
            } else {
              uni.showToast({ title: response.msg || "清空失败", icon: "none" });
            }
          } catch (error) {
            console.error("清空足迹失败:", error);
            uni.showToast({ title: "网络错误，请重试", icon: "none" });
          }
        }
      },
    });
  }

  async removeFootprint(item: FootprintItem) {
    uni.showModal({
      title: "删除记录",
      content: `确定要删除"${item.name}"的浏览记录吗？`,
      success: async (res) => {
        if (res.confirm) {
          try {
            const response = await removeFootprintAPI(this.customerId, item.id);
            if (response.code === Code.OK.code) {
              const index = this.allFootprints.findIndex((f) => f.id === item.id);
              if (index > -1) {
                this.allFootprints.splice(index, 1);
                uni.showToast({ title: "删除成功", icon: "success" });
              }
            } else {
              uni.showToast({ title: response.msg || "删除失败", icon: "none" });
            }
          } catch (error) {
            console.error("删除足迹失败:", error);
            uni.showToast({ title: "网络错误，请重试", icon: "none" });
          }
        }
      },
    });
  }

  // 格式化时间
  formatTime(dateStr: string): string {
    if (!dateStr) return "";
    const date = new Date(dateStr);
    return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
  }

  // 格式化日期
  formatDate(dateStr: string): string {
    if (!dateStr) return "";
    const date = new Date(dateStr);
    return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
  }

  goProductDetail(item: FootprintItem) {
    uni.navigateTo({
      url: `/pages/product/product-detail?id=${item.id}`,
    });
  }

  goShopping() {
    uni.switchTab({
      url: "/pages/index/index",
    });
  }
}
</script>

<style lang="scss" scoped>
.footprints-page {
  background-color: #f5f5f5;
}

.footprints-content {
  padding-top: 10px;
}

.stats-section {
  background-color: #fff;
  padding: 20px;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-around;
}

.stats-item {
  text-align: center;
}

.stats-number {
  font-size: 24px;
  color: #333;
  font-weight: bold;
  margin-bottom: 5px;
  display: block;
}

.stats-label {
  font-size: 12px;
  color: #666;
}

.action-bar {
  background-color: #fff;
  padding: 15px 20px;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-buttons {
  display: flex;
  gap: 20px;
}

.filter-btn {
  font-size: 14px;
  color: #666;
  padding: 5px 10px;
  border-radius: 15px;

  &.active {
    color: #007aff;
    background-color: #f0f8ff;
    font-weight: bold;
  }
}

.clear-btn {
  font-size: 14px;
  color: #ff4444;
}

.footprints-list {
  padding: 0 15px;
}

.date-group {
  margin-bottom: 20px;
}

.date-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.date-text {
  font-size: 16px;
  color: #333;
  font-weight: bold;
}

.count-text {
  font-size: 12px;
  color: #999;
}

.products-scroll {
  width: 100%;
  white-space: nowrap;
}

.products-horizontal {
  display: flex;
  gap: 12px;
  padding: 0 5px;
}

.product-item {
  width: 140px;
  flex-shrink: 0;
  background-color: #fff;
  border-radius: 12px;
  padding: 12px;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.product-image {
  width: 100%;
  height: 100px;
  border-radius: 8px;
  margin-bottom: 8px;
}

.product-info {
  margin-bottom: 8px;
}

.product-name {
  font-size: 13px;
  color: #333;
  margin-bottom: 4px;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.3;
}

.product-price {
  font-size: 14px;
  color: #ff4444;
  font-weight: bold;
  margin-bottom: 4px;
  display: block;
}

.visit-time {
  font-size: 11px;
  color: #999;
}

.product-actions {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 4px;
  border-radius: 15px;
}

.empty-state {
  padding: 50px 20px;
  text-align: center;

  .van-button {
    margin-top: 20px;
    width: 200px;
  }
}
</style>
