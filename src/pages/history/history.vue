<template>
  <view class="history-container" :style="{ height: contentHeight  + 'px', paddingTop: topHeight + 'px' }">
    <navigation-bar showText text="观看历史" showIcon />
    <view class="content">
      <!--      <view class="filter-tabs">-->
      <!--        <view class="tab-item" v-for="(tab, index) in tabs" :key="index" :class="{ active: currentTab === index }" @click="switchTab(index)">-->
      <!--          {{ tab }}-->
      <!--        </view>-->
      <!--      </view>-->

      <view class="history-list" v-if="historyList.length > 0">
        <view class="history-item" v-for="(item, index) in historyList" :key="index" @click="goToCourse(item)">
          <image class="course-image" :src="item.image" mode="aspectFill"></image>
          <view class="course-info">
            <view class="course-name">{{ item.name }}</view>
            <view class="course-progress">
              <view class="progress-text">已学习 {{ item.progress }}%</view>
              <view class="progress-bar">
                <view class="progress-inner" :style="{ width: item.progress + '%' }"></view>
              </view>
            </view>
            <view class="watch-info">
              <view class="watch-time">{{ item.lastWatchTime }}</view>
              <view class="continue-btn">继续学习</view>
            </view>
          </view>
        </view>
      </view>

      <view v-else class="empty-history">
        <van-icon name="video-o" size="48px" color="#dcdee0" />
        <view class="empty-text">暂无观看记录，如有疑问请咨询在线客服</view>
        <view class="go-course-btn" @click="goToCourseList">去看看课程</view>
      </view>

      <view class="clear-history" v-if="historyList.length > 0" @click="showClearConfirm"> 清空观看历史 </view>
    </view>
  </view>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import NavigationBar from "@/components/common/NavigationBar.vue";
import {getLayoutHeights} from "@/utils/get-page-height.util";

interface HistoryItem {
  id: string;
  name: string;
  image: string;
  progress: number;
  lastWatchTime: string;
}

@Component({
  components: { NavigationBar },
})
export default class History extends Vue {
  tabs: string[] = ["全部"];
  currentTab = 0;
  historyList: HistoryItem[] = [];

  topHeight = 88;
  contentHeight = 0;

  created() {
    // 模拟获取历史记录数据
    this.fetchHistoryData();
  }

  onShow() {
    const { topHeight, contentHeight } = getLayoutHeights();
    this.topHeight = topHeight;
    this.contentHeight = contentHeight;
  }

  onClickLeft() {
    uni.navigateBack();
  }

  switchTab(index: number) {
    if (this.currentTab === index) return;
    this.currentTab = index;
    this.fetchHistoryData();
  }

  fetchHistoryData() {
    // 模拟异步获取数据
    uni.showLoading({
      title: "加载中...",
    });

    setTimeout(() => {
      uni.hideLoading();

      if (this.currentTab === 0) {
        this.historyList = [];
      } else if (this.currentTab === 1) {
        this.historyList = [];
      } else {
        this.historyList = [];
      }
    }, 500);
  }

  goToCourse(item: HistoryItem) {
    uni.navigateTo({
      url: `/pages/course/course-detail?id=${item.id}`,
    });
  }

  goToCourseList() {
    uni.switchTab({
      url: "/pages/index/index",
    });
  }

  showClearConfirm() {
    uni.showModal({
      title: "提示",
      content: "确定要清空所有观看历史吗？",
      success: (res) => {
        if (res.confirm) {
          this.clearHistory();
        }
      },
    });
  }

  clearHistory() {
    uni.showLoading({
      title: "清除中...",
    });

    // 模拟清除操作
    setTimeout(() => {
      this.historyList = [];
      uni.hideLoading();
      uni.showToast({
        title: "已清空观看历史",
        icon: "success",
      });
    }, 500);
  }
}
</script>

<style lang="scss" scoped>
.history-container {
  background-color: #f7f8fa;

  .header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 100;
  }

  .content {
    padding: 88px 15px 20px;

    .filter-tabs {
      display: flex;
      background-color: #fff;
      border-radius: 8px;
      margin-bottom: 15px;
      overflow: hidden;

      .tab-item {
        flex: 1;
        height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        color: #666;
        position: relative;

        &.active {
          color: #1989fa;
          font-weight: bold;

          &::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 3px;
            background-color: #1989fa;
            border-radius: 1.5px;
          }
        }
      }
    }

    .history-list {
      .history-item {
        display: flex;
        background-color: #fff;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;

        .course-image {
          width: 120px;
          height: 80px;
          border-radius: 4px;
          margin-right: 12px;
          flex-shrink: 0;
        }

        .course-info {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          .course-name {
            font-size: 15px;
            color: #333;
            line-height: 1.4;
            margin-bottom: 8px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }

          .course-progress {
            margin-bottom: 8px;

            .progress-text {
              font-size: 12px;
              color: #999;
              margin-bottom: 4px;
            }

            .progress-bar {
              height: 4px;
              background-color: #f0f0f0;
              border-radius: 2px;
              overflow: hidden;

              .progress-inner {
                height: 100%;
                background-color: #1989fa;
                border-radius: 2px;
              }
            }
          }

          .watch-info {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .watch-time {
              font-size: 12px;
              color: #999;
            }

            .continue-btn {
              font-size: 12px;
              color: #1989fa;
              padding: 4px 8px;
              border: 1px solid #1989fa;
              border-radius: 12px;
            }
          }
        }
      }
    }

    .empty-history {
      padding: 60px 0;
      display: flex;
      flex-direction: column;
      align-items: center;

      .empty-text {
        margin-top: 10px;
        font-size: 14px;
        color: #999;
        margin-bottom: 20px;
      }

      .go-course-btn {
        padding: 8px 20px;
        background-color: #1989fa;
        color: #fff;
        font-size: 14px;
        border-radius: 20px;
      }
    }

    .clear-history {
      text-align: center;
      padding: 15px 0;
      font-size: 14px;
      color: #999;
    }
  }
}
</style>
