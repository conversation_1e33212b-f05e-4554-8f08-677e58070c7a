<template>
  <view class="checkin-page" :style="{ height: contentHeight + 'px', paddingTop: topHeight + 'px' }">
    <navigation-bar style="width: 100%; z-index: 99" :showIcon="true" :showText="true" text="我的签到"></navigation-bar>

    <view class="checkin-content">
      <!-- 签到状态卡片 -->
      <view class="checkin-card">
        <view class="card-header">
          <view class="checkin-info">
            <text class="checkin-title">每日签到</text>
            <text class="checkin-desc">连续签到获得更多奖励</text>
          </view>
          <view class="checkin-streak">
            <text class="streak-number">{{ consecutiveDays }}</text>
            <text class="streak-label">连续签到</text>
          </view>
        </view>

        <view class="checkin-action">
          <van-button v-if="!todayChecked" type="primary" size="large" @click="doCheckin" :loading="checkingIn">
            {{ checkingIn ? "签到中..." : "立即签到" }}
          </van-button>
          <view v-else class="checked-status">
            <van-icon name="success" size="24" color="#52c41a" />
            <text class="checked-text">今日已签到</text>
          </view>
        </view>
      </view>

      <!-- 签到奖励 -->
      <view class="reward-section">
        <view class="section-title">
          <text class="title-text">签到奖励</text>
        </view>
        <van-empty v-if="!rewardList.length" description="暂无签到奖励" />

        <view class="reward-list" v-else>
          <view
            v-for="(reward, index) in rewardList"
            :key="index"
            class="reward-item"
            :class="{
              current: index + 1 === consecutiveDays % 7 || (consecutiveDays % 7 === 0 && index === 6),
              completed: index + 1 <= consecutiveDays % 7 || (consecutiveDays % 7 === 0 && index <= 6),
            }"
          >
            <view class="reward-day">第{{ index + 1 }}天</view>
            <view class="reward-icon">
              <van-icon :name="reward.icon" size="24" :color="reward.color" />
            </view>
            <text class="reward-name">{{ reward.name }}</text>
            <text class="reward-amount">+{{ reward.amount }}</text>
          </view>
        </view>
      </view>

      <!-- 签到日历 -->
      <view class="calendar-section">
        <view class="section-title">
          <text class="title-text">签到日历</text>
          <text class="month-text">{{ currentMonth }}</text>
        </view>

        <view class="calendar-grid">
          <view class="calendar-header">
            <text v-for="day in weekDays" :key="day" class="week-day">{{ day }}</text>
          </view>

          <view class="calendar-body">
            <view
              v-for="(date, index) in calendarDates"
              :key="index"
              class="calendar-date"
              :class="{
                'other-month': date.otherMonth,
                today: date.isToday,
                checked: date.checked,
              }"
            >
              <text class="date-number">{{ date.day }}</text>
              <view v-if="date.checked" class="check-mark">
                <van-icon name="success" size="12" color="#fff" />
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 签到记录 -->
      <view class="history-section">
        <view class="section-title">
          <text class="title-text">签到记录</text>
          <text class="more-btn" @click="viewHistory">查看全部 ></text>
        </view>
        <van-empty v-if="!checkinHistory.length" description="暂无签到记录" />

        <view class="history-list" v-else>
          <view v-for="(record, index) in checkinHistory" :key="index" class="history-item">
            <view class="history-info">
              <van-icon name="calendar-o" size="20" color="#007aff" />
              <view class="history-details">
                <text class="history-date">{{ record.date }}</text>
                <text class="history-reward">获得{{ record.reward }}</text>
              </view>
            </view>
            <view class="history-right">
              <text class="history-streak">连续{{ record.streak }}天</text>
              <van-icon name="arrow" size="16" color="#999" />
            </view>
          </view>
        </view>
        <van-loading v-if="loadingHistory" size="24px" vertical>加载中...</van-loading>
      </view>
    </view>
  </view>
</template>

<script lang="ts">
import Vue from "vue";
import { Component } from "vue-property-decorator";
import NavigationBar from "@/components/common/NavigationBar.vue";
import { getLayoutHeights } from "@/utils/get-page-height.util";

interface RewardItem {
  name: string;
  amount: number;
  icon: string;
  color: string;
}

interface CheckinRecord {
  date: string;
  reward: string;
  streak: number;
}

interface CalendarDate {
  day: number;
  isToday: boolean;
  checked: boolean;
  otherMonth: boolean;
}

@Component({
  name: "checkin",
  components: { NavigationBar },
})
export default class Checkin extends Vue {
  consecutiveDays = 3;
  todayChecked = false;
  checkingIn = false;

  topHeight = 88;
  contentHeight = 0;

  weekDays = ["日", "一", "二", "三", "四", "五", "六"];

  rewardList: RewardItem[] = [
    // { name: "积分", amount: 10, icon: "gold-coin-o", color: "#ffd700" },
    // { name: "积分", amount: 15, icon: "gold-coin-o", color: "#ffd700" },
    // { name: "积分", amount: 20, icon: "gold-coin-o", color: "#ffd700" },
    // { name: "优惠券", amount: 5, icon: "coupon-o", color: "#ff6b6b" },
    // { name: "积分", amount: 30, icon: "gold-coin-o", color: "#ffd700" },
    // { name: "积分", amount: 40, icon: "gold-coin-o", color: "#ffd700" },
    // { name: "大礼包", amount: 1, icon: "gift-o", color: "#52c41a" },
  ];

  checkinHistory: CheckinRecord[] = [];

  onShow() {
    const { topHeight, contentHeight } = getLayoutHeights();
    this.topHeight = topHeight;
    this.contentHeight = contentHeight;
  }

  get currentMonth() {
    const now = new Date();
    return `${now.getFullYear()}年${now.getMonth() + 1}月`;
  }

  get calendarDates(): CalendarDate[] {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth();

    // 获取当月第一天和最后一天
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);

    // 获取第一天是星期几
    const firstDayWeek = firstDay.getDay();

    const dates: CalendarDate[] = [];

    // 添加上个月的日期
    for (let i = firstDayWeek - 1; i >= 0; i--) {
      const date = new Date(year, month, -i);
      dates.push({
        day: date.getDate(),
        isToday: false,
        checked: false,
        otherMonth: true,
      });
    }

    // 添加当月的日期
    for (let day = 1; day <= lastDay.getDate(); day++) {
      const date = new Date(year, month, day);
      const isToday = day === now.getDate();
      // 模拟签到记录
      const checked = day <= now.getDate() && day >= now.getDate() - this.consecutiveDays + 1;

      dates.push({
        day,
        isToday,
        checked,
        otherMonth: false,
      });
    }

    // 补充下个月的日期，确保6行
    const remainingDays = 42 - dates.length;
    for (let day = 1; day <= remainingDays; day++) {
      dates.push({
        day,
        isToday: false,
        checked: false,
        otherMonth: true,
      });
    }

    return dates;
  }

  onLoad() {
    // 检查今日是否已签到
    const today = new Date().toDateString();
    const lastCheckin = uni.getStorageSync("lastCheckinDate");
    this.todayChecked = lastCheckin === today;

    // 获取连续签到天数
    this.consecutiveDays = uni.getStorageSync("consecutiveDays") || 0;
  }

  async doCheckin() {
    if (this.todayChecked) {
      uni.showToast({ title: "今日已签到", icon: "none" });
      return;
    }

    this.checkingIn = true;

    // 模拟签到请求
    setTimeout(() => {
      this.checkingIn = false;
      this.todayChecked = true;
      this.consecutiveDays += 1;

      // 保存签到状态
      const today = new Date().toDateString();
      uni.setStorageSync("lastCheckinDate", today);
      uni.setStorageSync("consecutiveDays", this.consecutiveDays);

      // 获取奖励
      const rewardIndex = (this.consecutiveDays - 1) % 7;
      const reward = this.rewardList[rewardIndex];

      // 添加签到记录
      const newRecord: CheckinRecord = {
        date: new Date().toLocaleDateString(),
        reward: `${reward.name}+${reward.amount}`,
        streak: this.consecutiveDays,
      };
      this.checkinHistory.unshift(newRecord);

      uni.showToast({
        title: `签到成功！`,
        icon: "success",
        duration: 2000,
      });
    }, 1500);
  }

  viewHistory() {
    uni.navigateTo({
      url: "/pages/checkin/history",
    });
  }
}
</script>

<style lang="scss" scoped>
.checkin-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.checkin-content {
  padding: 15px;
}

/* 签到卡片 */
.checkin-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 25px;
  margin-bottom: 20px;
  color: #fff;
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}

.checkin-info {
  flex: 1;
}

.checkin-title {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 5px;
  display: block;
}

.checkin-desc {
  font-size: 14px;
  opacity: 0.9;
}

.checkin-streak {
  text-align: center;
}

.streak-number {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 5px;
  display: block;
}

.streak-label {
  font-size: 12px;
  opacity: 0.9;
}

.checkin-action {
  text-align: center;
}

.checked-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.checked-text {
  font-size: 16px;
  color: #52c41a;
  font-weight: bold;
}

/* 通用区块样式 */
.reward-section,
.calendar-section,
.history-section {
  background-color: #fff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 15px;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.title-text {
  font-size: 18px;
  color: #333;
  font-weight: bold;
}

.month-text,
.more-btn {
  font-size: 14px;
  color: #007aff;
}

/* 奖励列表 */
.reward-list {
  display: flex;
  gap: 10px;
  overflow-x: auto;
  padding-bottom: 10px;
}

.reward-item {
  min-width: 80px;
  text-align: center;
  padding: 15px 10px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  background-color: #f8f9fa;

  &.current {
    border-color: #007aff;
    background-color: #f0f8ff;
  }

  &.completed {
    background-color: #f6ffed;
    border-color: #52c41a;
  }
}

.reward-day {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.reward-icon {
  margin-bottom: 8px;
}

.reward-name {
  font-size: 12px;
  color: #333;
  margin-bottom: 3px;
  display: block;
}

.reward-amount {
  font-size: 12px;
  color: #007aff;
  font-weight: bold;
}

/* 日历样式 */
.calendar-grid {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
}

.calendar-header {
  display: flex;
  background-color: #f8f9fa;
}

.week-day {
  flex: 1;
  text-align: center;
  padding: 10px 0;
  font-size: 14px;
  color: #666;
  font-weight: bold;
}

.calendar-body {
  display: flex;
  flex-wrap: wrap;
}

.calendar-date {
  width: calc(100% / 7);
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  border-right: 1px solid #e9ecef;
  border-bottom: 1px solid #e9ecef;

  &:nth-child(7n) {
    border-right: none;
  }

  &.other-month {
    color: #ccc;
  }

  &.today {
    background-color: #f0f8ff;
    color: #007aff;
    font-weight: bold;
  }

  &.checked {
    background-color: #52c41a;
    color: #fff;
  }
}

.date-number {
  font-size: 14px;
}

.check-mark {
  position: absolute;
  bottom: 2px;
  right: 2px;
}

/* 签到记录 */
.history-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.history-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.history-details {
  margin-left: 12px;
}

.history-date {
  font-size: 14px;
  color: #333;
  margin-bottom: 3px;
  display: block;
}

.history-reward {
  font-size: 12px;
  color: #52c41a;
}

.history-streak {
  font-size: 12px;
  color: #666;
  background-color: #f8f9fa;
  padding: 4px 8px;
  border-radius: 10px;
}
</style>
