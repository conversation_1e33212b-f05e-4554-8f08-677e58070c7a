<template>
  <view class="history-page">
    <navigation-bar :showIcon="true" :showText="true" text="签到记录"></navigation-bar>

    <view class="filter-bar">
      <van-dropdown-menu>
        <van-dropdown-item v-model="filterType" :options="typeOptions" />
        <van-dropdown-item v-model="filterMonth" :options="monthOptions" />
      </van-dropdown-menu>

      <van-search v-model="searchText" placeholder="搜索签到记录" @search="onSearch" />
    </view>

    <view class="history-list">
      <view v-for="(record, index) in filteredRecords" :key="index" class="history-item">
        <view class="history-info">
          <van-icon name="calendar-o" size="20" color="#007aff" />
          <view class="history-details">
            <text class="history-date">{{ record.date }}</text>
            <text class="history-reward">获得{{ record.reward }}</text>
          </view>
        </view>
        <view class="history-right">
          <text class="history-streak">连续{{ record.streak }}天</text>
          <van-icon name="arrow" size="16" color="#999" />
        </view>
      </view>

      <van-empty v-if="!filteredRecords.length" description="暂无签到记录" />

      <van-loading v-if="loading" size="24px" vertical>加载中...</van-loading>
      <view v-if="!hasMore" class="no-more">没有更多了</view>
    </view>
  </view>
</template>

<script lang="ts">
import Vue from "vue";
import { Component } from "vue-property-decorator";
import NavigationBar from "@/components/common/NavigationBar.vue";

interface CheckinRecord {
  date: string;
  reward: string;
  streak: number;
  type: string;
}

@Component({
  name: "checkin-history",
  components: { NavigationBar },
})
export default class CheckinHistory extends Vue {
  loading = false;
  hasMore = true;
  page = 1;
  pageSize = 10;

  filterType = "all";
  filterMonth = "all";
  searchText = "";

  typeOptions = [
    { text: "全部类型", value: "all" },
    { text: "普通签到", value: "normal" },
    { text: "补签", value: "recheck" },
  ];

  monthOptions = [
    { text: "全部时间", value: "all" },
    { text: "本月", value: "current" },
    { text: "上月", value: "last" },
  ];

  records: CheckinRecord[] = [];

  get filteredRecords() {
    return this.records.filter((record) => {
      const typeMatch = this.filterType === "all" || record.type === this.filterType;
      const textMatch = this.searchText === "" || record.date.includes(this.searchText) || record.reward.includes(this.searchText);
      return typeMatch && textMatch;
    });
  }

  mounted() {
    // this.loadRecords();
  }

  async loadRecords() {}

  onSearch() {
    this.page = 1;
    this.records = [];
    this.loadRecords();
  }

  onLoadMore() {
    if (!this.loading && this.hasMore) {
      this.page++;
      this.loadRecords();
    }
  }
}
</script>

<style lang="scss" scoped>
.history-page {
  padding-top: 88px;

  .filter-bar {
    padding: 16rpx;
    background: #fff;
    position: sticky;
    top: 88px;
    z-index: 99;
  }

  .history-list {
    padding: 16rpx;

    .history-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24rpx;
      margin-bottom: 16rpx;
      background: #fff;
      border-radius: 16rpx;

      .history-info {
        display: flex;
        align-items: center;

        .history-details {
          margin-left: 16rpx;

          .history-date {
            font-size: 28rpx;
            color: #333;
            margin-bottom: 8rpx;
          }

          .history-reward {
            font-size: 24rpx;
            color: #666;
          }
        }
      }

      .history-right {
        display: flex;
        align-items: center;

        .history-streak {
          font-size: 24rpx;
          color: #999;
          margin-right: 16rpx;
        }
      }
    }

    .no-more {
      text-align: center;
      padding: 24rpx;
      color: #999;
      font-size: 24rpx;
    }
  }
}
</style>
