<template>
  <view class="index" :style="{ height: contentHeight  + 'px', paddingTop: topHeight + 'px' }">
    <NavigationBar text="栏目管理" show-icon backLevel showText path="user-center/user-center" />
    <!--    <van-tabs v-model="activeTab" @change="onTabChanged">-->
    <!--      <van-tab v-if="userInfo.userType === AccountType.PERSONAL.code" name="camp">-->
    <van-empty v-if="userInfo.auditStatus === 0" image="error" description="账号暂未激活，请等待管理员审核！" />
    <view class="camp-wrapper" :style="{ maxHeight: `${tabsHeight || 500}px` }" v-else-if="campList.length > 0">
      <!-- 左侧：侧边栏 -->
      <van-sidebar class="camp-sidebar" :active-key="activeKey" @change="onSidebarChange">
        <van-sidebar-item v-for="(item, index) in campList" :key="index" :title="item.name" />
      </van-sidebar>
      <!-- 右侧：课程卡片 -->
      <view class="camp-content" :style="{ maxHeight: `${tabsHeight || 500}px` }">
        <view class="camp-actions">
          <van-button type="primary" block class="btn-share" @click="shareCourse">分享营期</van-button>
        </view>
        <view class="camp-scroll">
          <view v-for="(course, idx) in campList[activeKey].courses" :key="idx" class="custom-card">
            <image class="thumb" :src="course.cover" mode="aspectFill" />
            <view class="content">
              <view class="title">{{ course.title }}</view>
              <view v-html="course.desc" class="desc"></view>
              <view class="time" v-if="course.startTime != null">
                <van-icon name="clock-o" />
                {{ course.startTime }}
              </view>
            </view>
            <van-button size="small" type="primary" class="btn-into" @click="intoSalesCourse(course)">查看课程 </van-button>
          </view>
          <van-divider contentPosition="center">没有更多课程了</van-divider>
        </view>
      </view>
    </view>
    <van-empty v-else image="error" description="暂无营期">
      <van-button v-if="unLogin" round type="info" class="bottom-button" @click="toLogin">去登录</van-button>
    </van-empty>
    <add-button v-if="campList[activeKey].campperiodRedPack" :parent="campList[activeKey].campperiodRedPackAmount" @click="setRedPackAmount"></add-button>
    <van-popup :show="showRedPackDialog" position="bottom" round :z-index="999" lock-scroll :overlay="true" teleport="body">
      <view style="padding: 24px">
        <view style="text-align: right">
          <van-icon name="close" size="20" @click="cancelRedPackAmount" />
        </view>
        <view style="font-size: 32rpx; font-weight: bold; margin-bottom: 20rpx">设置红包金额</view>
        <van-field :value="redPackAmount" @change="amountChange" type="number" label="金额" placeholder="请输入红包金额" input-align="right">
          <template #right-icon>元</template>
        </van-field>
        <view style="margin-top: 24rpx; display: flex; justify-content: center">
          <van-button type="primary" class="btn-red" @click="confirmRedPackAmount">确定</van-button>
        </view>
        <view class="van-multi-ellipsis--l2"> ⚠️注：营期红包设置后，该营期后续所有课程发放的红包金额，会根据当前设置的金额进行发放 </view>
      </view>
    </van-popup>
    <change-button v-if="userInfo.roleType === 2 || userInfo.roleType === 3" @click="showCampSwitcher = true"></change-button>
    <camp-data v-if="userInfo.userType === AccountType.PERSONAL.code && campList.length > 0" @click="clickCampData"></camp-data>
    <change-button v-if="(activeTab === 'camp' && userInfo.roleType === 2) || userInfo.roleType === 3" @click="showCampSwitcher = true"></change-button>
    <van-popup :show="showCampSwitcher" position="bottom" custom-style="overflow: hidden" round :z-index="999" :lock-scroll="true" :overlay="true">
      <view style="height: 70vh; padding: 24rpx">
        <view class="camp-switcher-header">
          <view style="text-align: right">
            <van-icon name="close" size="20" @click="showCampSwitcher = false" />
          </view>
          <view style="font-size: 32rpx; font-weight: bold; margin-bottom: 20rpx">切换营期</view>
        </view>
        <van-tree-select height="600" class="camp-switcher-tree" :items="campTree" :active-id="selectedCompanyId" :main-active-index="activeColumnIndex" @click-nav="onClickNav" @click-item="onClickItem" />
      </view>
    </van-popup>
    <course-share v-model="showShare" :share-course-info="shareCourseInfo"></course-share>
    <qy-user-selector :show="showQyUserSelector" :qy-users="qyUsers" @close="closeQyUserSelector" @confirm="onQyUserSelected"></qy-user-selector>
    <van-popup :show="showUserLogin" position="bottom" custom-style="overflow: scroll" round :z-index="999" :lock-scroll="true" :overlay="true" close-on-click-overlay @close="closeUserLogin">
      <view style="height: 35vh; padding: 24rpx">
        <view class="login-content-container">
          <view style="text-align: right; margin-bottom: 10rpx">
            <van-icon name="cross" size="20" @click="closeUserLogin" />
          </view>
          <view class="login-header">用户登录</view>
          <view>
            <view class="account-input-container">
              <van-field :value="form.account" :error-message="accountError" name="account" label="账号" placeholder="请输入账号" @input="handleInputAccount" />
            </view>
            <van-field :value="form.password" :error-message="passwordError" name="password" label="密码" type="password" @change="handleInputPassword" placeholder="请输入登录密码" />
          </view>
          <view class="submit-section">
            <van-button round block type="info" @click="handleSubmit"> 立即登录</van-button>
          </view>
        </view>
      </view>
    </van-popup>

    <!-- 自定义登录选择弹窗 -->
    <van-popup :show="showLoginOptions" position="center" round :z-index="999" :lock-scroll="true" :overlay="true" close-on-click-overlay @close="closeLoginOptions">
      <view style="padding: 24px; width: 80vw; max-width: 300px">
        <view style="text-align: right">
          <van-icon name="cross" size="20" @click="closeLoginOptions" />
        </view>
        <view style="font-size: 18px; font-weight: bold; text-align: center; margin-bottom: 16px"> 登录</view>
        <view style="text-align: center; margin-bottom: 24px"> 登录后才可以观看哦</view>
        <view style="display: flex; flex-direction: column; gap: 12px">
          <van-button type="primary" block @click="handleQuickLogin"> 一键登录</van-button>
          <van-button type="default" block @click="handleAccountLogin"> 账号登录</van-button>
        </view>
      </view>
    </van-popup>
  </view>
</template>

<script lang="ts">
import Vue from "vue";
import { Component } from "vue-property-decorator";
import NavigationBar from "@/components/common/NavigationBar.vue";
import CourseShare from "@/components/common/CourseShare.vue";
import QyUserSelector from "@/components/common/QyUserSelector.vue";
import { AccountType } from "@/constants/enum/account-type.enum";
import { getCourseByCompanyIdANDSalesId, setCampPeriodRedPacketAmount } from "@/api/camp-period.api";
import { getUserInfo } from "@/api/system-user.api";
import { businessLogin, getAppId, handleLogin } from "@/api/auth.api";
import AddButton from "@/components/flaot-button/AddButton.vue";
import ChangeButton from "@/components/flaot-button/ChangeButton.vue";
import { Code } from "@/constants/enum/code.enum";
import { queryHeadquartersCompanyList } from "@/api/company.api";
import { sm4 } from "sm-crypto";
import CampData from "@/components/flaot-button/CampData.vue";
import {getLayoutHeights} from "@/utils/get-page-height.util";
interface LoginForm {
  account: string;
  password: string;
}

/**
 * @Desc 小程序首页
 */
@Component({
  name: "index",
  computed: {
    AccountType() {
      return AccountType;
    },
  },
  components: {
    CampData,
    ChangeButton,
    AddButton,
    CourseShare,
    NavigationBar,
    QyUserSelector,
  },
})
export default class Index extends Vue {
  userInfo = uni.getStorageSync("userInfo");
  tabBarKey = new Date().getTime();
  activeTab = "camp";
  activeKey = 0;
  showShare = false;
  campList = [];
  showUserLogin = false;
  unLogin = false;
  shareCourseInfo: any = {};
  showRedPackDialog = false;
  redPackAmount = "";
  showCampSwitcher = false;
  campTree = [];
  selectedCompanyId = null;
  activeColumnIndex = 0;
  selectedColumnId = null;
  selectedColumnName = "";
  form: LoginForm = {
    account: "",
    password: "",
  };
  accountError = "";
  passwordError = "";
  key = "7c4a8d09ca3762af61e59520943dc264"; // 16 字节的密钥
  showQyUserSelector = false;
  qyUsers: any[] = [];
  selectedQyUser: any = null;
  pendingShareInfo: any = null;
  topHeight = 88;
  contentHeight = 0;
  onLoad(): void {}

  mounted() {}

  onShow() {
    const { topHeight, contentHeight } = getLayoutHeights();
    this.topHeight = topHeight;
    this.contentHeight = contentHeight;
    if (uni.getStorageSync("wxUserInfo")) {
      this.userLogin();
      return;
    }
    if (!uni.getStorageSync("userInfo")) {
      this.unLogin = true;
    } else {
      const token = uni.getStorageSync("token");
      if (!(token && token.expires > Date.now())) {
        this.unLogin = true;
      } else {
        this.userLogin();
      }
    }
  }

  /**
   * 获取tabs的高度
   */
  get tabsHeight() {
    let topHeight = "";
    let menuButtonObject = uni.getMenuButtonBoundingClientRect(); //获取菜单按钮（右上角胶囊按钮）的布局位置信息。坐标信息以屏幕左上角为原点。
    uni.getSystemInfo({
      //获取系统信息
      success: (res: any) => {
        let navHeight = menuButtonObject.height + (menuButtonObject.top - res.statusBarHeight) * 2; //导航栏高度=菜单按钮高度+（菜单按钮与顶部距离-状态栏高度）*2
        topHeight = navHeight + res.statusBarHeight;
      },
      fail(err) {
        console.log(err);
      },
    });
    const pageHeight = uni.getSystemInfoSync().windowHeight;
    const pageWidth = uni.getSystemInfoSync().windowWidth;
    // 计算tab的高度 = 页面高度 - 导航栏高度 - 顶部状态栏高度
    return pageHeight - topHeight;
  }

  /**
   * 登录信息获取
   */
  getLoginInfo() {
    uni.clearStorageSync();
    if (!uni.getStorageSync("wxUserInfo")) {
      // 跳转微信授权
      uni.navigateTo({ url: "/pages/auth/web-auth" });
    } else {
      this.userLogin();
    }
  }

  userAccountLogin() {
    this.showUserLogin = true;
  }

  // 登录
  async userLogin() {
    try {
      const token = uni.getStorageSync("token");
      if (!(token && token.expires > Date.now())) {
        const userInfo = await handleLogin();
        this.userInfo = userInfo;
        if (userInfo.userType === AccountType.PERSONAL.code) {
          // 系统用户
          if (userInfo.auditStatus !== 0) {
            if (userInfo.roleType === 2 || userInfo.roleType === 3) {
              await this.getOrganizationTree();
            }
            await this.getCourse();
            this.selectedColumnId = this.userInfo.columnId;
            this.selectedCompanyId = this.userInfo.companyId;
          } else {
            // 获取账号审核状态
            await this.getUserInfo();
          }
        }
      } else {
        if (this.userInfo.userType === AccountType.PERSONAL.code) {
          // 系统用户
          if (this.userInfo.auditStatus !== 0) {
            if (this.userInfo.roleType === 2 || this.userInfo.roleType === 3) {
              await this.getOrganizationTree();
            }
            await this.getCourse();
          } else {
            // 获取账号审核状态
            await this.getUserInfo();
          }
          this.selectedColumnId = this.userInfo.columnId;
          this.selectedCompanyId = this.userInfo.companyId;
        }
      }
      this.tabBarKey = new Date().getTime();
      // 登录成功后，设置 unLogin 为 false
      this.unLogin = false;
      return Promise.resolve();
    } catch (e) {
      console.error("登录失败", e);
      this.unLogin = true;
      return Promise.reject(e);
    }
  }

  // 提交登录
  async handleSubmit() {
    if (!this.form.account) {
      this.accountError = "请输入账号";
      return;
    }
    if (!this.form.password) {
      this.passwordError = "请输入密码";
      return;
    }
    try {
      uni.showLoading({ title: "登录中..." });
      const encryptResult = this.encryptData(this.form.password);
      let params = {};
      try {
        params = {
          encryptKey: encryptResult.encryptKey,
          encryptInitIv: encryptResult.encryptInitIv,
          encryptData: encryptResult.encryptData,
          accountId: this.form.account,
        };
      } catch (e) {
        uni.hideLoading();
        uni.showToast({ title: "请检查密码是否包含中文符号", icon: "none" });
        return;
      }
      const res = await businessLogin(params);
      if (res.code !== Code.OK.code) return uni.showToast({ title: res.msg || "登录失败", icon: "none" });
      // 登录成功
      this.showUserLogin = false;
      this.userInfo = res.data.userInfoResponse;
      await this.userLogin();
    } catch (error) {
      uni.showToast({ title: "登录失败", icon: "none" });
    } finally {
      uni.hideLoading();
    }
  }

  handleInputAccount(e: any) {
    this.form.account = e.detail;
    this.accountError = "";
  }
  clickCampData() {
    console.error(this.campList[this.activeKey]);
    const campData = this.campList[this.activeKey];
    const data = {
      companyId: this.selectedCompanyId,
      campPeriodId: campData.id,
      columnId: this.selectedColumnId,
      name: campData.name,
    };
    console.error(data, "data");
    uni.navigateTo({
      url: `/pages/campData/trainingCampData?companyId=${data.companyId}&columnId=${data.columnId}&campPeriodId=${data.campPeriodId}&name=${data.name}`,
    });
  }
  handleInputPassword(e: any) {
    this.form.password = e.detail;
    this.passwordError = "";
  }

  encryptData(jsonStr: any) {
    let encryptData = {};
    try {
      // 设置密钥和初始向量，开始对数据做sm4加密
      const sm4Key = this.randomStr(16);
      const sm4KeyArr = this.strToOneByteArr(sm4Key);
      const iv = this.randomStr(16);
      const sm4InitIvArr = this.strToOneByteArr(iv);
      // 将对象数据转为json数据加密
      const sm4Data = JSON.stringify(jsonStr);
      // console.log("sm4待加密数据:", sm4Data);
      let encryptSM4 = sm4.encrypt(sm4Data, sm4KeyArr, {
        mode: "cbc",
        iv: sm4InitIvArr,
      });
      encryptSM4 = `{SM4}${encryptSM4}`;
      // console.log("sm4加密后数据:", encryptSM4);
      // 加密sm4的初始秘钥与向量，上送给服务器时需要加’04‘头，如果是服务器解密需要把返回的04头去掉
      encryptData = {
        encryptInitIv: iv,
        encryptKey: sm4Key,
        encryptData: encryptSM4,
      };
    } catch (e) {
      console.error(e);
      uni.showToast({ title: "数据加密失败" });
    }
    return encryptData;
  }

  randomStr(length: number) {
    const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    let result = "";
    for (let item = 0; item < length; item++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * @description: 将字符串转为字节数组
   */
  strToOneByteArr(str: string) {
    const arr = [];
    for (let i = 0, len = str.length; i < len; i++) {
      arr.push(str.charCodeAt(i));
    }
    return arr;
  }

  /**
   * @Desc 下拉刷新页面
   */
  onPullDownRefresh() {
    const userInfo = uni.getStorageSync("userInfo");
    const token = uni.getStorageSync("token");

    // 检查用户是否已登录且token是否有效
    if (!userInfo || !(token && token.expires > Date.now())) {
      uni.showToast({
        title: "请先登录后再刷新",
        icon: "none",
        duration: 2000,
      });
      uni.stopPullDownRefresh();
      return;
    }

    this.userLogin().finally(() => {
      uni.stopPullDownRefresh();
    });
  }

  getUserInfo() {
    getUserInfo({ id: this.userInfo.id })
      .then((response) => {
        console.log(response);
        if (response.data.auditStatus == 2) {
          const userInfo = response.data;
          userInfo.userType = AccountType.PERSONAL.code;
          uni.setStorageSync("userInfo", userInfo);
          this.userInfo = userInfo;
          this.getCourse();
        }
      })
      .catch((error) => {
        console.error("错误:", error);
        // this.reloadIndex();
        this.unLogin = true;
      });
  }

  async getCourse() {
    // 显示加载提示
    uni.showLoading({
      title: "加载训练营数据...",
      mask: true,
    });

    try {
      const params = {
        companyId: this.selectedCompanyId ?? this.userInfo.companyId,
        ...(this.selectedCompanyId == null && { salesGroupId: this.userInfo.salesGroupId }),
      };

      const response = await getCourseByCompanyIdANDSalesId(params);
      this.campList = response.data;
      if (response.code !== Code.OK.code) {
        this.unLogin = false;
        return;
      }
      this.unLogin = false;
    } catch (error) {
      console.error("获取课程列表失败:", error);
      this.reloadIndex();
      this.unLogin = true;
    } finally {
      // 无论成功或失败，都隐藏加载提示
      uni.hideLoading();
    }
  }

  getOrganizationTree() {
    queryHeadquartersCompanyList({
      id: this.userInfo.headquartersId, // 总公司ID
      level: 2, // 公司层级
    })
      .then((resp) => {
        const columns = resp.data.columns || [];
        this.campTree = columns.map((column: any) => ({
          id: Number(column.id),
          text: column.name,
          children: (column.companies || []).map((company: any) => ({
            id: Number(company.id),
            text: company.name,
          })),
        }));
      })
      .catch((error) => {
        console.error("错误:", error);
        this.reloadIndex();
        this.unLogin = true;
      });
  }

  reloadIndex() {
    uni.showToast({
      title: "缓存失效，请重新登录！",
      duration: 2000,
      icon: "none",
    });
    setTimeout(() => {
      uni.reLaunch({
        url: "/pages/index/index",
      });
    }, 2000);
    uni.clearStorageSync();
  }

  onClickNav({ detail = {} }) {
    this.activeColumnIndex = detail.index || 0;
  }

  async onClickItem({ detail = {} }) {
    // 显示加载提示
    uni.showLoading({
      title: "切换营期中...",
      mask: true,
    });

    try {
      const column = this.campTree[this.activeColumnIndex];
      const selectedCompany = detail;
      this.selectedColumnId = column.id || null;
      this.selectedColumnName = column.text;
      this.selectedCompanyId = selectedCompany.id;
      this.showCampSwitcher = false;
      this.activeKey = 0;
      await this.getCourse(); // 等待数据加载完成
    } catch (error) {
      console.error("切换营期失败:", error);
      uni.showToast({
        title: "切换营期失败",
        icon: "none",
      });
    } finally {
      uni.hideLoading();
    }
  }

  getTodayDateStr(): string {
    const now = new Date();
    const yyyy = now.getFullYear();
    const mm = String(now.getMonth() + 1).padStart(2, "0");
    const dd = String(now.getDate()).padStart(2, "0");
    return `${yyyy}-${mm}-${dd}`;
  }

  onTabChanged(name: string): void {
    console.log(name);
    this.activeTab = name.detail.name;
  }

  onSidebarChange(key: any): void {
    // 显示短暂的加载提示
    uni.showLoading({
      title: "切换中...",
      mask: true,
    });

    uni.removeStorageSync("currentCampCourses");
    this.activeKey = key.detail;

    // 使用setTimeout确保UI有时间更新
    setTimeout(() => {
      uni.hideLoading();
    }, 300);
  }

  // 登录选择弹窗变量
  showLoginOptions = false;

  toLogin() {
    console.log("toLogin", new Date().getTime());
    // 显示自定义登录选择弹窗
    this.showLoginOptions = true;
  }

  // 账号登录
  handleAccountLogin() {
    this.showLoginOptions = false;
    this.userAccountLogin();
  }

  // 一键登录
  handleQuickLogin() {
    this.showLoginOptions = false;
    setTimeout(() => {
      this.getLoginInfo();
    }, 100);
  }

  // 关闭登录选择弹窗
  closeLoginOptions() {
    this.showLoginOptions = false;
  }

  // 关闭账号登录弹窗
  closeUserLogin() {
    this.showUserLogin = false;
  }

  // 关闭企微用户选择弹窗
  closeQyUserSelector() {
    this.showQyUserSelector = false;
    this.pendingShareInfo = null;
    this.qyUsers = [];
  }

  // 企微用户选择确认
  onQyUserSelected(selectedUser: any) {
    this.showQyUserSelector = false;
    if (this.pendingShareInfo) {
      this.proceedWithShare(this.pendingShareInfo.camp, this.pendingShareInfo.course, selectedUser);
    }
    this.pendingShareInfo = null;
    this.qyUsers = [];
  }

  shareCourse() {
    const currentCamp = this.campList[this.activeKey];
    let todayCourses: any[] = [];

    if (currentCamp.startingFlag === "0") {
      todayCourses = currentCamp.courses || [];
    } else {
      const todayStr = this.getTodayDateStr();
      todayCourses = (currentCamp.courses || []).filter((course) => course.startTime === todayStr);
    }

    if (todayCourses.length === 0) {
      uni.showToast({ title: "暂无可分享的课程", icon: "none" });
      return;
    }

    // 检查企微用户绑定情况
    const qyRelations = this.userInfo.systemUserQyRelationResponses || [];

    if (qyRelations.length >= 2) {
      // 有多个企微用户，需要选择
      this.qyUsers = qyRelations;
      this.pendingShareInfo = {
        camp: currentCamp,
        course: todayCourses[0],
      };
      this.showQyUserSelector = true;
      return;
    }

    // 只有一个或没有企微用户，直接分享
    const selectedQyUser = qyRelations.length === 1 ? qyRelations[0] : null;
    this.proceedWithShare(currentCamp, todayCourses[0], selectedQyUser);
  }

  proceedWithShare(camp: any, course: any, qyUser: any = null) {
    // 保存当前分享的课程，供 onShareAppMessage 使用
    const params = {
      companyId: this.selectedCompanyId,
      columnId: this.selectedColumnId,
      salesGroupId: this.userInfo.salesGroupId,
      courseId: course.id,
      salesId: this.userInfo.id,
      campPeriodId: camp.id,
      appId: getAppId(),
      ...(qyUser && {
        corpId: qyUser.corpId,
        qyUserId: qyUser.qyUserId,
      }),
    };
    console.log(params);
    this.shareCourseInfo = {
      camp: camp,
      course: course,
      params,
    };
    this.showShare = true;
  }

  onShareAppMessage() {
    const shareInfo = this.shareCourseInfo;
    if (!shareInfo || !shareInfo.course || !shareInfo.camp) {
      return;
    }

    const { course, camp, params } = shareInfo;

    // 构建分享路径，包含企微参数
    let sharePath = `/pages/course/course?companyId=${params.companyId}&columnId=${params.columnId}&salesGroupId=${this.userInfo.salesGroupId}&courseId=${course.id}&salesId=${this.userInfo.id}&campPeriodId=${camp.id}`;

    if (params.corpId && params.qyUserId) {
      sharePath += `&corpId=${params.corpId}&qyUserId=${params.qyUserId}`;
    }

    return {
      title: camp.explicitName,
      path: sharePath,
      imageUrl: course.cover,
    };
  }

  intoSalesCourse(course: any) {
    console.log(course);
    const allCourses = this.campList[this.activeKey]?.courses || [];
    uni.setStorageSync("currentCampCourses", allCourses);
    uni.navigateTo({
      url: `/pages/course/sales-course?data=${JSON.stringify(course)}`,
    });
  }

  intoCourse() {
    uni.navigateTo({
      url: `/pages/course/course?companyId=` + this.userInfo.companyId + `&columnId=` + this.userInfo.columnId + `&campPeriodId=` + this.userInfo.campPeriodId,
    });
  }
  amountChange(e: any) {
    this.redPackAmount = e.detail;
  }
  setRedPackAmount() {
    // 普通销售不可设置红包金额
    if (this.userInfo.roleType === "6" || this.userInfo.roleType === 6) {
      uni.showToast({ title: "您没有权限设置营期红包金额", icon: "none" });
      return;
    }
    this.redPackAmount = this.campList[this.activeKey].campperiodRedPackAmount;
    this.showRedPackDialog = true;
  }

  confirmRedPackAmount() {
    const amount = Number(this.redPackAmount);
    const range = this.campList[this.activeKey].redPackRange;

    if (isNaN(amount) || amount <= 0) {
      uni.showToast({ title: "请输入有效的红包金额", icon: "none" });
      return;
    }

    if (range && range.length === 2) {
      const [min, max] = range.map(Number);
      if (amount < min || amount > max) {
        uni.showToast({ title: `红包金额应在 ${min} 元 到 ${max} 元之间`, icon: "none" });
        return;
      }
    }
    setCampPeriodRedPacketAmount({ id: this.campList[this.activeKey].id, redPacketAmount: this.redPackAmount })
      .then((response) => {
        if (response.code == Code.OK.code) {
          uni.showToast({ title: `设置成功：¥${amount}`, icon: "success" });
          this.$set(this.campList[this.activeKey], "campperiodRedPackAmount", this.redPackAmount);
        } else {
          uni.showToast({ title: `设置失败`, icon: "error" });
        }
        this.showRedPackDialog = false;
      })
      .catch(() => {
        uni.showToast({ title: `请求异常，设置失败`, icon: "error" });
        this.showRedPackDialog = false;
      });
  }

  cancelRedPackAmount() {
    this.showRedPackDialog = false;
  }

  /**
   * @Desc 打开公开课视频
   * @param item 公开课信息
   */
  openPublicCourse(item: any) {
    console.log(item);
    // 判断是否登录
    if (this.unLogin || !uni.getStorageSync("userInfo")) {
      this.toLogin();
      return;
    }

    // 已登录，跳转到课程播放页面
    uni.navigateTo({
      url: `/pages/course/sales-course?data=${JSON.stringify(item)}`,
    });
  }

  /**
   * @Desc 打开动态详情H5页面
   * @param item 动态信息
   */
  openNewsDetail(item: any) {
    // 检查URL是否是微信公众号文章链接
    if (item.url && item.url.includes("mp.weixin.qq.com/s/")) {
      // 使用微信官方API打开公众号文章
      wx.openOfficialAccountArticle({
        url: item.url, // 直接传递完整的文章URL
        success: () => {
          console.log("打开公众号文章成功");
        },
        fail: (err) => {
          console.error("打开公众号文章失败:", err);
          // 失败时回退到使用系统浏览器打开
          uni.setClipboardData({
            data: item.url,
            success: () => {
              uni.showModal({
                title: "提示",
                content: "链接已复制，请在浏览器中打开",
                showCancel: false,
              });
            },
          });
        },
      });
    } else {
      // 如果不是微信公众号文章链接，则使用系统浏览器打开
      uni.setClipboardData({
        data: item.url,
        success: () => {
          uni.showModal({
            title: "提示",
            content: "链接已复制，请在浏览器中打开",
            showCancel: false,
          });
        },
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.index {}

.index-carousel {
  width: 100%;
  height: 350rpx;

  swiper,
  swiper-item,
  image {
    width: 100%;
    height: 100%;
  }
}

.camp-wrapper {
  display: flex;
  overflow: hidden !important;
  padding: 12px;
  height: 100%;
}

.van-sidebar {
  flex-shrink: 0;
  width: 100px; // 控制左侧宽度
}

.camp-sidebar {
  overflow-y: auto;
  max-height: 100%; /* 确保侧边栏可以滚动 */
  padding-bottom: 120rpx; /* 为底部tabbar预留空间 */
}

.camp-content {
  flex: 1;
  margin-left: 12px;
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100%;
}

.camp-actions {
  position: sticky;
  top: 0;
  z-index: 20;
  background: #fff;
  padding: 12px 0;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
  border-radius: 8px;
}

.camp-scroll {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 120rpx; /* 为底部tabbar预留空间 */
  -webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
}

.custom-card {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #fff;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 12px;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);

  .thumb {
    width: 100rpx;
    height: 100rpx;
    border-radius: 8rpx;
    margin-right: 12rpx;
  }

  .content {
    flex: 1;
    margin-right: 10rpx;

    .title {
      font-size: 32rpx;
      font-weight: bold;
      margin-bottom: 6rpx;
    }

    .desc {
      font-size: 26rpx;
      color: #666;
      margin-bottom: 6rpx;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .time {
      color: #f44;
      font-size: 28rpx;
      font-weight: bold;
      text-align: left;
      margin-top: 4rpx;
    }

    .tag {
      display: inline-block;
      background: #f5f5f5;
      padding: 2rpx 8rpx;
      border-radius: 4rpx;
      font-size: 24rpx;
      color: #999;
      margin-top: 4rpx;
    }
  }
}

.btn-share {
  width: 100%;
  display: flex;
  text-align: center;
  bottom: 20px;

  ::v-deep .van-button--normal {
    width: 90%;
    background: $standard-color;
    border-radius: 5px;
    border: none;
  }
}

.btn-red {
  width: 100%;
  display: flex;
  text-align: center;
  bottom: 20px;

  ::v-deep .van-button--normal {
    width: 90%;
    background: #ff4d4f;
    border-radius: 5px;
    border: none;
  }
}

.btn-into {
  position: absolute;
  bottom: 16rpx;
  right: 16rpx;

  ::v-deep .van-button--primary {
    background: $standard-color;
    border-radius: 5px;
    border: none;
  }
}

.record-card {
  display: flex;
  background: #fff;
  padding: 24rpx;
  border-radius: 16rpx;
  margin: 24rpx;
  box-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.06);
  min-height: 240rpx; // 增加高度

  .thumb {
    width: 240rpx;
    height: 140rpx;
    border-radius: 12rpx;
    margin-right: 24rpx;
    flex-shrink: 0;
    object-fit: cover;
  }

  .right {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex: 1;
    padding: 10rpx 0;

    .content {
      .title {
        font-size: 36rpx;
        font-weight: bold;
        margin-bottom: 10rpx;
      }

      .desc {
        font-size: 28rpx;
        color: #666;
      }
    }

    .btn {
      align-self: flex-end;
      margin-top: 20rpx;

      ::v-deep .van-button--primary {
        background: $standard-color;
        border-radius: 5px;
        border: none;
      }
    }
  }
}

.camp-switcher-header {
  position: sticky;
  top: 0;
  background: #fff;
  z-index: 10;
}

.camp-switcher-tree {
  overflow: hidden;
}

.login-content-container {
  display: flex;
  flex-direction: column;
}

.login-header {
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  padding: 10px 0;
  background: #fff;
  position: relative;
  z-index: 10;
}

.account-input-container {
  margin-bottom: 20px;
}

.submit-section {
  margin-top: 30px;
}

/* 动态卡片样式 */
.news-wrapper {
  padding: 24rpx;
}

.news-card {
  display: flex;
  background: #fff;
  padding: 24rpx;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.06);
  position: relative;
}

.news-thumb {
  width: 200rpx;
  height: 140rpx;
  border-radius: 12rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
  object-fit: cover;
}

.news-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.news-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.news-desc {
  font-size: 28rpx;
  color: #666;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  margin-bottom: 10rpx;
}

.news-time {
  font-size: 24rpx;
  color: #999;
  display: flex;
  align-items: center;
}

.news-time .van-icon {
  margin-right: 6rpx;
}
</style>
