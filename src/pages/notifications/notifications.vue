<template>
  <view class="notifications-page" :style="{ height: contentHeight  + 'px', paddingTop: topHeight + 'px' }">
    <navigation-bar style="width: 100%; z-index: 99" :showIcon="true" :showText="true" text="消息通知"></navigation-bar>

    <view class="notifications-content">
      <!-- 消息分类 -->
      <view class="message-tabs">
        <view v-for="(tab, index) in messageTabs" :key="index" class="tab-item" :class="activeTab === index ? 'active' : ''" @click="switchTab(index)">
          <text class="tab-text">{{ tab.name }}</text>
          <view v-if="tab.count > 0" class="tab-badge">{{ tab.count }}</view>
        </view>
      </view>

      <!-- 消息列表 -->
      <view class="message-list">
        <view v-for="(message, index) in currentMessages" :key="index" class="message-item" :class="!message.isRead ? 'unread' : ''" @click="readMessage(message, index)">
          <view class="message-icon">
            <van-icon :name="message.icon" size="24" :color="message.iconColor" />
          </view>

          <view class="message-content">
            <view class="message-header">
              <text class="message-title">{{ message.title }}</text>
              <text class="message-time">{{ message.time }}</text>
            </view>
            <text class="message-desc">{{ message.content }}</text>
          </view>

          <view v-if="!message.isRead" class="unread-dot"></view>
        </view>

        <!-- 空状态 -->
        <view v-if="currentMessages.length === 0" class="empty-state">
          <van-empty description="暂无消息" />
        </view>
      </view>
    </view>

    <!-- 消息详情弹窗 -->
    <van-popup :show="showMessageDetail" position="center" round @close="closeMessageDetail">
      <view class="message-detail-popup">
        <view class="detail-header">
          <text class="detail-title">{{ selectedMessage ? selectedMessage.title : "" }}</text>
          <van-icon name="cross" size="20" @click="closeMessageDetail" />
        </view>

        <view class="detail-content">
          <view class="detail-time">{{ selectedMessage ? selectedMessage.time : "" }}</view>
          <view class="detail-text">{{ selectedMessage ? selectedMessage.content : "" }}</view>
          <view v-if="selectedMessage && selectedMessage.detailContent" class="detail-extra">
            {{ selectedMessage.detailContent }}
          </view>
        </view>

        <view class="detail-footer">
          <van-button type="primary" block @click="closeMessageDetail">知道了</van-button>
        </view>
      </view>
    </van-popup>
  </view>
</template>

<script lang="ts">
import Vue from "vue";
import { Component } from "vue-property-decorator";
import NavigationBar from "@/components/common/NavigationBar.vue";
import {getLayoutHeights} from "@/utils/get-page-height.util";

interface Message {
  id: number;
  title: string;
  content: string;
  detailContent: string;
  time: string;
  type: string;
  isRead: boolean;
  icon: string;
  iconColor: string;
}

@Component({
  name: "notifications",
  components: { NavigationBar },
})
export default class Notifications extends Vue {
  activeTab = 0;
  showMessageDetail = false;
  selectedMessage: Message | null = null;

  topHeight = 88;
  contentHeight = 0;

  messageTabs = [
    { name: "全部", count: 5 },
    { name: "订单", count: 2 },
    { name: "活动", count: 1 },
    { name: "系统", count: 2 },
  ];

  allMessages: Message[] = [
    {
      id: 1,
      title: "订单支付成功",
      content: "您的订单已支付成功，商家正在准备发货",
      detailContent: "订单号：202312150001\n商品：新鲜苹果 x2\n金额：¥25.60\n预计发货时间：2小时内",
      time: "2小时前",
      type: "order",
      isRead: false,
      icon: "success",
      iconColor: "#52c41a",
    },
    {
      id: 2,
      title: "商品已发货",
      content: "您购买的商品已发货，请注意查收",
      detailContent: "快递公司：顺丰速运\n快递单号：SF1234567890\n预计送达时间：明天上午",
      time: "5小时前",
      type: "order",
      isRead: false,
      icon: "logistics",
      iconColor: "#1890ff",
    },
    {
      id: 3,
      title: "新用户专享优惠",
      content: "恭喜您获得新用户专享100元优惠券",
      detailContent: "优惠券已发放到您的账户，有效期30天，满200元可用，快去选购心仪的商品吧！",
      time: "1天前",
      type: "activity",
      isRead: true,
      icon: "gift-o",
      iconColor: "#ff4444",
    },
    {
      id: 4,
      title: "系统维护通知",
      content: "系统将于今晚23:00-01:00进行维护",
      detailContent: "为了给您提供更好的服务体验，我们将在今晚23:00-01:00进行系统维护升级，期间可能无法正常使用，给您带来的不便敬请谅解。",
      time: "2天前",
      type: "system",
      isRead: false,
      icon: "warning-o",
      iconColor: "#faad14",
    },
    {
      id: 5,
      title: "账户安全提醒",
      content: "检测到您的账户在新设备登录",
      detailContent: "登录时间：2023-12-15 14:30\n登录设备：iPhone 15\n登录地点：上海市浦东新区\n如非本人操作，请及时修改密码。",
      time: "3天前",
      type: "system",
      isRead: true,
      icon: "shield-o",
      iconColor: "#722ed1",
    },
  ];

  get currentMessages() {
    if (this.activeTab === 0) {
      return this.allMessages;
    }
    const typeMap = ["", "order", "activity", "system"];
    return this.allMessages.filter((msg) => msg.type === typeMap[this.activeTab]);
  }

  switchTab(index: number) {
    this.activeTab = index;
  }

  readMessage(message: Message, index: number) {
    this.selectedMessage = message;
    this.showMessageDetail = true;

    // 标记为已读
    if (!message.isRead) {
      message.isRead = true;
      this.updateTabCounts();
    }
  }

  closeMessageDetail() {
    this.showMessageDetail = false;
    this.selectedMessage = null;
  }

  updateTabCounts() {
    // 重新计算各分类的未读数量
    this.messageTabs[0].count = this.allMessages.filter((msg) => !msg.isRead).length;
    this.messageTabs[1].count = this.allMessages.filter((msg) => msg.type === "order" && !msg.isRead).length;
    this.messageTabs[2].count = this.allMessages.filter((msg) => msg.type === "activity" && !msg.isRead).length;
    this.messageTabs[3].count = this.allMessages.filter((msg) => msg.type === "system" && !msg.isRead).length;
  }

  onLoad() {
    this.updateTabCounts();
  }
  onShow() {
    const { topHeight, contentHeight } = getLayoutHeights();
    this.topHeight = topHeight;
    this.contentHeight = contentHeight;
  }
}
</script>

<style lang="scss" scoped>
.notifications-page {
  background-color: #f5f5f5;
}

.notifications-content {}

.message-tabs {
  background-color: #fff;
  display: flex;
  border-bottom: 1px solid #f0f0f0;
  position: fixed;
  z-index: 99;
  width: 100%;
}

.tab-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15px 3px;
  position: relative;
  min-width: 0;

  &.active {
    .tab-text {
      color: #007aff;
      font-weight: bold;
    }

    &::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 24px;
      height: 2px;
      background-color: #007aff;
    }
  }
}

.tab-text {
  font-size: 14px;
  color: #666;
  text-align: center;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.tab-badge {
  position: absolute;
  top: 8px;
  right: 15px;
  background-color: #ff4444;
  color: #fff;
  font-size: 10px;
  padding: 1px 5px;
  border-radius: 8px;
  min-width: 14px;
  height: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  transform: scale(0.9);
}

.message-list {
  padding: 50px 15px 0 15px;
}

.message-item {
  background-color: #fff;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 10px;
  display: flex;
  align-items: flex-start;
  position: relative;

  &.unread {
    background-color: #f8f9ff;
    border-left: 3px solid #007aff;
  }
}

.message-icon {
  margin-right: 15px;
  margin-top: 2px;
}

.message-content {
  flex: 1;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.message-title {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.message-time {
  font-size: 12px;
  color: #999;
}

.message-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.unread-dot {
  width: 8px;
  height: 8px;
  background-color: #ff4444;
  border-radius: 50%;
  margin-left: 10px;
  margin-top: 8px;
}

.empty-state {
  padding: 50px 20px;
  text-align: center;
}

/* 消息详情弹窗 */
.message-detail-popup {
  width: 80vw;
  max-width: 400px;
  max-height: 60vh;
  padding: 20px;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.detail-title {
  font-size: 18px;
  color: #333;
  font-weight: bold;
}

.detail-content {
  margin-bottom: 20px;
}

.detail-time {
  font-size: 12px;
  color: #999;
  margin-bottom: 10px;
}

.detail-text {
  font-size: 16px;
  color: #333;
  line-height: 1.6;
  margin-bottom: 15px;
}

.detail-extra {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  white-space: pre-line;
}
</style>
