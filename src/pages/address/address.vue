<template>
  <view class="address-page" :style="{ height: contentHeight  + 'px', paddingTop: topHeight + 'px' }">
    <navigation-bar style="width: 100%; z-index: 99" :showIcon="true" :showText="true" text="地址管理"></navigation-bar>

    <view class="address-content">
      <!-- 加载状态 -->
      <view v-if="loading" class="loading-state">
        <van-loading type="spinner" color="#007aff" />
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 地址列表 -->
      <view class="address-list" v-else>
        <view v-for="(address, index) in addressList" :key="index" class="address-item" :class="address.isDefault === 1 ? 'default' : ''" @click="selectAddress(address, index)">
          <view class="address-info">
            <view class="address-header">
              <text class="receiver-name">{{ address.name }}</text>
              <text class="receiver-phone">{{ address.phone }}</text>
              <view v-if="address.isDefault === 1" class="default-tag">默认</view>
            </view>
            <text class="address-detail">{{ address.fullAddress || address.province + address.city + address.district + address.detail }}</text>
          </view>

          <view class="address-actions">
            <van-icon name="edit" size="20" color="#666" @click.stop="editAddress(address, index)" />
            <van-icon name="delete-o" size="20" color="#666" @click.stop="deleteAddress(index)" />
          </view>
        </view>

        <!-- 空状态 -->
        <view v-if="!loading && addressList.length === 0" class="empty-state">
          <van-empty description="暂无地址" />
        </view>
      </view>

      <!-- 添加地址按钮 -->
      <view class="add-address-btn">
        <van-button type="primary" block @click="addAddress">添加新地址</van-button>
      </view>
    </view>

    <!-- 地址编辑弹窗 -->
    <van-popup :show="showAddressForm" position="bottom" round @close="closeAddressForm">
      <view class="address-form">
        <view class="form-header">
          <text class="form-title">{{ isEdit ? "编辑地址" : "新增地址" }}</text>
          <van-icon name="cross" size="20" @click="closeAddressForm" />
        </view>

        <view class="form-content">
          <!-- 地址贴贴板 -->
          <view class="clipboard-section">
            <view class="clipboard-header" @click="toggleClipboard">
              <text class="clipboard-title">地址贴贴板</text>
              <van-icon :name="showClipboard ? 'arrow-up' : 'arrow-down'" size="16" />
            </view>

            <!-- 展开的识别框 -->
            <view v-if="showClipboard" class="paste-content">
              <textarea v-model="pasteText" class="paste-textarea" placeholder="粘贴整段地址，自动拆分姓名、电话及地址" @blur="parsePastedAddress" />
              <view class="paste-actions">
                <text class="clear-btn" @click="clearPasteText">清除</text>
                <text class="recognize-btn" @click="recognizeAddress">识别</text>
              </view>
            </view>
          </view>

          <!-- 姓名和电话 -->
          <view class="form-item">
            <view class="form-label">
              <text class="label-text">联系方式</text>
              <text class="required">*</text>
            </view>
            <view class="form-row">
              <input v-model="formData.name" class="form-input name-input" placeholder="请输入姓名" maxlength="16" />
              <input v-model="formData.phone" class="form-input phone-input" :class="{ error: phoneError }" placeholder="请输入手机号" type="tel" maxlength="11" @input="validatePhone" @blur="validatePhone" />
            </view>
            <view v-if="phoneError" class="error-message">
              <text class="error-text">{{ phoneError }}</text>
            </view>
          </view>

          <!-- 地区选择 -->
          <view class="form-item">
            <view class="form-label">
              <text class="label-text">地区</text>
              <text class="required">*</text>
            </view>
            <picker mode="region" :value="regionValue" @change="onRegionChange" custom-item="请选择">
              <view class="region-selector">
                <text class="region-text" :class="!selectedRegion ? 'placeholder' : ''">
                  {{ selectedRegion || "请选择省市区" }}
                </text>
              </view>
            </picker>
          </view>

          <!-- 详细地址 -->
          <view class="form-item">
            <view class="form-label">
              <text class="label-text">地址</text>
              <text class="required">*</text>
            </view>
            <input v-model="formData.detail" class="form-input" placeholder="详细地址1-80个字符" maxlength="80" />
          </view>

          <!-- 标签选择 -->
          <view class="tag-section">
            <text class="tag-label">标签</text>
            <view class="tag-list">
              <text v-for="(tag, index) in addressTags" :key="index" class="tag-item" :class="formData.tag === tag ? 'active' : ''" @click="selectTag(tag)">
                {{ tag }}
              </text>
              <text class="tag-item add-tag" @click="addCustomTag">+</text>
            </view>
          </view>

          <!-- 设为默认地址 -->
          <view class="default-section">
            <text class="default-label">设为默认地址</text>
            <switch :checked="formData.isDefault" @change="onDefaultChange" />
          </view>
        </view>

        <view class="form-footer">
          <van-button type="primary" block @click="saveAddress">保存</van-button>
        </view>
      </view>
    </van-popup>
  </view>
</template>

<script lang="ts">
import Vue from "vue";
import { Component } from "vue-property-decorator";
import NavigationBar from "@/components/common/NavigationBar.vue";
import { getAddressList, saveAddress, deleteAddress, setDefaultAddress } from "@/api/mall.api";
import { Address, AddressSaveParams } from "@/types/mall.types";
import { Code } from "@/constants/enum/code.enum";
import { authManager } from "../../utils/auth";
import {getLayoutHeights} from "@/utils/get-page-height.util";

@Component({
  name: "address",
  components: { NavigationBar },
})
export default class AddressView extends Vue {
  showAddressForm = false;
  isEdit = false;
  editIndex = -1;
  isSelectMode = false;
  showClipboard = false;
  phoneError = "";
  loading = false;
  customerId = "";

  addressTags = ["家", "公司", "学校"];
  selectedRegion = "";
  pasteText = "";
  regionValue = ["", "", ""];

  formData = {
    name: "",
    phone: "",
    province: "",
    city: "",
    district: "",
    detail: "",
    fullAddress: "",
    tag: "家",
    isDefault: false,
  };
  topHeight = 88;
  contentHeight = 0;
  addressList: Address[] = [];

  // 获取用户ID（使用新的认证管理器）
  async getUserId(): Promise<string | null> {
    return await authManager.getUserId(true);
  }

  // 加载地址列表
  async loadAddressList() {
    if (this.customerId === "") {
      return;
    }

    this.loading = true;
    try {
      const response = await getAddressList(this.customerId);
      if (response.code === Code.OK.code) {
        this.addressList = response.data || [];
      } else {
        uni.showToast({
          title: response.msg || "加载地址列表失败",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("加载地址列表失败:", error);
      uni.showToast({
        title: "网络错误，请重试",
        icon: "none",
      });
    } finally {
      this.loading = false;
    }
  }

  async onLoad(options: any) {
    // 获取用户ID
    const userId = await this.getUserId();
    if (userId) {
      this.customerId = userId;

      // 如果是从其他页面选择地址跳转过来
      if (options.select) {
        this.isSelectMode = true;
      }

      // 加载地址列表
      await this.loadAddressList();
    } else {
      // 用户取消登录，跳转到首页
      uni.switchTab({
        url: '/pages/home/<USER>'
      });
    }
  }

  onShow() {
    const { topHeight, contentHeight } = getLayoutHeights();
    this.topHeight = topHeight;
    this.contentHeight = contentHeight;
  }

  selectAddress(address: Address, index: number) {
    // 如果是选择模式，返回选中的地址
    if (this.isSelectMode) {
      uni.navigateBack({
        success: () => {
          // 通过事件总线或其他方式传递选中的地址
          uni.$emit("addressSelected", address);
        },
      });
    }
  }

  addAddress() {
    this.isEdit = false;
    this.resetFormData();
    this.showAddressForm = true;
  }

  editAddress(address: Address, index: number) {
    this.isEdit = true;
    this.editIndex = index;
    this.formData = {
      name: address.name,
      phone: address.phone,
      province: address.province,
      city: address.city,
      district: address.district,
      detail: address.detail,
      fullAddress: address.fullAddress || "",
      tag: address.tag || "家",
      isDefault: address.isDefault === 1,
    };
    // 同时更新地区选择器的值
    this.selectedRegion = `${address.province} ${address.city} ${address.district}`;
    this.regionValue = [address.province, address.city, address.district];
    this.showAddressForm = true;
  }

  async deleteAddress(index: number) {
    const address = this.addressList[index];
    uni.showModal({
      title: "删除地址",
      content: "确定要删除这个地址吗？",
      success: async (res) => {
        if (res.confirm) {
          try {
            const response = await deleteAddress(address.id, this.customerId);
            if (response.code === Code.OK.code) {
              uni.showToast({ title: "删除成功", icon: "success" });
              await this.loadAddressList(); // 重新加载地址列表
            } else {
              uni.showToast({
                title: response.msg || "删除失败",
                icon: "none",
              });
            }
          } catch (error) {
            console.error("删除地址失败:", error);
            uni.showToast({
              title: "网络错误，请重试",
              icon: "none",
            });
          }
        }
      },
    });
  }

  closeAddressForm() {
    this.showAddressForm = false;
    this.resetFormData();
  }

  resetFormData() {
    this.formData = {
      name: "",
      phone: "",
      province: "",
      city: "",
      district: "",
      detail: "",
      fullAddress: "",
      tag: "家",
      isDefault: false,
    };
    this.selectedRegion = "";
    this.pasteText = "";
    this.regionValue = ["", "", ""];
    this.phoneError = "";
  }

  selectTag(tag: string) {
    this.formData.tag = tag;
  }

  addCustomTag() {
    uni.showModal({
      title: "添加标签",
      editable: true,
      placeholderText: "请输入标签名称",
      success: (res) => {
        if (res.confirm && res.content) {
          const newTag = res.content.trim();
          if (newTag && !this.addressTags.includes(newTag)) {
            this.addressTags.push(newTag);
            this.formData.tag = newTag;
          }
        }
      },
    });
  }

  onRegionChange(e: any) {
    const [province, city, district] = e.detail.value;
    this.formData.province = province;
    this.formData.city = city;
    this.formData.district = district;
    this.selectedRegion = `${province} ${city} ${district}`;
    this.regionValue = [province, city, district];
  }

  onDefaultChange(e: any) {
    this.formData.isDefault = e.detail.value;
  }

  validatePhone() {
    const phone = this.formData.phone.trim();

    // 清空之前的错误信息
    this.phoneError = "";

    if (!phone) {
      return; // 空值不校验，在保存时统一校验
    }

    // 手机号格式校验
    const mobileRegex = /^1[3-9]\d{9}$/;
    // 座机号格式校验 (区号-号码 或 区号号码)
    const landlineRegex = /^(0\d{2,3}-?\d{7,8})$/;

    if (!mobileRegex.test(phone) && !landlineRegex.test(phone)) {
      if (phone.length < 11) {
        this.phoneError = "请输入完整的手机号码";
      } else if (phone.length > 11 && !landlineRegex.test(phone)) {
        this.phoneError = "请输入正确的座机号码格式";
      } else {
        this.phoneError = "请输入正确的手机号或座机号";
      }
    }
  }

  toggleClipboard() {
    this.showClipboard = !this.showClipboard;
  }

  clearPasteText() {
    this.pasteText = "";
  }

  recognizeAddress() {
    if (!this.pasteText.trim()) {
      uni.showToast({ title: "请先输入地址信息", icon: "none" });
      return;
    }
    this.parseAddressFromText(this.pasteText);
    uni.showToast({ title: "识别成功", icon: "success" });
  }

  parsePastedAddress() {
    if (this.pasteText.trim()) {
      this.parseAddressFromText(this.pasteText);
    }
  }

  // 解析地址文本
  parseAddressFromText(text: string) {
    // 尝试提取手机号
    const phoneMatch = text.match(/1[3-9]\d{9}/);
    if (phoneMatch && !this.formData.phone) {
      this.formData.phone = phoneMatch[0];
    }

    // 尝试提取姓名（简单规则：2-4个中文字符，在手机号前面）
    const nameMatch = text.match(/[\u4e00-\u9fa5]{2,4}/);
    if (nameMatch && !this.formData.name) {
      this.formData.name = nameMatch[0];
    }

    // 提取地区信息（省市区）
    const regionMatch = this.extractRegionFromText(text);
    if (regionMatch) {
      this.formData.province = regionMatch.province;
      this.formData.city = regionMatch.city;
      this.formData.district = regionMatch.district;
      this.selectedRegion = `${regionMatch.province} ${regionMatch.city} ${regionMatch.district}`;
      this.regionValue = [regionMatch.province, regionMatch.city, regionMatch.district];
    }

    // 提取详细地址（移除姓名、手机号和地区后的内容）
    let addressText = text;
    if (phoneMatch) {
      addressText = addressText.replace(phoneMatch[0], "");
    }
    if (nameMatch) {
      addressText = addressText.replace(nameMatch[0], "");
    }
    if (regionMatch) {
      addressText = addressText.replace(regionMatch.fullRegion, "");
    }

    // 清理地址文本，移除时间信息
    addressText = addressText.replace(/[，,\s]+/g, "").trim();
    // 移除时间相关信息
    addressText = addressText.replace(/明天|今天|后天|早上|上午|下午|晚上|[0-9]+点|[0-9]+时/g, "");

    if (addressText && !this.formData.detail) {
      this.formData.detail = addressText;
    }
  }

  // 提取地区信息
  extractRegionFromText(text: string) {
    // 匹配地区格式：省+市+区/县
    const regionPattern = /(北京市|天津市|上海市|重庆市|[^市]+省|[^区]+自治区|香港特别行政区|澳门特别行政区)([^区县市]+市)?([^区县]+[区县])/;
    const match = text.match(regionPattern);

    if (match) {
      let province = match[1];
      let city = match[2] || "";
      let district = match[3];

      // 处理直辖市的情况
      if (["北京市", "天津市", "上海市", "重庆市"].includes(province)) {
        city = province;
      } else if (city) {
        // 移除市字后再加上
        city = city.replace("市", "") + "市";
      }

      return {
        province,
        city,
        district,
        fullRegion: match[0],
      };
    }

    // 如果没有匹配到完整格式，尝试简单匹配
    const simpleMatch = text.match(/(北京|天津|上海|重庆)([^区县]+[区县])/);
    if (simpleMatch) {
      const province = simpleMatch[1] + "市";
      return {
        province,
        city: province,
        district: simpleMatch[2],
        fullRegion: simpleMatch[0],
      };
    }

    return null;
  }

  async saveAddress() {
    // 表单验证
    if (!this.formData.name.trim()) {
      uni.showToast({ title: "请输入收货人姓名", icon: "none" });
      return;
    }

    if (!this.formData.phone.trim()) {
      uni.showToast({ title: "请输入手机号", icon: "none" });
      return;
    }

    // 触发手机号校验
    this.validatePhone();
    if (this.phoneError) {
      uni.showToast({ title: this.phoneError, icon: "none" });
      return;
    }

    if (!this.formData.province || !this.formData.city || !this.formData.district) {
      uni.showToast({ title: "请选择省市区", icon: "none" });
      return;
    }

    if (!this.formData.detail.trim()) {
      uni.showToast({ title: "请输入详细地址", icon: "none" });
      return;
    }

    try {
      const addressData: AddressSaveParams = {
        customerId: this.customerId,
        name: this.formData.name,
        phone: this.formData.phone,
        province: this.formData.province,
        city: this.formData.city,
        district: this.formData.district,
        detail: this.formData.detail,
        tag: this.formData.tag,
        isDefault: this.formData.isDefault ? 1 : 0,
      };

      // 如果是编辑模式，添加ID
      if (this.isEdit && this.editIndex >= 0) {
        addressData.id = this.addressList[this.editIndex].id;
      }

      const response = await saveAddress(addressData);
      if (response.code === Code.OK.code) {
        this.closeAddressForm();
        uni.showToast({
          title: this.isEdit ? "修改成功" : "添加成功",
          icon: "success",
        });
        await this.loadAddressList(); // 重新加载地址列表
      } else {
        uni.showToast({
          title: response.msg || "保存失败",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("保存地址失败:", error);
      uni.showToast({
        title: "网络错误，请重试",
        icon: "none",
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.address-page {
  background-color: #f5f5f5;
}

.address-content {
  padding: 15px;
}

.loading-state {
  padding: 50px 20px;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.loading-text {
  font-size: 14px;
  color: #666;
}

.address-list {
  margin-bottom: 20px;
}

.address-item {
  background-color: #fff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 15px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  &.default {
    border: 2px solid #007aff;
  }
}

.address-info {
  flex: 1;
}

.address-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.receiver-name {
  font-size: 16px;
  color: #333;
  font-weight: bold;
  margin-right: 15px;
}

.receiver-phone {
  font-size: 14px;
  color: #666;
  margin-right: 10px;
}

.default-tag {
  background-color: #007aff;
  color: #fff;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 8px;
}

.address-detail {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.address-actions {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-left: 15px;
}

.empty-state {
  padding: 50px 20px;
  text-align: center;
}

.add-address-btn {
  margin-top: 20px;
}

/* 地址表单样式 */
.address-form {
  padding: 20px;
  max-height: 80vh;
  overflow-y: auto;
  background-color: #f5f5f5;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e5e5e5;
}

.form-title {
  font-size: 18px;
  color: #333;
  font-weight: bold;
}

.form-content {
  margin-bottom: 30px;
}

.form-item {
  background-color: #fff;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 15px;
}

.form-label {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.label-text {
  font-size: 16px;
  color: #333;
  margin-right: 5px;
}

.required {
  color: #ff4444;
  font-size: 16px;
}

.form-input {
  width: 100%;
  font-size: 16px;
  color: #333;
  border: none;
  outline: none;

  &::placeholder {
    color: #999;
  }
}

.form-row {
  display: flex;
  gap: 12px;
  align-items: center;
}

.name-input {
  flex: 1;
  min-width: 0;
  padding: 12px 15px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  font-size: 16px;
  color: #333;

  &::placeholder {
    color: #999;
  }

  &:focus {
    border-color: #007aff;
    background-color: #fff;
    outline: none;
  }
}

.phone-input {
  flex: 1.5;
  min-width: 0;
  padding: 12px 15px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  font-size: 16px;
  color: #333;

  &::placeholder {
    color: #999;
  }

  &:focus {
    border-color: #007aff;
    background-color: #fff;
    outline: none;
  }

  &.error {
    border-color: #ff4444;
    background-color: #fff5f5;
  }
}

.error-message {
  margin-top: 8px;
  padding-left: 15px;
}

.error-text {
  font-size: 12px;
  color: #ff4444;
  line-height: 1.4;
}

.region-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
}

.region-text {
  font-size: 16px;
  color: #333;

  &.placeholder {
    color: #999;
  }
}

.clipboard-section {
  background-color: #fff;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 15px;
}

.clipboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}

.clipboard-title {
  font-size: 16px;
  color: #333;
}

.paste-content {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #f0f0f0;
}

.paste-textarea {
  min-height: 80px;
  font-size: 14px;
  color: #333;
  border: none;
  outline: none;
  resize: none;
  background-color: #f8f8f8;
  border-radius: 8px;
  padding: 10px;

  &::placeholder {
    color: #999;
  }
}

.paste-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin-top: 10px;
}

.clear-btn {
  color: #999;
  font-size: 14px;
  padding: 6px 12px;
  border-radius: 6px;
}

.recognize-btn {
  background-color: #0289ea;
  color: #fff;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
}

.tag-section {
  background-color: #fff;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 15px;
}

.tag-label {
  font-size: 16px;
  color: #333;
  margin-bottom: 15px;
  display: block;
}

.tag-list {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.tag-item {
  padding: 8px 16px;
  border: 1px solid #e5e5e5;
  border-radius: 20px;
  font-size: 14px;
  color: #666;
  background-color: #f8f8f8;

  &.active {
    border-color: #007aff;
    color: #007aff;
    background-color: #f0f8ff;
  }

  &.add-tag {
    border-style: dashed;
    color: #999;
  }
}

.default-section {
  background-color: #fff;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.default-label {
  font-size: 16px;
  color: #333;
}

/* 确保switch正常显示 */
.default-section switch {
  flex-shrink: 0;
  transform: scale(0.8);
}

.form-footer {
  padding: 0 20px 20px;

  .van-button {
    background: linear-gradient(135deg, #ff6b9d, #ff4757);
    border: none;
    border-radius: 25px;
    height: 50px;
    font-size: 16px;
    font-weight: bold;
  }
}
</style>
