<template>
  <view class="settings-page" :style="{ height: contentHeight  + 'px', paddingTop: topHeight + 'px' }">
    <navigation-bar style="width: 100%; z-index: 99" :showIcon="true" :showText="true" text="设置"></navigation-bar>

    <view class="settings-content">
      <!-- 用户信息区域 -->
      <view class="user-section">
        <view class="user-item" @click="goUserProfile">
          <view class="user-info">
            <image v-if="wxUserInfo && wxUserInfo.headimgurl" :src="wxUserInfo.headimgurl" mode="aspectFill" class="user-avatar" />
            <open-data v-else type="userAvatarUrl" class="user-avatar"></open-data>
            <text class="user-name">微信用户</text>
          </view>
          <van-icon name="arrow" size="16" color="#999" />
        </view>
      </view>

      <!-- 账户设置 -->
      <view class="settings-section">
        <view class="settings-item" @click="changePhone">
          <text class="item-label">更换手机</text>
          <view class="item-right">
            <text class="item-value">点击绑定</text>
            <van-icon name="arrow" size="16" color="#999" />
          </view>
        </view>

        <view class="settings-item" @click="changeEmail">
          <text class="item-label">更换邮箱</text>
          <view class="item-right">
            <text class="item-value">点击绑定</text>
            <van-icon name="arrow" size="16" color="#999" />
          </view>
        </view>

        <view class="settings-item" @click="changePassword">
          <text class="item-label">更换密码</text>
          <view class="item-right">
            <text class="item-value">点击更换</text>
            <van-icon name="arrow" size="16" color="#999" />
          </view>
        </view>

        <view class="settings-item" @click="changeLanguage">
          <text class="item-label">修改语言</text>
          <view class="item-right">
            <text class="item-value">简体中文</text>
            <van-icon name="arrow" size="16" color="#999" />
          </view>
        </view>
      </view>

      <!-- 功能设置 -->
      <view class="settings-section">
        <view class="settings-item" @click="addressManagement">
          <text class="item-label">地址管理</text>
          <view class="item-right">
            <text class="item-value">点击管理</text>
            <van-icon name="arrow" size="16" color="#999" />
          </view>
        </view>

        <view class="settings-item" @click="invoiceManagement">
          <text class="item-label">发票管理</text>
          <view class="item-right">
            <text class="item-value">点击前往</text>
            <van-icon name="arrow" size="16" color="#999" />
          </view>
        </view>
      </view>

      <!-- 系统设置 -->
      <view class="settings-section">
        <view class="settings-item" @click="permissionSettings">
          <text class="item-label">权限设置</text>
          <view class="item-right">
            <text class="item-value">点击管理</text>
            <van-icon name="arrow" size="16" color="#999" />
          </view>
        </view>

        <view class="settings-item" @click="clearCache">
          <text class="item-label">清除缓存</text>
          <view class="item-right">
            <text class="item-value">点击清除</text>
            <van-icon name="arrow" size="16" color="#999" />
          </view>
        </view>
      </view>

      <!-- 联系客服 -->
      <view class="settings-section">
        <view class="settings-item" @click="callCustomerService">
          <text class="item-label">客服电话</text>
          <view class="item-right">
            <text class="item-value">400-1805185 点击拨打</text>
            <van-icon name="arrow" size="16" color="#999" />
          </view>
        </view>
      </view>

      <!-- 账号注销 -->
      <view class="settings-section">
        <view class="settings-item danger" @click="deleteAccount">
          <text class="item-label danger-text">账号注销</text>
          <view class="item-right">
            <text class="item-value danger-text">注销后无法恢复</text>
            <van-icon name="arrow" size="16" color="#ff4444" />
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts">
import Vue from "vue";
import { Component } from "vue-property-decorator";
import NavigationBar from "@/components/common/NavigationBar.vue";
import {getLayoutHeights} from "@/utils/get-page-height.util";

@Component({
  name: "settings",
  components: { NavigationBar },
})
export default class Settings extends Vue {
  wxUserInfo = uni.getStorageSync("wxUserInfo");
  topHeight = 88;
  contentHeight = 0;

  onShow() {
    const { topHeight, contentHeight } = getLayoutHeights();
    this.topHeight = topHeight;
    this.contentHeight = contentHeight;
  }

  goUserProfile() {
    uni.navigateTo({
      url: "/pages/user-center/user-info",
    });
  }

  changePhone() {
    uni.showToast({ title: "更换手机功能开发中", icon: "none" });
  }

  changeEmail() {
    uni.showToast({ title: "更换邮箱功能开发中", icon: "none" });
  }

  changePassword() {
    uni.showToast({ title: "更换密码功能开发中", icon: "none" });
  }

  changeLanguage() {
    uni.showActionSheet({
      itemList: ["简体中文", "English", "繁體中文"],
      success: (res) => {
        const languages = ["简体中文", "English", "繁體中文"];
        uni.showToast({
          title: `已切换到${languages[res.tapIndex]}`,
          icon: "success",
        });
      },
    });
  }

  addressManagement() {
    uni.navigateTo({
      url: "/pages/address/address",
    });
  }

  invoiceManagement() {
    uni.showToast({ title: "发票管理功能开发中", icon: "none" });
  }

  permissionSettings() {
    uni.showToast({ title: "权限设置功能开发中", icon: "none" });
  }

  clearCache() {
    uni.showModal({
      title: "清除缓存",
      content: "确定要清除所有缓存数据吗？",
      success: (res) => {
        if (res.confirm) {
          uni.showLoading({ title: "清除中..." });
          setTimeout(() => {
            uni.hideLoading();
            uni.showToast({ title: "缓存清除成功", icon: "success" });
          }, 1500);
        }
      },
    });
  }

  callCustomerService() {
    uni.makePhoneCall({
      phoneNumber: "400-1805185",
    });
  }

  deleteAccount() {
    uni.showModal({
      title: "账号注销",
      content: "注销后账号数据将无法恢复，确定要注销吗？",
      confirmColor: "#ff4444",
      success: (res) => {
        if (res.confirm) {
          uni.showToast({ title: "账号注销功能开发中", icon: "none" });
        }
      },
    });
  }
}
</script>

<style lang="scss" scoped>
.settings-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.settings-content {
  padding: 20px 15px;
}

.user-section {
  background-color: #fff;
  border-radius: 12px;
  margin-bottom: 20px;
  overflow: hidden;
}

.user-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  margin-right: 15px;
}

.user-name {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.settings-section {
  background-color: #fff;
  border-radius: 12px;
  margin-bottom: 20px;
  overflow: hidden;
}

.settings-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 18px 20px;
  border-bottom: 1px solid #f5f5f5;

  &:last-child {
    border-bottom: none;
  }

  &.danger {
    background-color: #fff5f5;
  }
}

.item-label {
  font-size: 16px;
  color: #333;

  &.danger-text {
    color: #ff4444;
  }
}

.item-right {
  display: flex;
  align-items: center;
}

.item-value {
  font-size: 14px;
  color: #666;
  margin-right: 8px;

  &.danger-text {
    color: #ff4444;
  }
}
</style>
