<template>
  <view class="contact-container" :style="{ height: contentHeight  + 'px', paddingTop: topHeight + 'px' }">
    <navigation-bar showText text="联系我们" showIcon />

    <view class="content">
      <view class="section info-section">
        <view class="section-title">联系方式</view>
        <view class="contact-info">
          <view class="section">
            <view class="section-content">如果您学习中遇到任何问题，您加入的学习群群主就是您的学习顾问，可以跟您的学习顾问沟通。 如果您有紧急问题，投诉建议，可拨打电话：4001805185</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import NavigationBar from "@/components/common/NavigationBar.vue";
import { getLayoutHeights } from "@/utils/get-page-height.util";

@Component({
  components: { NavigationBar },
})
export default class Contact extends Vue {
  form = {
    name: "",
    phone: "",
    email: "",
    message: "",
  };
  topHeight = 88;
  contentHeight = 0;

  onShow() {
    const { topHeight, contentHeight } = getLayoutHeights();
    this.topHeight = topHeight;
    this.contentHeight = contentHeight;
  }

  onClickLeft() {
    uni.navigateBack();
  }

  submitForm() {
    // 表单验证
    if (!this.form.name) {
      uni.showToast({
        title: "请输入姓名",
        icon: "none",
      });
      return;
    }
    if (!this.form.phone) {
      uni.showToast({
        title: "请输入手机号码",
        icon: "none",
      });
      return;
    }
    if (!this.form.message) {
      uni.showToast({
        title: "请输入留言内容",
        icon: "none",
      });
      return;
    }

    // 提交表单
    uni.showLoading({
      title: "提交中...",
    });

    // 模拟提交
    setTimeout(() => {
      uni.hideLoading();
      uni.showToast({
        title: "提交成功",
        icon: "success",
      });
      this.form = {
        name: "",
        phone: "",
        email: "",
        message: "",
      };
    }, 1500);
  }
}
</script>

<style lang="scss" scoped>
.contact-container {
  background-color: #f7f8fa;

  .header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 100;
  }

  .content {
    padding: 88px 20px 20px;

    .section {
      margin-bottom: 20px;
      background-color: #fff;
      border-radius: 8px;
      padding: 15px;

      .section-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 15px;
        color: #333;
      }
    }

    .info-section {
      .contact-info {
        .info-item {
          display: flex;
          align-items: center;
          margin-bottom: 12px;

          text {
            margin-left: 10px;
            font-size: 14px;
            color: #333;
          }
        }
      }
    }

    .form-section {
      .form {
        .form-item {
          margin-bottom: 15px;

          .label {
            display: block;
            font-size: 14px;
            color: #333;
            margin-bottom: 5px;
          }

          .input {
            width: 100%;
            height: 40px;
            border: 1px solid #eee;
            border-radius: 4px;
            padding: 0 10px;
            font-size: 14px;
            box-sizing: border-box;
          }

          .textarea {
            width: 100%;
            height: 100px;
            border: 1px solid #eee;
            border-radius: 4px;
            padding: 10px;
            font-size: 14px;
            box-sizing: border-box;
          }
        }

        .submit-btn {
          height: 44px;
          background-color: #1989fa;
          color: #fff;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 16px;
          margin-top: 20px;
        }
      }
    }

    .qrcode-section {
      .qrcode-container {
        display: flex;
        justify-content: space-around;

        .qrcode-item {
          display: flex;
          flex-direction: column;
          align-items: center;

          .qrcode {
            width: 120px;
            height: 120px;
            margin-bottom: 10px;
          }

          text {
            font-size: 14px;
            color: #333;
          }
        }
      }
    }
  }
}
</style>
