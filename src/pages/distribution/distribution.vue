<template>
  <view class="distribution-page" :style="{ height: contentHeight  + 'px', paddingTop: topHeight + 'px' }">
    <navigation-bar style="width: 100%; z-index: 99" backLevel :showIcon="true" :showText="true" text="我的分销"></navigation-bar>

    <view class="distribution-content">
      <!-- 分销员信息卡片 -->
      <view class="distributor-card">
        <view class="card-bg"></view>
        <view class="card-content">
          <view class="distributor-info">
            <image :src="userInfo.avatar" class="user-avatar" mode="aspectFill" />
            <view class="user-details">
              <text class="user-name">{{ userInfo.name }}</text>
              <view class="referral-code">
                <text class="code-label">推荐码</text>
                <text class="code-value">{{ userInfo.referralCode }}</text>
                <van-icon name="copy" size="16" color="#fff" @click="copyReferralCode" />
              </view>
              <view class="level-info">
                <van-icon name="diamond" size="16" color="#ffd700" />
                <text class="level-text">分销等级{{ userInfo.level }}</text>
              </view>
            </view>
            <view class="apply-btn" @click="applyDistributor">
              <text class="apply-text">申请取货点</text>
            </view>
          </view>

          <view class="rule-notice" @click="showDistributionRules">
            <van-icon name="volume-o" size="16" color="#fff" />
            <text class="notice-text">分销规则公告</text>
          </view>

          <view class="superior-info">
            <text class="superior-label">上级用户</text>
            <view class="superior-user">
              <image :src="superiorInfo.avatar" class="superior-avatar" mode="aspectFill" />
              <text class="superior-name">{{ superiorInfo.name }}</text>
              <text class="modify-btn" @click="modifySuperior">修改</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 基础统计 -->
      <view class="stats-section">
        <view class="stats-header">
          <text class="stats-title">基础统计</text>
          <picker :value="monthIndex" :range="monthOptions" @change="onMonthChange">
            <view class="month-picker">
              <text class="month-text">{{ monthOptions[monthIndex] }}</text>
              <van-icon name="arrow-down" size="12" color="#666" />
            </view>
          </picker>
        </view>

        <view class="stats-grid">
          <view class="stat-item">
            <text class="stat-number">{{ stats.promotedCustomers }}</text>
            <text class="stat-label">已推广客户</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{ stats.consumedCustomers }}</text>
            <text class="stat-label">已消费客户</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{ stats.unconsumedCustomers }}</text>
            <text class="stat-label">未消费客户</text>
          </view>
        </view>

        <view class="detail-stats">
          <view class="detail-row">
            <view class="detail-item">
              <view class="detail-icon purple">
                <van-icon name="friends-o" size="20" color="#fff" />
              </view>
              <view class="detail-info">
                <text class="detail-title">新增客户</text>
                <text class="detail-value">{{ stats.newCustomers }}人</text>
                <text class="detail-desc">有效({{ stats.validNew }}) 需复购({{ stats.needRepurchase }})</text>
              </view>
            </view>

            <view class="detail-item">
              <view class="detail-icon green">
                <van-icon name="chart-trending-o" size="20" color="#fff" />
              </view>
              <view class="detail-info">
                <text class="detail-title">新增客户总GMV</text>
                <text class="detail-value">¥{{ stats.newCustomerGMV }}</text>
              </view>
            </view>
          </view>

          <view class="detail-row">
            <view class="detail-item">
              <view class="detail-icon blue">
                <van-icon name="shopping-cart-o" size="20" color="#fff" />
              </view>
              <view class="detail-info">
                <text class="detail-title">订单总数</text>
                <text class="detail-value">{{ stats.totalOrders }}笔</text>
              </view>
            </view>

            <view class="detail-item">
              <view class="detail-icon teal">
                <van-icon name="gold-coin-o" size="20" color="#fff" />
              </view>
              <view class="detail-info">
                <text class="detail-title">订单总GMV</text>
                <text class="detail-value">¥{{ stats.totalOrderGMV }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 返佣统计 -->
      <view class="commission-section">
        <view class="section-title">
          <text class="title-text">返佣统计</text>
        </view>

        <view class="commission-grid">
          <view class="commission-item">
            <text class="commission-label">返佣总额</text>
            <text class="commission-value">¥{{ commission.total }}</text>
          </view>
          <view class="commission-item">
            <text class="commission-label">待生效</text>
            <text class="commission-value">¥{{ commission.pending }}</text>
          </view>
          <view class="commission-item">
            <text class="commission-label">待结算</text>
            <text class="commission-value">¥{{ commission.unsettled }}</text>
          </view>
          <view class="commission-item">
            <text class="commission-label">已结算</text>
            <text class="commission-value">¥{{ commission.settled }}</text>
          </view>
        </view>
      </view>
      <!-- 功能菜单 -->
      <view class="function-menu">
        <view class="menu-row">
          <view class="menu-item" @click="goCustomerOrders">
            <view class="menu-icon blue">
              <image src="/static/images/sales/cust-order.png" class="icon-img" mode="aspectFill" />
            </view>
            <text class="menu-text">客户订单</text>
          </view>
          <view class="menu-item" @click="goMyTeam">
            <view class="menu-icon orange">
              <image src="/static/images/sales/my-group.png" class="icon-img" mode="aspectFill" />
            </view>
            <text class="menu-text">我的团队</text>
          </view>
        </view>

        <view class="menu-row">
          <view class="menu-item" @click="goCustomerDistribution">
            <view class="menu-icon green">
              <image src="/static/images/sales/cust-dist.png" class="icon-img" mode="aspectFill" />
            </view>
            <text class="menu-text">客户分布</text>
          </view>
          <view class="menu-item" @click="goCustomerVisit">
            <view class="menu-icon teal">
              <image src="/static/images/sales/cust-visit.png" class="icon-img" mode="aspectFill" />
            </view>
            <text class="menu-text">客户拜访</text>
          </view>
        </view>

        <view class="menu-row">
          <view class="menu-item" @click="goRecommendTreasure">
            <view class="menu-icon pink">
              <image src="/static/images/sales/recommended-baby.png" class="icon-img" mode="aspectFill" />
            </view>
            <text class="menu-text">推荐宝</text>
          </view>
          <view class="menu-item" @click="goPromotionReward">
            <view class="menu-icon blue-light">
              <image src="/static/images/sales/promotion-reward.png" class="icon-img" mode="aspectFill" />
            </view>
            <text class="menu-text">推广奖励</text>
          </view>
        </view>

        <view class="menu-row">
          <view class="menu-item" @click="goIncomeDetail">
            <view class="menu-icon red">
              <image src="/static/images/sales/income-detail.png" class="icon-img" mode="aspectFill" />
            </view>
            <text class="menu-text">收益明细</text>
          </view>
          <view class="menu-item" @click="goLevelIntroduction">
            <view class="menu-icon yellow">
              <image src="/static/images/sales/level-intro.png" class="icon-img" mode="aspectFill" />
            </view>
            <text class="menu-text">等级介绍</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts">
import Vue from "vue";
import { Component } from "vue-property-decorator";
import NavigationBar from "@/components/common/NavigationBar.vue";
import {getLayoutHeights} from "@/utils/get-page-height.util";

interface UserInfo {
  name: string;
  avatar: string;
  referralCode: string;
  level: number;
}

interface SuperiorInfo {
  name: string;
  avatar: string;
}

interface Stats {
  promotedCustomers: number;
  consumedCustomers: number;
  unconsumedCustomers: number;
  newCustomers: number;
  validNew: number;
  needRepurchase: number;
  newCustomerGMV: string;
  totalOrders: number;
  totalOrderGMV: string;
}

interface Commission {
  total: string;
  pending: string;
  unsettled: string;
  settled: string;
}

@Component({
  name: "distribution",
  components: { NavigationBar },
})
export default class Distribution extends Vue {
  monthIndex = 0;
  monthOptions = ["当月", "上月", "近3个月", "近6个月"];
  topHeight = 88;
  contentHeight = 0;

  userInfo: UserInfo = {
    name: "微信用户",
    avatar: "/static/images/test.jpeg",
    referralCode: "D4A61523D44494D3",
    level: 3,
  };

  superiorInfo: SuperiorInfo = {
    name: "微信用户",
    avatar: "/static/images/test.jpeg",
  };

  stats: Stats = {
    promotedCustomers: 0,
    consumedCustomers: 0,
    unconsumedCustomers: 0,
    newCustomers: 0,
    validNew: 0,
    needRepurchase: 0,
    newCustomerGMV: "0.00",
    totalOrders: 0,
    totalOrderGMV: "0.00",
  };

  commission: Commission = {
    total: "0.00",
    pending: "0.00",
    unsettled: "0.00",
    settled: "0.00",
  };

  onLoad() {
    this.loadDistributionData();
  }

  onShow() {
    const { topHeight, contentHeight } = getLayoutHeights();
    this.topHeight = topHeight;
    this.contentHeight = contentHeight;
  }

  loadDistributionData() {
    // 模拟加载分销数据
    this.stats = {
      promotedCustomers: Math.floor(Math.random() * 50),
      consumedCustomers: Math.floor(Math.random() * 30),
      unconsumedCustomers: Math.floor(Math.random() * 20),
      newCustomers: Math.floor(Math.random() * 10),
      validNew: Math.floor(Math.random() * 5),
      needRepurchase: Math.floor(Math.random() * 5),
      newCustomerGMV: (Math.random() * 1000).toFixed(2),
      totalOrders: Math.floor(Math.random() * 100),
      totalOrderGMV: (Math.random() * 5000).toFixed(2),
    };

    this.commission = {
      total: (Math.random() * 1000).toFixed(2),
      pending: (Math.random() * 200).toFixed(2),
      unsettled: (Math.random() * 300).toFixed(2),
      settled: (Math.random() * 500).toFixed(2),
    };
  }

  copyReferralCode() {
    uni.setClipboardData({
      data: this.userInfo.referralCode,
      success: () => {
        uni.showToast({ title: "推荐码已复制", icon: "success" });
      },
    });
  }

  applyDistributor() {
    uni.showModal({
      title: "申请取货点",
      content: "申请成为取货点可享受更多分销权益",
      success: (res) => {
        if (res.confirm) {
          uni.showToast({ title: "申请已提交", icon: "success" });
        }
      },
    });
  }

  showDistributionRules() {
    uni.showToast({ title: "分销规则页面开发中", icon: "none" });
  }

  modifySuperior() {
    uni.showToast({ title: "修改上级功能开发中", icon: "none" });
  }

  onMonthChange(e: any) {
    this.monthIndex = e.detail.value;
    this.loadDistributionData();
  }

  // 功能菜单方法
  goCustomerOrders() {
    uni.showToast({ title: "客户订单页面开发中", icon: "none" });
  }

  goMyTeam() {
    uni.navigateTo({
      url: "/pages/columnManagement/column-index",
    });
  }

  goCustomerDistribution() {
    uni.navigateTo({
      url: "/pages/customer-mgmt/home-page",
    });
  }

  goCustomerVisit() {
    uni.showToast({ title: "客户拜访页面开发中", icon: "none" });
  }

  goRecommendTreasure() {
    uni.showToast({ title: "推荐宝页面开发中", icon: "none" });
  }

  goPromotionReward() {
    uni.showToast({ title: "推广奖励页面开发中", icon: "none" });
  }

  goIncomeDetail() {
    uni.showToast({ title: "收益明细页面开发中", icon: "none" });
  }

  goLevelIntroduction() {
    uni.showToast({ title: "等级介绍页面开发中", icon: "none" });
  }
}
</script>

<style lang="scss" scoped>
.distribution-page {
  background-color: #f5f5f5;
}

.distribution-content {
  padding: 15px;
}

/* 分销员信息卡片 */
.distributor-card {
  position: relative;
  background: linear-gradient(135deg, #ff6b9d 0%, #ff8a80 100%);
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 20px;
  overflow: hidden;
  color: #fff;
}

.card-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="3" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="2" fill="rgba(255,255,255,0.1)"/></svg>');
}

.card-content {
  position: relative;
  z-index: 2;
}

.distributor-info {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.user-avatar {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  margin-right: 15px;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 5px;
  display: block;
}

.referral-code {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 5px;
}

.code-label {
  font-size: 12px;
  opacity: 0.9;
}

.code-value {
  font-size: 14px;
  font-weight: bold;
}

.level-info {
  display: flex;
  align-items: center;
  gap: 5px;
}

.level-text {
  font-size: 12px;
  opacity: 0.9;
}

.apply-btn {
  background-color: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  padding: 8px 16px;
}

.apply-text {
  font-size: 12px;
  color: #fff;
}

.rule-notice {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 15px;
}

.notice-text {
  font-size: 14px;
  opacity: 0.9;
}

.superior-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.superior-label {
  font-size: 14px;
  opacity: 0.9;
}

.superior-user {
  display: flex;
  align-items: center;
  gap: 8px;
}

.superior-avatar {
  width: 24px;
  height: 24px;
  border-radius: 12px;
}

.superior-name {
  font-size: 14px;
}

.modify-btn {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  padding: 4px 8px;
  font-size: 12px;
}

/* 功能菜单 */
.function-menu {
  background-color: #fff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 15px;
}

.menu-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 25px;

  &:last-child {
    margin-bottom: 0;
  }
}

.menu-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.menu-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;

  &.blue {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
  }
  &.orange {
    background: linear-gradient(135deg, #fa709a, #fee140);
  }
  &.green {
    background: linear-gradient(135deg, #43e97b, #38f9d7);
  }
  &.teal {
    background: linear-gradient(135deg, #4ecdc4, #44a08d);
  }
  &.pink {
    background: linear-gradient(135deg, #f093fb, #f5576c);
  }
  &.blue-light {
    background: linear-gradient(135deg, #667eea, #764ba2);
  }
  &.red {
    background: linear-gradient(135deg, #f093fb, #f5576c);
  }
  &.yellow {
    background: linear-gradient(135deg, #ffecd2, #fcb69f);
  }
}

.icon-img {
  width: 24px;
  height: 24px;
  border-radius: 4px;
}

.menu-text {
  font-size: 14px;
  color: #333;
  text-align: center;
}

/* 统计区域 */
.stats-section,
.commission-section {
  background-color: #fff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 15px;
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.stats-title,
.title-text {
  font-size: 18px;
  color: #333;
  font-weight: bold;
}

.month-picker {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 5px 10px;
  background-color: #f8f9fa;
  border-radius: 15px;
}

.month-text {
  font-size: 14px;
  color: #666;
}

.stats-grid {
  display: flex;
  justify-content: space-around;
  margin-bottom: 25px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 24px;
  color: #333;
  font-weight: bold;
  margin-bottom: 5px;
  display: block;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

.detail-stats {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.detail-row {
  display: flex;
  gap: 15px;
}

.detail-item {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 12px;
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
}

.detail-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;

  &.purple {
    background-color: #722ed1;
  }
  &.green {
    background-color: #52c41a;
  }
  &.blue {
    background-color: #1890ff;
  }
  &.teal {
    background-color: #13c2c2;
  }
}

.detail-info {
  flex: 1;
}

.detail-title {
  font-size: 12px;
  color: #666;
  margin-bottom: 3px;
  display: block;
}

.detail-value {
  font-size: 16px;
  color: #333;
  font-weight: bold;
  margin-bottom: 3px;
  display: block;
}

.detail-desc {
  font-size: 10px;
  color: #999;
}

/* 返佣统计 */
.section-title {
  margin-bottom: 20px;
}

.commission-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.commission-item {
  width: calc(37% - 7.5px);
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
}

.commission-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  display: block;
}

.commission-value {
  font-size: 20px;
  color: #333;
  font-weight: bold;
}
</style>
