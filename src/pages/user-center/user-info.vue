<template>
  <view class="d-flex flex-column">
    <navigation-bar style="width: 100%; z-index: 99" backLevel :showIcon="true" :showText="true" text="个人信息" path="personal-center/personal-center"></navigation-bar>
    <view class="personal-main">
      <view class="item-box">
        <view class="head-pg-body">
          <text class="item-label">头像</text>
          <view class="head-avatar">
            <image v-if="wxUserInfo && wxUserInfo.headimgurl" :src="wxUserInfo.headimgurl" mode="aspectFill" class="avatar-image" />
            <open-data v-else type="userAvatarUrl" class="avatar-image"></open-data>
          </view>
        </view>
      </view>
      <view class="item-box">
        <text class="item-label">姓名</text>
        <text class="item-value">{{ userInfo.username || userInfo.nickname }}</text>
      </view>
      <view class="item-box">
        <text class="item-label">账号</text>
        <text class="item-value">{{ userInfo.accountId || "暂无" }}</text>
      </view>
      <view class="item-box">
        <text class="item-label">手机号</text>
        <text class="item-value">{{ userInfo.phone | dataApa(3, 4) }}</text>
      </view>
      <view class="item-box" @click="copyText(userInfo.id)">
        <text class="item-label">用户ID</text>
        <text class="item-value">{{ userInfo.id }}</text>
      </view>
      <view class="item-box" @click="copyText(userInfo.unionId || wxUserInfo.unionid)">
        <text class="item-label">unionId</text>
        <text class="item-value">{{ userInfo.unionId || wxUserInfo.unionid }}</text>
        <van-icon name="replay" color="#0B8DEB" size="32rpx" @click.stop.native="refreshUnionId" />
      </view>
      <view class="w-100">
        <van-button type="primary" block @click="cancelAccout()" class="btn-exit">注销账号</van-button>
      </view>
    </view>
  </view>
</template>

<script lang="ts">
import {Component, Watch} from "vue-property-decorator";
import Vue from "vue";
import dataApa from "@/filtres/data-apa-filter";
import NavigationBar from "@/components/common/NavigationBar.vue";
import {resetUnionId} from "@/api/system-user.api";
import {Code} from "@/constants/enum/code.enum";
Vue.filter("dataApa", dataApa);
@Component({
  name: "PersonalInfo",
  components: { NavigationBar },
})
export default class PersonalInfo extends Vue {
  isRefreshing: boolean = false;
  localWxUserInfo: any = null;

  onShow(): void {
    if (this.isRefreshing) {
      uni.showLoading({
        title: "正在刷新unionId",
      });
    }
  }

  mounted() {
    // 比对用户微信信息是否有更新
    uni.$on('wxUserInfoUpdated', (data: any) => {
      uni.setStorageSync("wxUserInfo", {...data, ...{ num: new Date().getTime() }});
      if (this.isRefreshing) {
        this.refreshUnionIdAsync(); // 立即执行刷新
      }
    });
  }

  get userInfo(): any {
    return uni.getStorageSync("userInfo");
  }
  get wxUserInfo(): any {
    return uni.getStorageSync("wxUserInfo");
  }

  cancelAccout(): void {
    uni.showModal({
      title: "是否确认注销账号？",
      success: (res) => {
        if (res.confirm) {
          uni.clearStorageSync();
          uni.reLaunch({
            url: "/pages/user-center/user-center",
          });
        }
      },
    });
  }

  async refreshUnionIdAsync(): void {
    uni.hideLoading();
    console.error("refreshUnionIdAsync", this.localWxUserInfo, this.wxUserInfo);
    if (this.wxUserInfo && this.wxUserInfo.unionid) {
      const { unionId } = this.localWxUserInfo;
      if (unionId !== this.wxUserInfo.unionid) {
        this.isRefreshing = false;
        // 进行unionId更新
        const params = {
          id: this.userInfo.id,
          userType: this.userInfo.userType,
          unionId: this.wxUserInfo.unionid,
        }
        const { code, msg } = await resetUnionId(params);
        if (code === Code.OK.code) {
          uni.showToast({
            title: "unionId更新成功",
            icon: "success",
            duration: 1500,
          });
          setTimeout(() => {
            uni.clearStorageSync();
            uni.reLaunch({
              url: "/pages/user-center/user-center",
            });
          }, 1000);
        } else {
          uni.showToast({
            title: msg,
            icon: "none",
            duration: 1500,
          });
          }
      } else {
        this.isRefreshing = false;
        uni.showToast({
          title: "unionId一致不需要更新",
          icon: "none",
        });
      }
    } else {
      this.isRefreshing = false;
      uni.showToast({
        title: "unionId未获取成功，请重新授权",
        icon: "none",
      });
    }
  }

  refreshUnionId(): void {
    if (!this.userInfo) return uni.showToast({ title: "请先登录后进行刷新", icon: "none" });
    // 跳转微信授权
    this.localWxUserInfo = JSON.parse(JSON.stringify(this.userInfo));
    this.isRefreshing = true;
    uni.navigateTo({ url: "/pages/auth/web-auth" });
  }

  /**
   * 复制文本到剪贴板
   * @param text 要复制的文本
   */
  copyText(text: string): void {
    if (!text) {
      uni.showToast({
        title: "无内容可复制",
        icon: "none",
      });
      return;
    }

    uni.setClipboardData({
      data: text,
      success: () => {
        uni.showToast({
          title: "复制成功",
          icon: "success",
          duration: 1500,
        });
      },
    });
  }
}
</script>

<style lang="scss" scoped>
.personal-main {
  background-color: #f5f5f5;
  height: 90vh; // 固定高度
  overflow-x: hidden;
  overflow-y: hidden; // 禁止垂直滚动
  padding: 20rpx;
  box-sizing: border-box;
  padding-bottom: 120rpx;
}
.item-box {
  display: flex;
  align-items: center;
  background-color: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);
  padding: 24rpx 30rpx;
  margin-bottom: 20rpx;
}
.item-label {
  font-size: 26rpx;
  color: #666666;
  width: 120rpx;
  flex-shrink: 0;
}
.item-value {
  font-size: 30rpx;
  color: #333333;
  word-break: break-all;
  flex: 1;
}
.head-pg-body {
  display: flex;
  align-items: center;
  width: 100%;
}
.head-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  overflow: hidden;
  border: 2rpx solid #eeeeee;
  background-color: #f0f0f0;
  margin-left: auto;
}
.btn-exit {
  position: fixed;
  bottom: 40rpx;
  left: 30rpx;
  right: 30rpx;
  z-index: 10;
  ::v-deep .van-button--normal {
    height: 88rpx;
    background: $standard-color;
    border-radius: 8rpx;
    border: none;
    font-size: 30rpx;
  }
}
.avatar-image {
  width: 100%; /* 图片宽度填满容器 */
  height: 100%; /* 图片高度填满容器 */
  display: block; /* 确保图片正确显示 */
}
</style>
