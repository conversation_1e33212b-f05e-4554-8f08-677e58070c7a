<template>
  <view class="mine-body" :style="{ height: tabsHeight + 'px' }">
    <!-- 头部用户信息区域 -->
    <view class="header-section">
      <view class="header-bg">
        <!-- 用户信息 -->
        <view class="user-profile">
          <view class="user-avatar" @click="goUserInfo">
            <image v-if="wxUserInfo && wxUserInfo.headimgurl" :src="wxUserInfo.headimgurl" mode="aspectFill" class="avatar-image" />
            <open-data v-else type="userAvatarUrl" class="avatar-image"></open-data>
          </view>
          <view class="user-details">
            <view class="user-name">微信用户</view>
            <view class="user-id" v-if="userInfo && userInfo.id">ID {{ userInfo.id }}</view>
            <view class="user-level">
              <text class="level-badge">{{ userLevel }}</text>
            </view>
          </view>

          <!-- 设置和消息图标 -->
          <view class="header-icons">
            <van-icon name="setting-o" size="20" color="#fff" @click="goSettings" />
            <view class="notification-icon" @click="goNotifications">
              <van-icon name="envelop-o" size="20" color="#fff" />
              <view v-if="unreadMessageCount > 0" class="message-badge">{{ unreadMessageCount > 99 ? "99+" : unreadMessageCount }}</view>
            </view>
          </view>
        </view>

        <!-- 数据统计区域 -->
        <view class="stats-section">
          <view class="stat-item" @click="goWallet">
            <view class="stat-number">{{ userBalance.toFixed(2) }}</view>
            <view class="stat-label">账户余额</view>
          </view>
          <view class="stat-item" @click="goFavorites">
            <view class="stat-number">{{ orderStats.favorites || 0 }}</view>
            <view class="stat-label">商品收藏</view>
          </view>
          <view class="stat-item" @click="goFootprints">
            <view class="stat-number">{{ orderStats.footprints || 0 }}</view>
            <view class="stat-label">我的足迹</view>
          </view>
          <view class="stat-item" @click="goPoints">
            <view class="stat-number">{{ orderStats.points || 5 }}</view>
            <view class="stat-label">我的积分</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 订单状态区域 -->
    <view class="order-status-section">
      <view class="order-status-item" @click="goOrderStatus('pending')">
        <van-icon name="credit-pay" size="24" color="#666" />
        <text class="order-status-text">待付款</text>
      </view>
      <view class="order-status-item" @click="goOrderStatus('shipped')">
        <van-icon name="gift-o" size="24" color="#666" />
        <text class="order-status-text">待发货</text>
      </view>
      <view class="order-status-item" @click="goOrderStatus('delivered')">
        <van-icon name="logistics" size="24" color="#666" />
        <text class="order-status-text">待收货</text>
      </view>
      <view class="order-status-item" @click="goOrderStatus('refund')">
        <van-icon name="after-sale" size="24" color="#666" />
        <text class="order-status-text">退款/售后</text>
      </view>
      <view class="order-status-item" @click="goAllOrders">
        <van-icon name="orders-o" size="24" color="#666" />
        <text class="order-status-text">我的订单</text>
      </view>
    </view>

    <!-- 营销横幅区域 -->
    <view class="banner-section">
      <swiper class="banner-swiper" indicator-dots indicator-color="rgba(255,255,255,0.5)" indicator-active-color="#fff" autoplay circular>
        <swiper-item v-for="(banner, index) in banners" :key="index" @click="goBannerDetail(banner)">
          <view class="banner-item" :style="{ backgroundColor: banner.bgColor }">
            <view class="banner-content">
              <text class="banner-title">{{ banner.title }}</text>
              <text class="banner-subtitle">{{ banner.subtitle }}</text>
            </view>
            <image :src="banner.image" class="banner-image" mode="aspectFit" />
          </view>
        </swiper-item>
      </swiper>
    </view>

    <!-- 功能菜单区域 -->
    <view class="menu-grid-section">
      <!-- 第一行 -->
      <view class="menu-row">
        <view class="menu-grid-item" @click="goFavorites">
          <van-icon name="star-o" size="24" color="#666" />
          <text class="menu-grid-text">我的收藏</text>
        </view>
        <view class="menu-grid-item" @click="goAddresses">
          <van-icon name="location-o" size="24" color="#666" />
          <text class="menu-grid-text">我的地址</text>
        </view>
        <view class="menu-grid-item" @click="goMyRedPacket">
          <van-icon name="balance-pay" size="24" color="#666" />
          <text class="menu-grid-text">我的奖励</text>
        </view>
        <view class="menu-grid-item" @click="goMembership">
          <van-icon name="diamond-o" size="24" color="#666" />
          <text class="menu-grid-text">我的会员</text>
        </view>
      </view>

      <!-- 第二行 -->
      <view class="menu-row">
        <view class="menu-grid-item" @click="goWallet">
          <van-icon name="balance-o" size="24" color="#666" />
          <text class="menu-grid-text">我的钱包</text>
        </view>
        <view class="menu-grid-item" @click="goCoupons" @longpress="refreshCouponCount">
          <view class="menu-icon-container">
            <van-icon name="coupon-o" size="24" color="#666" />
            <view v-if="couponCount > 0" class="badge">{{ couponCount }}</view>
          </view>
          <text class="menu-grid-text">我的卡券</text>
        </view>
        <view class="menu-grid-item" @click="goMessages">
          <van-icon name="chat-o" size="24" color="#666" />
          <text class="menu-grid-text">我的留言</text>
        </view>
        <view class="menu-grid-item" @click="goCheckIn">
          <van-icon name="completed" size="24" color="#666" />
          <text class="menu-grid-text">我的签到</text>
        </view>
      </view>

      <!-- 第三行 -->
      <view class="menu-row">
        <view class="menu-grid-item" @click="goCustomerService">
          <van-icon name="service-o" size="24" color="#666" />
          <text class="menu-grid-text">电话客服</text>
        </view>
        <view class="menu-grid-item" @click="goDistribution" v-if="isSalesFlag">
          <van-icon name="friends-o" size="24" color="#666" />
          <text class="menu-grid-text">我的分销</text>
        </view>
        <view class="menu-grid-item" @click="logout">
          <van-icon name="revoke" size="24" color="#666" />
          <text class="menu-grid-text">{{ isHasCache ? "退出登录" : "立即登录" }}</text>
        </view>
        <view class="menu-grid-item">
          <button open-type="contact" @contact="handleContact" class="contact-button-grid">
            <van-icon name="service-o" size="24" color="#666" />
            <text class="menu-grid-text">在线客服</text>
          </button>
        </view>
      </view>
    </view>
    <van-popup :show="showUserLogin" position="bottom" custom-style="overflow: scroll" round :z-index="999" :lock-scroll="true" :overlay="true" close-on-click-overlay @close="closeUserLogin">
      <view style="height: 35vh; padding: 24rpx">
        <view class="login-content-container">
          <view style="text-align: right; margin-bottom: 10rpx">
            <van-icon name="cross" size="20" @click="closeUserLogin" />
          </view>
          <view class="login-header">用户登录</view>
          <view>
            <view class="account-input-container">
              <van-field :value="form.account" :error-message="accountError" name="account" label="账号" placeholder="请输入账号" @input="handleInputAccount" />
            </view>
            <van-field :value="form.password" :error-message="passwordError" name="password" label="密码" type="password" @change="handleInputPassword" placeholder="请输入登录密码" />
          </view>
          <view class="submit-section">
            <van-button round block type="info" @click="handleSubmit"> 立即登录</van-button>
          </view>
        </view>
      </view>
    </van-popup>
    <!-- 自定义登录选择弹窗 -->
    <van-popup :show="showLoginOptions" position="center" round :z-index="999" :lock-scroll="true" :overlay="true" close-on-click-overlay @close="closeLoginOptions">
      <view style="padding: 24px; width: 80vw; max-width: 300px">
        <view style="text-align: right">
          <van-icon name="cross" size="20" @click="closeLoginOptions" />
        </view>
        <view style="font-size: 18px; font-weight: bold; text-align: center; margin-bottom: 16px"> 登录</view>
        <view style="text-align: center; margin-bottom: 24px"> 登录后可以查看课程</view>
        <view style="display: flex; flex-direction: column; gap: 12px">
          <van-button type="primary" block @click="handleQuickLogin"> 一键登录</van-button>
          <van-button type="default" block @click="handleAccountLogin"> 账号登录</van-button>
        </view>
      </view>
    </van-popup>
  </view>
</template>

<script lang="ts">
import Vue from "vue";
import { Component } from "vue-property-decorator";
import NavigationBar from "@/components/common/NavigationBar.vue";
import { AccountType } from "@/constants/enum/account-type.enum";
import { businessLogin, handleLogin } from "@/api/auth.api";
import { getUserCouponsStats, getUserStats, getFootprintCount } from "@/api/mall-extended.api";
import { Code } from "@/constants/enum/code.enum";
import { sm4 } from "sm-crypto";
import {getIndexData} from "@/api/app.api";
import {SYSTEM_ROLE_CONST} from "@/constants/enum/system-role.enum";

interface LoginForm {
  account: string;
  password: string;
}

/**
 * @Desc 个人中心
 */
@Component({
  name: "user-center",
  computed: {
    AccountType() {
      return AccountType;
    },
  },
  components: { NavigationBar },
})
export default class UserCenter extends Vue {
  userInfo = {};
  wxUserInfo = uni.getStorageSync("wxUserInfo");
  actualHeight = 0;
  // 登录选择弹窗变量
  showLoginOptions = false;
  showUserLogin = false;
  key = "7c4a8d09ca3762af61e59520943dc264"; // 16 字节的密钥
  form: LoginForm = {
    account: "",
    password: "",
  };
  accountError = "";
  passwordError = "";
  isJumpToAuth = false;

  // 商城数据统计
  orderStats = {
    total: 0,
    favorites: 0,
    footprints: 0,
    points: 0,
  };

  // 用户余额
  userBalance = 0;

  // 用户等级
  userLevel = "普通会员";

  // 优惠券数量
  couponCount = 0;

  // 用户统计数据
  userStats = {
    totalOrders: 0,
    totalAmount: 0,
    totalPoints: 0,
    memberLevel: "普通会员",
    favoriteCount: 0,
    footprintCount: 0,
  };

  // 加载状态
  statsLoading = false;

  // 未读消息数量
  unreadMessageCount = 5;

  // 营销横幅数据
  banners = [];

  onLoad() {}

  onShow() {
    this.userInfo = uni.getStorageSync("userInfo");
    this.loadUserData();
    this.loadCouponCount(); // 加载优惠券数量
    this.loadUserStats(); // 加载用户统计数据
    this.loadBanners(); // 加载营销横幅数据
    if (this.isJumpToAuth) {
      uni.showLoading({
        title: "登录中...",
      });
      // 比对用户微信信息是否有更新
      setTimeout(() => {
        uni.hideLoading();
        this.isJumpToAuth = false;
        this.userLogin();
      }, 2000);
    }
  }

  // 加载用户商城数据
  loadUserData() {
    // 基础数据初始化，具体数据通过loadUserStats加载
    this.orderStats = {
      total: 0,
      favorites: 0,
      footprints: 0,
      points: 0,
    };

    this.userBalance = 0;
    this.userLevel = "普通会员";
    this.unreadMessageCount = 0;
  }

  // 加载用户统计数据
  async loadUserStats() {
    const customerId = this.getUserId();
    if (!customerId) {
      console.log("用户中心 - 未获取到用户ID，跳过加载统计数据");
      return;
    }

    this.statsLoading = true;
    try {
      console.log("用户中心 - 开始加载用户统计数据, customerId:", customerId);

      // 并行加载用户统计和足迹数量
      const [userStatsResponse, footprintCountResponse] = await Promise.all([
        getUserStats(customerId),
        getFootprintCount(customerId)
      ]);

      console.log("用户中心 - 用户统计API响应:", userStatsResponse);
      console.log("用户中心 - 足迹数量API响应:", footprintCountResponse);

      // 处理用户统计数据
      if (userStatsResponse.code === Code.OK.code) {
        const stats = userStatsResponse.data || {};
        this.userStats = {
          totalOrders: stats.totalOrders || 0,
          totalAmount: stats.totalAmount || 0,
          totalPoints: stats.totalPoints || 0,
          memberLevel: stats.memberLevel || "普通会员",
          favoriteCount: stats.favoriteCount || 0,
          footprintCount: stats.footprintCount || 0,
        };

        // 更新显示数据
        this.orderStats.total = this.userStats.totalOrders;
        this.orderStats.favorites = this.userStats.favoriteCount;
        this.orderStats.points = this.userStats.totalPoints;
        this.userLevel = this.userStats.memberLevel;

        console.log("用户中心 - 更新用户统计数据:", this.userStats);
      }

      // 处理足迹数量数据
      if (footprintCountResponse.code === Code.OK.code) {
        const footprintCount = footprintCountResponse.data || 0;
        this.orderStats.footprints = footprintCount;
        this.userStats.footprintCount = footprintCount;
        console.log("用户中心 - 更新足迹数量:", footprintCount);
      }

    } catch (error) {
      console.error("用户中心 - 加载统计数据失败:", error);
    } finally {
      this.statsLoading = false;
    }
  }

  // 加载营销横幅数据
  async loadBanners() {
    try {
      const response = await getIndexData({index: 3});
      if (response.code === Code.OK.code) {
        if (response.data && Array.isArray(response.data) && response.data.length > 0) {
          this.banners = response.data.map(item => {
            return {
              title: item.title,
              subtitle: item.subText,
              image: item.smallImageUrl,
              bgColor: item.backgroundColor,
              url: item.linkUrl
            };
          })
        }
      } else {
        uni.showToast({
          title: response.msg || "获取横幅数据失败",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("用户中心 - 加载营销横幅数据失败:", error);
      uni.showToast({
        title: "加载营销横幅数据失败",
        icon: "none",
        duration: 1000,
      });
    }
  }

  // 加载优惠券数量
  async loadCouponCount() {
    try {
      const customerId = this.getUserId();
      if (!customerId) {
        console.log("用户中心 - 未获取到用户ID，跳过加载优惠券数量");
        return;
      }

      console.log("用户中心 - 开始加载优惠券数量, customerId:", customerId);
      const response = await getUserCouponsStats(customerId);
      console.log("用户中心 - 优惠券统计API响应:", response);

      if (response.code === Code.OK.code) {
        const stats = response.data || {};
        // 显示可使用的优惠券数量
        this.couponCount = stats.unused || 0;
        console.log("用户中心 - 更新优惠券数量:", this.couponCount);
      } else {
        console.error("用户中心 - 获取优惠券统计失败:", response.msg);
        // API失败时保持默认值0
        this.couponCount = 0;
      }
    } catch (error) {
      console.error("用户中心 - 加载优惠券数量异常:", error);
      // 异常时保持默认值0
      this.couponCount = 0;
    }
  }

  // 获取用户ID
  getUserId(): string {
    try {
      const userInfo = uni.getStorageSync("userInfo");
      if (userInfo && userInfo.id) {
        return String(userInfo.id);
      } else {
        // 如果没有用户信息，返回空字符串
        return "";
      }
    } catch (error) {
      console.error("获取用户信息失败:", error);
      return "";
    }
  }

  logout(): void {
    if (this.isHasCache) {
      // 清除存储并跳转
      uni.clearStorageSync();
      uni.showToast({
        title: "清除成功",
        icon: "success",
        duration: 1000,
      });
      uni.reLaunch({
        url: "/pages/user-center/user-center",
      });
    } else {
      // 立即登录
      this.toLogin();
    }
  }
  /**
   * 获取tabs的高度
   */
  get tabsHeight() {
    return uni.getSystemInfoSync().windowHeight;
  }

  get actualBtnHeight() {
    return this.actualHeight + 10;
  }

  get isHasCache() {
    return uni.getStorageSync("userInfo") && uni.getStorageSync("token");
  }

  get isSalesFlag() {
    const user = this.userInfo;
    const userType = user ? String(user.userType) : "";
    // 判断是否是销售角色
    return userType === AccountType.PERSONAL.code;
  }

  goAssets() {
    uni.navigateTo({
      url: "/pages/columnManagement/column-index",
    });
  }

  goCourses() {
    uni.navigateTo({
      url: "/pages/history/history",
    });
  }

  goAboutUs() {
    uni.navigateTo({
      url: "/pages/about/about",
    });
  }
  handleContact(e: any) {
    console.error("contact", e);
  }

  goContactUs() {
    uni.navigateTo({
      url: "/pages/contact/contact",
    });
  }
  goRedPacket() {
    uni.navigateTo({
      url: "/pages/redpacket/redpacket",
    });
  }

  // 商城相关页面跳转方法
  goSettings() {
    uni.navigateTo({
      url: "/pages/settings/settings",
    });
  }

  goNotifications() {
    uni.navigateTo({
      url: "/pages/notifications/notifications",
    });
  }

  goWallet() {
    uni.navigateTo({
      url: "/pages/wallet/wallet",
    });
  }

  goFavorites() {
    uni.navigateTo({
      url: "/pages/favorites/favorites",
    });
  }

  goFootprints() {
    uni.navigateTo({
      url: "/pages/footprints/footprints",
    });
  }

  goPoints() {
    uni.navigateTo({
      url: "/pages/points/points",
    });
  }

  goOrderStatus(status: string) {
    const statusMap = {
      pending: "pending",
      shipped: "processing",
      delivered: "shipped",
      refund: "refund",
    };
    uni.navigateTo({
      url: `/pages/orders/orders?status=${statusMap[status] || status}`,
    });
  }

  goAllOrders() {
    uni.navigateTo({
      url: "/pages/orders/orders",
    });
  }

  goAddresses() {
    uni.navigateTo({
      url: "/pages/address/address",
    });
  }

  goDistribution() {
    uni.navigateTo({
      url: "/pages/distribution/distribution"
    });
  }

  goMyRedPacket() {
    uni.navigateTo({
      url: "/pages/redpacket/redpacket"
    });
  }

  goMembership() {
    uni.navigateTo({
      url: "/pages/membership/membership",
    });
  }

  async goCoupons() {
    // 跳转前刷新优惠券数量
    await this.loadCouponCount();
    uni.navigateTo({
      url: "/pages/coupons/coupons",
    });
  }

  // 长按刷新优惠券数量
  async refreshCouponCount() {
    uni.showToast({
      title: "正在刷新优惠券数量...",
      icon: "loading",
      duration: 1000
    });
    await this.loadCouponCount();
    uni.showToast({
      title: "刷新完成",
      icon: "success",
      duration: 1000
    });
  }

  goMessages() {
    uni.navigateTo({
      url: "/pages/messages/messages",
    });
  }

  goCashback() {
    uni.showToast({ title: "优惠返现功能开发中", icon: "none" });
  }

  goInvoices() {
    uni.showToast({ title: "我的发票功能开发中", icon: "none" });
  }

  goCheckIn() {
    uni.navigateTo({
      url: "/pages/checkin/checkin",
    });
  }

  // 快捷入口方法
  goCustomerService() {
    uni.makePhoneCall({
      phoneNumber: "400-1805185",
    });
  }

  goRedPacket() {
    uni.navigateTo({
      url: "/pages/redpacket/redpacket",
    });
  }

  goMore() {
    uni.showToast({ title: "更多功能开发中", icon: "none" });
  }

  // 横幅点击事件
  goBannerDetail(banner: any) {
    if (banner.id) {
      uni.navigateTo({
        url: `/pages/product/product-detail?id=${banner.id}`,
      });
    }
  }
  // 登录
  async userLogin() {
    const userInfo = await handleLogin();
    console.error("userInfo:", userInfo);
    if (userInfo) {
      uni.reLaunch({
        url: "/pages/user-center/user-center",
      });
    }
  }
  userAccountLogin() {
    this.showUserLogin = true;
  }

  toLogin() {
    // 显示自定义登录选择弹窗
    this.showLoginOptions = true;
  }
  goUserInfo() {
    uni.navigateTo({
      url: "/pages/user-center/user-info",
    });
  }

  // 账号登录
  handleAccountLogin() {
    this.showLoginOptions = false;
    this.userAccountLogin();
  }

  // 一键登录
  handleQuickLogin() {
    this.showLoginOptions = false;
    setTimeout(() => {
      this.getLoginInfo();
    }, 100);
  }

  /**
   * 登录信息获取
   */
  getLoginInfo() {
    uni.clearStorageSync();
    if (!uni.getStorageSync("wxUserInfo")) {
      this.isJumpToAuth = true;
      // 跳转微信授权
      uni.navigateTo({ url: "/pages/auth/web-auth" });
    } else {
      this.userLogin();
    }
  }

  // 关闭登录选择弹窗
  closeLoginOptions() {
    this.showLoginOptions = false;
  }

  // 关闭账号登录弹窗
  closeUserLogin() {
    this.showUserLogin = false;
  }
  handleInputAccount(e: any) {
    this.form.account = e.detail;
    this.accountError = "";
  }

  handleInputPassword(e: any) {
    this.form.password = e.detail;
    this.passwordError = "";
  }
  // 提交登录
  async handleSubmit() {
    if (!this.form.account) {
      this.accountError = "请输入账号";
      return;
    }
    if (!this.form.password) {
      this.passwordError = "请输入密码";
      return;
    }
    try {
      uni.showLoading({ title: "登录中..." });
      const encryptResult = this.encryptData(this.form.password);
      let params = {};
      try {
        params = {
          encryptKey: encryptResult.encryptKey,
          encryptInitIv: encryptResult.encryptInitIv,
          encryptData: encryptResult.encryptData,
          accountId: this.form.account,
        };
      } catch (e) {
        uni.hideLoading();
        uni.showToast({ title: "请检查密码是否包含中文符号", icon: "none" });
        return;
      }
      const res = await businessLogin(params);
      if (res.code !== Code.OK.code) return uni.showToast({ title: res.msg || "登录失败", icon: "none" });
      // 登录成功
      this.showUserLogin = false;
      this.userInfo = res.data.userInfoResponse;
      await this.userLogin();
    } catch (error) {
      uni.showToast({ title: "登录失败", icon: "none" });
    } finally {
      uni.hideLoading();
    }
  }
  encryptData(jsonStr: any) {
    let encryptData = {};
    try {
      // 设置密钥和初始向量，开始对数据做sm4加密
      const sm4Key = this.randomStr(16);
      const sm4KeyArr = this.strToOneByteArr(sm4Key);
      const iv = this.randomStr(16);
      const sm4InitIvArr = this.strToOneByteArr(iv);
      // 将对象数据转为json数据加密
      const sm4Data = JSON.stringify(jsonStr);
      let encryptSM4 = sm4.encrypt(sm4Data, sm4KeyArr, {
        mode: "cbc",
        iv: sm4InitIvArr,
      });
      encryptSM4 = `{SM4}${encryptSM4}`;
      // 加密sm4的初始秘钥与向量，上送给服务器时需要加’04‘头，如果是服务器解密需要把返回的04头去掉
      encryptData = {
        encryptInitIv: iv,
        encryptKey: sm4Key,
        encryptData: encryptSM4,
      };
    } catch (e) {
      console.error(e);
      uni.showToast({ title: "数据加密失败" });
    }
    return encryptData;
  }

  randomStr(length: number) {
    const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    let result = "";
    for (let item = 0; item < length; item++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * @description: 将字符串转为字节数组
   */
  strToOneByteArr(str: string) {
    const arr = [];
    for (let i = 0, len = str.length; i < len; i++) {
      arr.push(str.charCodeAt(i));
    }
    return arr;
  }
}
</script>

<style lang="scss" scoped>
.login-content-container {
  display: flex;
  flex-direction: column;
}
.login-header {
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  padding: 10px 0;
  background: #fff;
  position: relative;
  z-index: 10;
}
.account-input-container {
  margin-bottom: 20px;
}
.submit-section {
  margin-top: 30px;
}
/* 头部区域样式 */
.header-section {
  position: relative;
  margin-bottom: 20px;
}

.header-bg {
  background: $standard-color;
  padding: 20px;
  padding-top: 100px;
  position: relative;
}

.notification-icon {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.message-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: #ff4444;
  color: #fff;
  font-size: 8px;
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 10px;
  height: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

.user-profile {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  position: relative;
}

.header-icons {
  position: absolute;
  right: 0;
  display: flex;
  gap: 15px;
  z-index: 10;
}

.user-avatar {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid rgba(255, 255, 255, 0.3);
  margin-right: 15px;
}

.avatar-image {
  width: 100%;
  height: 100%;
  display: block;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 20px;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 5px;
}

.user-id {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  background-color: rgba(255, 255, 255, 0.2);
  padding: 4px 8px;
  border-radius: 12px;
  display: inline-block;
  margin-bottom: 8px;
}

.user-level {
  display: flex;
  align-items: center;
  gap: 8px;
}

.level-badge {
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  color: #333;
  font-size: 12px;
  font-weight: bold;
  padding: 4px 8px;
  border-radius: 10px;
}

.level-text {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
}

/* 数据统计区域 */
.stats-section {
  display: flex;
  justify-content: space-between;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px 10px;
}

.stat-item {
  flex: 1;
  text-align: center;
  color: #ffffff;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 12px;
  opacity: 0.9;
}

/* 营销横幅区域 */
.banner-section {
  margin: 0 15px 20px;
  border-radius: 12px;
  overflow: hidden;
  height: 120px;
}

.banner-swiper {
  height: 100%;
}

.banner-item {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  position: relative;
  overflow: hidden;
}

.banner-content {
  flex: 1;
  z-index: 2;
}

.banner-title {
  font-size: 18px;
  color: #fff;
  font-weight: bold;
  margin-bottom: 5px;
  display: block;
}

.banner-subtitle {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  display: block;
}

.banner-image {
  width: 80px;
  height: 80px;
  z-index: 1;
}

/* 订单状态区域 */
.order-status-section {
  background-color: #ffffff;
  margin: 0 15px 20px;
  border-radius: 12px;
  padding: 20px 0;
  display: flex;
  justify-content: space-around;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.order-status-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  padding: 10px 5px;
}

.order-status-text {
  font-size: 12px;
  color: #666;
  margin-top: 8px;
  text-align: center;
}

/* 功能菜单网格区域 */
.menu-grid-section {
  background-color: #ffffff;
  margin: 0 15px;
  border-radius: 12px;
  padding: 20px 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.menu-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 25px;
}

.menu-row:last-child {
  margin-bottom: 0;
}

.menu-grid-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px 5px;
}

.menu-grid-text {
  font-size: 12px;
  color: #333;
  margin-top: 8px;
  text-align: center;
}

.contact-button-grid {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;

  &::after {
    display: none;
  }
}

.menu-icon-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: #ff4444;
  color: #fff;
  font-size: 10px;
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

/* 页面整体样式 */
.mine-body {
  position: relative;
  background-color: #f5f5f5;
}
</style>
<style lang="scss">
.page {
  background-color: #ecf0fd;
}
</style>
