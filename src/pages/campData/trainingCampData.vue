<template>
  <view class="content" :style="{ height: contentHeight  + 'px', paddingTop: topHeight + 6 + 'px' }">
    <NavigationBar :showText="true" :showIcon="true" text="营期数据" :backLevel="true" />
    <van-tabs :active="active" @change="onTabChanged">
      <van-tab title="营期数据" name="2"></van-tab>
      <van-tab title="营期会员数据" name="1"></van-tab>
    </van-tabs>
    <view class="camp-period-data" ref="camp-period-data" id="camp-period-data">
      <view class="header">
        <view>{{ campName }}</view>
      </view>

      <view class="card-container">
        <view v-for="(data, index) in formattedData" :key="index" class="card">
          <view class="card-title">{{ data.title }}</view>
          <view class="card-value">{{ data.desc }}</view>
        </view>
      </view>
    </view>
    <view class="search-wrapper">
      <view v-show="isSearchExpanded" class="search-panel">
        <van-field v-if="active === '2'" :value="formData.courseId" label="课程名称" readonly is-link placeholder="请选择课程名称" @click-input="openCoursePicker" />

        <view v-if="active === '1'">
          <van-notice-bar color="#128FEB" background="#ecf9ff" wrapable text="重置或选择课程名称后，时间筛选失效；若需按时间筛选，请重新设置。" />
          <van-field :value="formData.courseName" label="课程名称" readonly is-link placeholder="请选择课程名称" @click-input="openCoursePicker" />
          <van-field :value="formData.salesGroupId" :disabled="isOther" label="销售组" placeholder="请选择销售组" is-link readonly @click-input="openSalesGroupPicker" />
          <van-field v-model="formData.salesId" :disabled="isOther" label="销售" placeholder="请选择销售" is-link readonly @click-input="openSalesPersonPicker" />
          <van-field v-model="timeRangeText" label="活跃时间" placeholder="选择时间范围" is-link readonly @click-input="showTimePicker = true" />
          <van-field v-model="formData.isFlag" label="课程状态" placeholder="选择课程状态" is-link readonly @click-input="showStatusPicker = true" />
        </view>
        <view class="search-btn-wrapper">
          <van-button size="small" color="#0289ea" @click="handleSearch" class="search-btn"> 确认查询 </van-button>
          <van-button size="small" color="#0289ea" @click="resetFormData" class="cancel-btn"> 重置 </van-button>
        </view>
      </view>
      <!-- 选择器组件 -->
      <van-popup :show="showSalesGroupPicker" position="bottom">
        <van-picker show-toolbar title="选择销售组" :columns="salesGroupOptions" @confirm="onSalesGroupConfirm" @cancel="showSalesGroupPicker = false" />
      </van-popup>
      <van-popup :show="showSalesPersonPicker" position="bottom">
        <van-picker show-toolbar title="选择销售" :columns="salesOptions" @confirm="onSalesPersonConfirm" @cancel="showSalesPersonPicker = false" />
      </van-popup>
      <van-popup :show="showCoursePicker" position="bottom">
        <van-picker show-toolbar title="选择课程" :columns="options" @confirm="onCourseConfirm" @cancel="showCoursePicker = false" />
      </van-popup>
      <van-popup :show="showStatusPicker" position="bottom">
        <van-picker show-toolbar title="选择课程状态" :columns="statusOptions" @confirm="onStatusConfirm" @cancel="showStatusPicker = false" />
      </van-popup>
      <van-popup :show="showTimePicker" position="bottom">
        <van-datetime-picker confirm-button-text=" " cancel-button-text=" " :value="startDate" type="datetime" title="开始时间" :min-date="minDate" @input="startDateChange" />
        <van-datetime-picker confirm-button-text=" " cancel-button-text=" " :value="endDate" type="datetime" title="结束时间" :min-date="startDate || minDate" :max-date="maxDate" @input="endDateChange" />
        <view class="search-btn-wrapper">
          <van-button size="normal" color="#0289ea" class="search-btn" @click="confirmRange">确认</van-button>
          <van-button size="normal" color="#0289ea" class="cancel-btn" @click="showTimePicker = false">取消</van-button>
        </view>
      </van-popup>
    </view>
    <search-data @click="isSearchExpanded = !isSearchExpanded"></search-data>
    <!--  数据展示整体  -->
    <van-loading color="#0289ea" size="45px" class="list-loading" v-if="dataLoading" vertical>加载中...</van-loading>
    <view class="camp-data-wrapper" :style="{ height: `${tabsHeight || 500}px` }">
      <view class="scroll-wrapper">
        <van-empty v-if="active === '2' && data.length === 0 && !dataLoading" image="error" description="暂无数据" />
        <view v-if="active === '2' && data.length !== 0">
          <view v-for="item in data" :key="item.id">
            <info-card :campData="item" :label="campLabel"></info-card>
          </view>
        </view>
        <van-empty v-if="active === '1' && data1.length === 0 && !dataLoading" image="error" description="暂无数据" />
        <view v-if="active === '1' && data1.length !== 0">
          <view v-for="item in data1" :key="item.id">
            <info-card :campData="item" :label="campDataLabel"></info-card>
          </view>
        </view>
      </view>
      <view class="div-bottom" v-if="(active === '1' && data1.length !== 0) || (active === '2' && data.length !== 0)">
        <custom-pagination :currentPage="currentPage" :total="totalData" @pageChange="pageChange"></custom-pagination>
      </view>
    </view>
  </view>
</template>
<script lang="ts">
import Vue from "vue";
import { Component } from "vue-property-decorator";
import { listByCompanyId } from "@/api/sales-group.api";
import { queryCampCourseListData, queryCampPeriodData, queryCampPeriodTableData, queryCustomerCourseListData, queryCustomerDataTableData } from "@/api/camp-period.api";
import CustomPagination from "@/components/common/Pagination.vue";
import NavigationBar from "@/components/common/NavigationBar.vue";
import InfoCard from "@/components/common/InfoCard.vue";
import { queryUserByPage } from "@/api/system-user.api";
import SearchData from "@/components/flaot-button/SerchData.vue";
import { SYSTEM_ROLE_CONST } from "@/constants/enum/system-role.enum";
import {getLayoutHeights} from "@/utils/get-page-height.util";

@Component({
  name: "TrainingCampData",
  components: { InfoCard, NavigationBar, CustomPagination, SearchData },
})
export default class TrainingCampData extends Vue {
  campPeriodData = {
    campPeriodTotalMembers: 0,
    campPeriodLastDayMembers: 0,
    campPeriodLastDayNewMembers: 0,
    campPeriodLastDayLoginMembers: 0,
    campPeriodLastDayLoginPercentMembers: 0,
    campPeriodToDayNewMembers: 0,
    campPeriodTotalRedPacket: 0,
    campPeriodAmountRedPacket: 0,
  };
  campCourseData = {
    countCustomerCourse: 0,
    countCustomerComplete: 0,
    countCustomerRedPacket: 0,
    amountCustomerRedPacket: 0,
  };
  formData = {
    courseId: "",
    courseName: "",
    columnId: "",
    campPeriodId: "",
    companyId: "",
    startDate: "",
    endDate: "",
    salesGroupId: "",
    salesId: "",
    isFlag: "",
  };
  active = "2";
  options = [];
  campLabel = ["观看人数:", "完播人数:", "完播率:", "红包数量(个):", "红包金额(元):"];
  campDataLabel = ["姓名", "观看次数", "观看课节数", "完播课节数", "红包领取次数", "首次加入时间", "观看时长", "完播时间"];
  salesGroupOptions = [];
  salesOptions = []; // 销售
  salesId = "";
  salesGroupId = "";
  isFlag = "";
  data = [];
  data1 = [];
  startDate = new Date().setHours(0, 0, 0, 0);
  endDate = new Date().setHours(23, 59, 59, 999);
  minDate = new Date(2024, 0, 1).getTime();
  maxDate = new Date().getTime();
  restTime = false;
  pagination = {
    current: 1,
    pageSize: 10,
    total: 0,
  };
  pagination1 = {
    current: 1,
    pageSize: 10,
    total: 0,
  };
  isSearchExpanded = false;
  searchParams = {
    courseName: "",
    salesGroup: "",
    salesPerson: "",
    timeRange: [],
    courseStatus: "",
  };
  showSalesGroupPicker = false;
  showCoursePicker = false;
  showSalesPersonPicker = false;
  showStatusPicker = false;
  showTimePicker = false;
  statusOptions = [
    { text: "未到课", value: "0" },
    { text: "到课", value: "1" },
    { text: "完播", value: "2" },
    { text: "已到课未完播", value: "3" },
  ];
  value2 = "";
  valueCamp = "";
  dataLoading = false;
  loadingSearch = false;
  companyId = "";
  campPeriodId = "";
  columnId = "";
  campName = "";
  pageHeight = 0;
  topHeight = 0;
  contentHeight = 0;
  actualCampHeight = 0;
  campDataTimer = 0;
  currentUserSalesId = "";
  currentUserSalesGroupId = "";
  isOther = false;

  async onLoad(options: any) {
    await this.checkCampType(options);
    await this.remoteMethod();
    await this.getCampPeriodData();
    await this.getCampPeriodTableData();
    await this.getCustomerTableData();
    await this.getCustomerCourseData();
  }

  onShow() {
    this.initSizes();
    const {topHeight, contentHeight} = getLayoutHeights();
    this.topHeight = topHeight;
    this.contentHeight = contentHeight;
  }

  get currentPage() {
    return this.active === "2" ? this.pagination.current : this.pagination1.current;
  }

  get totalData() {
    return this.active === "2" ? this.pagination.total : this.pagination1.total;
  }

  get timeRangeText() {
    if (!this.startDate || !this.endDate) return "";
    return `${this.formatDate(this.startDate)} ~ ${this.formatDate(this.endDate)}`;
  }

  get formattedData() {
    const campPeriodData = [
      { title: "营期总会员人数", desc: `${this.campPeriodData.campPeriodTotalMembers}人` },
      { title: "昨日营期总会员人数", desc: `${this.campPeriodData.campPeriodLastDayMembers}人` },
      { title: "昨日新增会员", desc: `${this.campPeriodData.campPeriodLastDayNewMembers}人` },
      { title: "昨日上线人数", desc: `${this.campPeriodData.campPeriodLastDayLoginMembers}人` },
      { title: "昨日上线率", desc: `${this.campPeriodData.campPeriodLastDayLoginPercentMembers}%` },
      { title: "发放红包（个）", desc: `${this.campPeriodData.campPeriodTotalRedPacket}个` },
      { title: "发放红包金额（元）", desc: `${this.campPeriodData.campPeriodAmountRedPacket}元` },
    ];
    // 会员数据
    const campCourseData = [
      { title: "营期总会员人数", desc: `${this.campPeriodData.campPeriodTotalMembers}人` },
      { title: "今日新增会员", desc: `${this.campPeriodData.campPeriodLastDayMembers}人` },
      { title: "观看次数", desc: `${this.campCourseData.countCustomerCourse}次` },
      { title: "完播次数", desc: `${this.campCourseData.countCustomerComplete}次` },
      { title: "发放红包（个）", desc: `${this.campCourseData.countCustomerRedPacket}个` },
      { title: "发放红包金额（元）", desc: `${this.campCourseData.amountCustomerRedPacket}元` },
    ];
    return this.active === "2" ? campPeriodData : campCourseData;
  }

  /**
   * 获取卡片的高度
   */
  get tabsHeight() {
    if (!this.pageHeight || !this.topHeight) return 0;
    // 计算tab的高度 = 页面高度 - 导航栏高度 - 顶部状态栏高度 - tab栏高度 - 底部分页按钮高度 - 营期卡片高度
    return this.pageHeight - this.topHeight - 44 - 50 - this.actualCampHeight; // 使用实际测量的高度
  }

  async initSizes() {
    // 同步获取页面高度
    this.pageHeight = uni.getSystemInfoSync().windowHeight;

    // 异步获取导航高度
    const menuButtonObject = await new Promise((resolve) => resolve(uni.getMenuButtonBoundingClientRect()));
    const systemInfo = await new Promise((resolve) => uni.getSystemInfo({ success: resolve }));

    this.topHeight = menuButtonObject.height + (menuButtonObject.top - systemInfo.statusBarHeight) * 2 + systemInfo.statusBarHeight;

    this.campDataTimer = setInterval(async () => {
      const rect = await new Promise((resolve) => {
        uni
          .createSelectorQuery()
          .in(this) // 组件作用域
          .select("#camp-period-data")
          .boundingClientRect(resolve)
          .exec();
      });

      if (rect && rect.height > 0) {
        this.actualCampHeight = rect.height;
      }
    }, 500);
  }

  async checkCampType(options: any) {
    this.companyId = options.companyId;
    this.campPeriodId = options.campPeriodId;
    this.columnId = options.columnId;
    this.campName = options.name;
    const user = uni.getStorageSync("userInfo");
    this.currentUserSalesId = user.id;
    this.currentUserSalesGroupId = user.salesGroupId;
    const roleType = user ? String(user.roleType) : "";
    // 判断是否是销售角色
    this.isOther = roleType === SYSTEM_ROLE_CONST.OTHER.value;
    await this.getSalesGroupList(this.companyId);
  }

  confirmRange() {
    this.showTimePicker = false;
    this.restTime = false;
    this.searchParams.timeRange = [this.startDate, this.endDate];
  }

  startDateChange(e: any) {
    this.startDate = e.detail;
  }

  endDateChange(e: any) {
    this.endDate = e.detail;
  }

  onTabChanged(e: any): void {
    this.active = e.detail.name;
  }

  openCoursePicker() {
    this.showCoursePicker = true;
  }

  openSalesGroupPicker() {
    if (this.isOther) return;
    this.showSalesGroupPicker = true;
  }
  openSalesPersonPicker() {
    if (this.isOther) return;
    this.showSalesPersonPicker = true;
  }

  onCourseConfirm(e: any) {
    if (this.active === "2") {
      this.formData.courseId = e.detail.value.text;
      this.value2 = e.detail.value.value;
    } else {
      this.formData.courseName = e.detail.value.text;
      this.valueCamp = e.detail.value.value;
    }
    this.showCoursePicker = false;
  }

  onStatusConfirm(e: any) {
    this.formData.isFlag = e.detail.value.text;
    this.isFlag = e.detail.value.value;
    this.showStatusPicker = false;
  }

  onSalesGroupConfirm(e: any) {
    this.formData.salesId = "";
    this.formData.salesGroupId = e.detail.value.text;
    this.salesGroupId = e.detail.value.value;
    this.getSalesList(e.detail.value.value);
    this.showSalesGroupPicker = false;
  }

  onSalesPersonConfirm(e: any) {
    this.formData.salesId = e.detail.value.text;
    this.salesId = e.detail.value.value;
    this.showSalesPersonPicker = false;
  }

  async getSalesList(salesGroupId: string) {
    this.formData.salesId = "";
    this.salesOptions = [];
    const res = await queryUserByPage({
      current: 1,
      size: 100,
      companyId: this.companyId || "",
      salesGroupId,
      columnId: this.columnId,
      status: 1, // 启用状态
      auditStatus: 2, // 审核通过状态
    });
    const { code, data, msg } = res;
    if (code === 0 && data) {
      this.salesOptions = data.records.map((item) => ({
        text: item.username,
        value: item.id,
      }));
    } else {
      uni.showToast({ title: msg, icon: "none" });
    }
  }

  handleSearch() {
    if (this.active === "2") {
      this.pagination.current = 1;
      this.getCampPeriodTableData();
      this.isSearchExpanded = false;
    } else {
      this.pagination1.current = 1;
      this.restTime = false;
      this.getCustomerTableData();
      this.getCustomerCourseData();
      this.isSearchExpanded = false;
    }
  }

  resetFormData() {
    if (this.active === "2") {
      this.pagination.current = 1;
      this.value2 = "";
      this.formData.courseId = "";
      this.isSearchExpanded = false;
      this.getCampPeriodTableData();
    } else {
      this.restTime = true;
      this.pagination1.current = 1;
      this.valueCamp = "";
      this.formData = {
        courseName: "",
        startDate: "",
        endDate: "",
        salesGroupId: "",
        salesId: "",
        isFlag: "",
      };
      this.startDate = new Date().setHours(0, 0, 0, 0);
      this.endDate = new Date().setHours(23, 59, 59, 999);
      this.isFlag = "";
      this.salesGroupId = "";
      this.salesId = "";
      this.getCustomerTableData();
      this.getCustomerCourseData();
      this.isSearchExpanded = false;
    }
  }

  async getSalesGroupList(value: string) {
    const params = {
      companyId: value,
    };
    const { code, data } = await listByCompanyId(params);
    if (code === 0 && data) {
      this.salesGroupOptions = data.map((item) => ({
        text: item.salesGroupName,
        value: item.id,
      }));
    }
  }

  async remoteMethod() {
    // console.log('search', search);
    if (!this.companyId) return;
    this.loadingSearch = true; // 开启加载状态
    try {
      const { code, data, msg } = await queryCampCourseListData({
        companyId: this.companyId,
        campPeriodId: this.campPeriodId,
      });

      if (code === 0) {
        this.options = data.map((item: any) => {
          return { text: item.label, value: item.value };
        });
      } else {
        uni.showToast({ title: msg, icon: "none" });
      }
    } catch (error) {
      uni.showToast({ title: "数据加载异常", icon: "none" });
    } finally {
      this.loadingSearch = false; // 关闭加载状态
    }
  }

  async getCampPeriodData() {
    if (!this.companyId) return;
    const { code, data, msg } = await queryCampPeriodData({
      companyId: this.companyId,
      campPeriodId: this.campPeriodId,
    });
    if (code === 0) {
      // 正确获取数组中的第一个对象
      const result = data[0];

      // 转换数据类型并移除单位
      this.campPeriodData = {
        campPeriodTotalMembers: parseFloat(result.campPeriodTotalMembers),
        campPeriodLastDayMembers: parseFloat(result.campPeriodLastDayMembers),
        campPeriodLastDayNewMembers: parseFloat(result.campPeriodLastDayNewMembers),
        campPeriodLastDayLoginMembers: parseFloat(result.campPeriodLastDayLoginMembers),
        campPeriodLastDayLoginPercentMembers: parseFloat(result.campPeriodLastDayLoginPercentMembers),
        campPeriodTotalRedPacket: parseFloat(result.campPeriodTotalRedPacket),
        campPeriodAmountRedPacket: parseFloat(result.campPeriodAmountRedPacket),
      };
    } else {
      uni.showToast({ title: msg, icon: "none" });
    }
  }

  /**
   * 获取营期数据
   */
  async getCampPeriodTableData() {
    if (!this.companyId) return;

    this.dataLoading = true; // 开启加载状态
    this.data = []; // 清空数据
    try {
      const { code, data, msg } = await queryCampPeriodTableData({
        companyId: this.companyId,
        campPeriodId: this.campPeriodId,
        courseId: this.value2,
        pageNum: this.pagination.current, // 传递当前页码
        pageSize: this.pagination.pageSize, // 传递每页条数
      });

      if (code === 0) {
        // this.data = data.records;
        const data1 = [];
        data.records.forEach((item: any) => {
          let newItem = {
            id: item.courseId,
            title: item.courseName,
            content: [item.viewCount, item.completeCount, item.completionRate, item.redPacketCount, item.redPacketAmount],
          };
          data1.push(newItem);
        });
        this.data = data1;

        this.pagination = {
          ...this.pagination,
          total: parseInt(data.total), // 确保转换为数字类型
        };
      } else {
        uni.showToast({ title: msg, icon: "none" });
      }
    } catch (error) {
      // console.error("表格数据加载失败:", error);
      uni.showToast({ title: "数据加载异常", icon: "none" });
    } finally {
      this.dataLoading = false; // 关闭加载状态
    }
  }

  /**
   * 获取会员数据
   */
  async getCustomerTableData() {
    if (!this.companyId) return;
    this.dataLoading = true; // 开启加载状态
    this.data1 = []; // 清空数据
    try {
      const { code, data, msg } = await queryCustomerDataTableData({
        companyId: this.companyId,
        campPeriodId: this.campPeriodId,
        columnId: this.columnId,
        courseId: this.valueCamp,
        // salesGroupId: this.salesGroupId,
        salesGroupId: "",
        salesId: !this.isOther ? this.salesId : this.currentUserSalesId,
        startDate: this.restTime || this.valueCamp ? "" : this.startDate ? this.formatDate(this.startDate) : "",
        isFlag: this.isFlag,
        endDate: this.restTime || this.valueCamp ? "" : this.endDate ? this.formatDate(this.endDate) : "",
        pageNum: this.pagination1.current, // 传递当前页码
        pageSize: this.pagination1.pageSize, // 传递每页条数
      });

      if (code === 0) {
        // this.data1 = data.records;
        const data1 = [];
        data.records.forEach((item: any) => {
          let newItem = {
            id: item.memberId,
            title: item.nickname, // 姓名
            status: item.statusDesc, // 状态
            salesGroup: item.salesMan, // 销售组
            icon: item.avatarUrl, // 头像 "https://huaxiacomp.cn/avatars/588857309011513413.jpg", //
            content: [item.realName, item.videoViewCount, item.viewCourseCount, item.completeCourseCount, item.redPacketCount, item.createdAt, item.viewCourseTime, item.completeCourseTime],
          };
          data1.push(newItem);
        });
        this.data1 = data1;
        this.pagination1 = {
          ...this.pagination1,
          total: parseInt(data.total), // 确保转换为数字类型
        };
      } else {
        uni.showToast({ title: msg, icon: "none" });
      }
    } catch (error) {
      // console.error("表格数据加载失败:", error);
      uni.showToast({ title: "数据加载异常", icon: "none" });
    } finally {
      this.dataLoading = false; // 关闭加载状态
    }
  }

  formatDate(timestamp: any) {
    const pad = (n: number) => n.toString().padStart(2, "0");
    const date = new Date(timestamp);

    return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())} ` + `${pad(date.getHours())}:${pad(date.getMinutes())}:${pad(date.getSeconds())}`;
  }

  async getCustomerCourseData() {
    if (!this.companyId) return;
    this.dataLoading = true; // 开启加载状态
    try {
      const { code, data, msg } = await queryCustomerCourseListData({
        companyId: this.companyId,
        columnId: this.columnId,
        campPeriodId: this.campPeriodId,
        courseId: this.valueCamp,
        salesGroupId: !this.isOther ? this.salesGroupId : this.currentUserSalesGroupId,
        salesId: !this.isOther ? this.salesId : this.currentUserSalesId,
        startDate: this.restTime || this.valueCamp ? "" : this.startDate ? this.formatDate(this.startDate) : "",
        isFlag: this.isFlag,
        endDate: this.restTime || this.valueCamp ? "" : this.endDate ? this.formatDate(this.endDate) : "",
      });

      if (code === 0) {
        // 正确获取数组中的第一个对象
        const result = data[0];

        // 转换数据类型并移除单位
        this.campCourseData = {
          countCustomerCourse: parseFloat(result.countCustomerCourse),
          countCustomerComplete: parseFloat(result.countCustomerComplete),
          countCustomerRedPacket: parseFloat(result.countCustomerRedPacket),
          amountCustomerRedPacket: parseFloat(result.amountCustomerRedPacket),
        };
      } else {
        uni.showToast({ title: msg, icon: "none" });
      }
    } catch (error) {
      // console.error("表格数据加载失败:", error);
      uni.showToast({ title: "数据加载异常", icon: "none" });
    } finally {
      this.dataLoading = false; // 关闭加载状态
    }
  }

  pageChange(currentPage: number) {
    if (this.active === "1") {
      this.pagination1.current = currentPage;
      this.getCustomerTableData(); // 重新加载数据
    } else {
      this.pagination.current = currentPage;
      this.getCampPeriodTableData();
    }
  }
  onUnload() {
    if (this.campDataTimer) {
      clearInterval(this.campDataTimer);
    }
  }
}
</script>
<style scoped lang="scss">
.content {
  flex: 14;
  background-color: #f1f1f1;
  overflow: hidden !important;
}

.data-body {
  height: 50vh;
  overflow-y: scroll;
  position: relative;
}

.div-bottom {
  flex-shrink: 0;
  background: #ffffff;
  width: 100%;
  position: fixed;
  bottom: 0;
  z-index: 10;
}

.camp-period-data {
  position: sticky;
  border-radius: 16px;
  border: 1px solid #999999;
  margin: 10px;
  padding: 10px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
}

.card-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
}

.card {
  background-color: #fafafa;
  border: 1px solid #ccc;
  border-radius: 8px;
  padding: 2px;
  text-align: center;
}

.card-title {
  font-size: 12px;
  font-weight: bold;
}

.card-value {
  font-size: 14px;
  margin-top: 5px;
}

.camp-data-wrapper {
  display: flex;
  flex-direction: column;
}

.scroll-wrapper {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 30px; /* 预留分页高度 */
}

.search-wrapper {
  position: relative;
  z-index: 100;
}

.search-trigger {
  position: absolute;
  left: 10px;
  top: 10px;
  font-size: 30px;
  z-index: 101;
}

.search-panel {
  position: fixed;
  left: 2%;
  top: 40%;
  width: 80%;
  background: #fff;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  transition: all 0.3s;
  padding: 20px;
  z-index: 100;

  &.van-show {
    left: 0;
  }
}

.search-btn-wrapper {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.search-btn {
  color: #fff;
  margin-top: 15px;
}

.cancel-btn {
  margin-left: 10px;
  color: #fff;
  margin-top: 15px;
}

.list-loading {
  position: absolute;
  top: 50%;
  left: 45%;
}
</style>
