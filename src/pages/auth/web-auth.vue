<template>
  <view>
    <web-view :src="webAuthUrl" @message.native="handleMessage"></web-view>
  </view>
</template>

<script lang="ts">
import Vue from "vue";
import { Component } from "vue-property-decorator";
import { handleLogin } from "@/api/auth.api";

@Component({
  name: "web-auth",
  components: {},
})
export default class WebAuth extends Vue {
  web_appid = process.env.VUE_APP_COM_APPID; // 公众号AppID
  web_redirect_uri = encodeURIComponent(process.env.VUE_APP_COM_WEB_PATH); // 回调地址
  state = this.web_appid; // 或者你想传任何字符串
  webAuthUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${this.web_appid}&redirect_uri=${this.web_redirect_uri}&response_type=code&scope=snsapi_userinfo&state=${this.state}#wechat_redirect`;
  handleMessage(e: any) {
    console.log("接收到的消息：", e.detail.data[0].type);
    // 处理消息
    if (e.detail.data[0].type === "authSuccess") {
      uni.removeStorageSync("wxUserInfo");
      console.log("接收到的消息authSuccess：", e);
      // 授权成功
      uni.setStorageSync("wxUserInfo", e.detail.data[0].info.data);
      uni.$emit("wxUserInfoUpdated", e.detail.data[0].info.data);
      // uni.navigateBack({ animationType: "auto" });
    } else if (e.type === "authFail") {
      // 授权失败
      uni.showToast({
        title: "授权失败",
        icon: "none",
      });
    }
  }
}
</script>
