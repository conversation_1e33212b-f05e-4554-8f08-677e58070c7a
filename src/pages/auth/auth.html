<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>信息授权</title>
    <style>
        /* 添加简单的样式以适配移动端 */
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            flex-direction: column;
            align-items: center;
            height: 100vh;
            background-color: #f5f5f5;
            position: relative;
        }
        h1 {
            font-size: 20px;
            color: #333;
        }
        p {
            font-size: 16px;
            color: #666;
        }

        .loader {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            position: fixed;
            top: 40%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 1000;
        }

        @keyframes spin {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }
    </style>
    <script type="text/javascript" src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
    <script>
        // 兼容性处理：检查 URLSearchParams 是否存在
        if (!window.URLSearchParams) {
            window.URLSearchParams = function (query) {
                this.params = new URLSearchParams(query);
            };
            URLSearchParams.prototype.get = function (name) {
                return this.params.get(name);
            };
        }

        // 兼容性处理：检查 fetch 是否存在
        if (!window.fetch) {
            window.fetch = function (url, options) {
                return new Promise((resolve, reject) => {
                    const xhr = new XMLHttpRequest();
                    xhr.open(options.method || 'GET', url);
                    xhr.onload = () => resolve({
                        ok: xhr.status >= 200 && xhr.status < 300,
                        status: xhr.status,
                        json: () => Promise.resolve(JSON.parse(xhr.responseText))
                    });
                    xhr.onerror = () => reject(new TypeError('Network request failed'));
                    xhr.send(options.body);
                });
            };
        }

        // 获取URL参数
        function getQueryParams() {
            const urlParams = new URLSearchParams(window.location.search);
            return {
                code: urlParams.get('code'),
                state: urlParams.get('state')
            };
        }

        // 发起接口请求
        async function fetchUserInfo(code, state) {
            try {
                const response = await fetch(`https://www.huaxiacomp.cn/gateway/hxsy-auth/auth/get-wx-user-info?code=${code}&appid=${state}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                const data = await response.json();
                console.log('接口返回数据:', data);

                // 发送授权成功消息给微信小程序
                wx.miniProgram.postMessage({ data: { type: 'authSuccess', info: data } });

                // 授权成功后跳转回小程序页面
                wx.miniProgram.navigateBack();
            } catch (error) {
                console.error('接口请求失败:', error);
                wx.miniProgram.postMessage({ data: { type: 'authFail' } });
            }
        }

        // 页面加载时执行
        window.onload = () => {
            const params = getQueryParams();
            if (params.code && params.state) {
                fetchUserInfo(params.code, params.state);
            } else {
                console.error('缺少必要的参数: code 或 state');
            }
        };
    </script>
</head>
<body>
    <h1>微信信息授权</h1>
    <div>正在处理授权信息，请稍候...</div>
    <div id="loader" class="loader"></div>
</body>
</html>