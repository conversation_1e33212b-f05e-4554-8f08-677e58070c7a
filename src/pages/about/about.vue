<template>
  <view class="about-container" :style="{ height: contentHeight  + 'px', paddingTop: topHeight + 'px' }">
    <navigation-bar showText text="关于我们" showIcon />

    <view class="content">
      <view class="section">
        <view class="section-title">简介</view>
        <view class="section-content">本平台定期更新专题分享类视频，匠心打造"药食同源+八大菜系"知识体系，融合千年食疗智慧、现代营养科学与中华烹饪精髓，为您提供从养生理论到美食实践分享类视频，本平台视频禁止搬运做其他商业用途。</view>
      </view>
    </view>
  </view>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import NavigationBar from "@/components/common/NavigationBar.vue";
import {getLayoutHeights} from "@/utils/get-page-height.util";

@Component({
  components: { NavigationBar },
})
export default class About extends Vue {
  onClickLeft() {
    uni.navigateBack();
  }
  topHeight = 88;
  contentHeight = 0;

  onShow() {
    const { topHeight, contentHeight } = getLayoutHeights();
    this.topHeight = topHeight;
    this.contentHeight = contentHeight;
  }
}
</script>

<style lang="scss" scoped>
.about-container {
  background-color: #f7f8fa;

  .header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 100;
  }

  .content {
    padding: 88px 20px 20px;

    .logo-container {
      display: flex;
      justify-content: center;
      margin: 30px 0;

      .logo {
        width: 100px;
        height: 100px;
        border-radius: 20px;
      }
    }

    .app-name {
      text-align: center;
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 5px;
    }

    .app-version {
      text-align: center;
      font-size: 14px;
      color: #999;
      margin-bottom: 30px;
    }

    .section {
      margin-bottom: 20px;
      background-color: #fff;
      border-radius: 8px;
      padding: 15px;

      .section-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 10px;
        color: #333;
      }

      .section-content {
        font-size: 14px;
        color: #666;
        line-height: 1.6;

        view {
          margin-bottom: 5px;
        }
      }
    }
  }
}
</style>
