<template>
  <view class="messages-page" :style="{ height: contentHeight + 'px', paddingTop: topHeight + 'px' }">
    <navigation-bar style="width: 100%; z-index: 99" :showIcon="true" :showText="true" text="我的留言"></navigation-bar>

    <view class="messages-content">
      <!-- 留言统计 -->
      <view class="message-stats">
        <view class="stat-item">
          <text class="stat-number">{{ messageStats.total }}</text>
          <text class="stat-label">总留言</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ messageStats.replied }}</text>
          <text class="stat-label">已回复</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ messageStats.pending }}</text>
          <text class="stat-label">待回复</text>
        </view>
      </view>

      <!-- 留言列表 -->
      <view class="message-list">
        <view v-for="(message, index) in messageList" :key="index" class="message-item" @click="viewMessageDetail(message)">
          <view class="message-header">
            <view class="message-info">
              <text class="message-type">{{ message.type }}</text>
              <text class="message-time">{{ message.createTime }}</text>
            </view>
            <view class="message-status" :class="'status-' + message.status">
              {{ message.statusText }}
            </view>
          </view>

          <view class="message-content">
            <text class="message-title">{{ message.title }}</text>
            <text class="message-text">{{ message.content }}</text>
          </view>

          <view v-if="message.reply" class="message-reply">
            <view class="reply-header">
              <van-icon name="service" size="16" color="#007aff" />
              <text class="reply-label">客服回复</text>
              <text class="reply-time">{{ message.replyTime }}</text>
            </view>
            <text class="reply-content">{{ message.reply }}</text>
          </view>

          <view class="message-actions">
            <!--            <van-button v-if="message.status === 'replied'" size="small" type="default" @click.stop="rateReply(message)"> 评价 </van-button>-->
            <van-button size="small" type="primary" @click.stop="viewMessageDetail(message)"> 查看详情 </van-button>
          </view>
        </view>

        <!-- 空状态 -->
        <view v-if="messageList.length === 0" class="empty-state">
          <van-empty description="暂无留言记录" />
          <van-button type="primary" @click="createMessage">写留言</van-button>
        </view>
      </view>

      <!-- 浮动按钮 -->
      <view class="fab-button" @click="createMessage">
        <van-icon name="edit" size="24" color="#fff" />
      </view>
    </view>

    <!-- 新建留言弹窗 -->
    <van-popup :show="showMessageForm" position="bottom" round @close="showMessageForm = false">
      <view class="message-form">
        <view class="form-header">
          <text class="form-title">写留言</text>
          <van-icon name="cross" size="20" @click="showMessageForm = false" />
        </view>

        <view class="form-content">
          <view class="form-item">
            <text class="form-label">留言类型</text>
            <picker :value="typeIndex" :range="messageTypes" @change="onTypeChange">
              <view class="picker-text">{{ messageTypes[typeIndex] }}</view>
            </picker>
          </view>

          <view class="form-item">
            <text class="form-label">留言标题</text>
            <input v-model="formData.title" class="form-input" placeholder="请输入留言标题" />
          </view>

          <view class="form-item">
            <text class="form-label">留言内容</text>
            <textarea v-model="formData.content" class="form-textarea" placeholder="请详细描述您的问题或建议" maxlength="500" />
            <text class="char-count">{{ formData.content.length }}/500</text>
          </view>

          <view class="form-item">
            <text class="form-label">联系方式（选填）</text>
            <input v-model="formData.contact" class="form-input" placeholder="手机号或邮箱" />
          </view>
        </view>

        <view class="form-footer">
          <van-button type="default" @click="showMessageForm = false">取消</van-button>
          <van-button type="primary" @click="submitMessage">提交</van-button>
        </view>
      </view>
    </van-popup>
  </view>
</template>

<script lang="ts">
import Vue from "vue";
import { Component } from "vue-property-decorator";
import NavigationBar from "@/components/common/NavigationBar.vue";
import { getLayoutHeights } from "@/utils/get-page-height.util";

interface Message {
  id: number;
  type: string;
  title: string;
  content: string;
  contact?: string;
  createTime: string;
  status: "pending" | "replied" | "closed";
  statusText: string;
  reply?: string;
  replyTime?: string;
  rating?: number;
}

@Component({
  name: "messages",
  components: { NavigationBar },
})
export default class Messages extends Vue {
  showMessageForm = false;
  typeIndex = 0;
  topHeight = 88;
  contentHeight = 0;

  messageTypes = ["商品咨询", "订单问题", "售后服务", "投诉建议", "其他"];

  formData = {
    title: "",
    content: "",
    contact: "",
  };

  messageStats = {
    total: 5,
    replied: 3,
    pending: 2,
  };

  messageList: Message[] = [
    // {
    //   id: 1,
    //   type: "订单问题",
    //   title: "订单配送时间咨询",
    //   content: "您好，我的订单已经发货3天了，请问大概什么时候能到？订单号：202312150001",
    //   contact: "138****8888",
    //   createTime: "2023-12-15 14:30",
    //   status: "replied",
    //   statusText: "已回复",
    //   reply: "您好，您的订单预计明天下午送达，请保持手机畅通。如有问题请随时联系我们。",
    //   replyTime: "2023-12-15 16:20",
    // },
    // {
    //   id: 2,
    //   type: "商品咨询",
    //   title: "商品规格询问",
    //   content: "请问这款苹果是什么品种？甜度如何？适合老人食用吗？",
    //   createTime: "2023-12-14 10:15",
    //   status: "replied",
    //   statusText: "已回复",
    //   reply: "这是红富士苹果，甜度适中，口感脆嫩，非常适合老人食用。我们的苹果都是精选优质果品。",
    //   replyTime: "2023-12-14 11:30",
    // },
    // {
    //   id: 3,
    //   type: "售后服务",
    //   title: "商品质量问题",
    //   content: "收到的蔬菜有部分不新鲜，希望能够退换货处理。",
    //   contact: "139****9999",
    //   createTime: "2023-12-13 16:45",
    //   status: "pending",
    //   statusText: "待回复",
    // },
    // {
    //   id: 4,
    //   type: "投诉建议",
    //   title: "配送服务建议",
    //   content: "希望能增加配送时间段选择，方便上班族接收。",
    //   createTime: "2023-12-12 09:20",
    //   status: "replied",
    //   statusText: "已回复",
    //   reply: "感谢您的建议，我们正在考虑增加晚间配送时段，预计下月上线。",
    //   replyTime: "2023-12-12 14:10",
    // },
    // {
    //   id: 5,
    //   type: "其他",
    //   title: "会员权益咨询",
    //   content: "请问如何升级会员等级？有什么具体要求吗？",
    //   createTime: "2023-12-11 20:30",
    //   status: "pending",
    //   statusText: "待回复",
    // },
  ];

  onLoad() {
    this.updateStats();
  }

  onShow() {
    const { topHeight, contentHeight } = getLayoutHeights();
    this.topHeight = topHeight;
    this.contentHeight = contentHeight;
  }

  updateStats() {
    this.messageStats.total = this.messageList.length;
    this.messageStats.replied = this.messageList.filter((m) => m.status === "replied").length;
    this.messageStats.pending = this.messageList.filter((m) => m.status === "pending").length;
  }

  createMessage() {
    this.showMessageForm = true;
  }

  onTypeChange(e: any) {
    this.typeIndex = e.detail.value;
  }

  submitMessage() {
    if (!this.formData.title.trim()) {
      uni.showToast({ title: "请输入留言标题", icon: "none" });
      return;
    }

    if (!this.formData.content.trim()) {
      uni.showToast({ title: "请输入留言内容", icon: "none" });
      return;
    }

    const newMessage: Message = {
      id: Date.now(),
      type: this.messageTypes[this.typeIndex],
      title: this.formData.title,
      content: this.formData.content,
      contact: this.formData.contact,
      createTime: new Date().toLocaleString(),
      status: "pending",
      statusText: "待回复",
    };

    this.messageList.unshift(newMessage);
    this.updateStats();

    // 重置表单
    this.formData = { title: "", content: "", contact: "" };
    this.typeIndex = 0;
    this.showMessageForm = false;

    uni.showToast({ title: "留言提交成功", icon: "success" });
  }

  viewMessageDetail(message: Message) {
    uni.showToast({ title: "留言详情页面开发中", icon: "none" });
  }

  rateReply(message: Message) {
    uni.showToast({ title: "评价功能开发中", icon: "none" });
  }
}
</script>

<style lang="scss" scoped>
.messages-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.messages-content {
  padding: 15px;
  padding-bottom: 80px;
}

/* 留言统计 */
.message-stats {
  background-color: #fff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 15px;
  display: flex;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 24px;
  color: #007aff;
  font-weight: bold;
  margin-bottom: 5px;
  display: block;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

/* 留言列表 */
.message-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.message-item {
  background-color: #fff;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.message-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.message-type {
  background-color: #f0f8ff;
  color: #007aff;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
}

.message-time {
  font-size: 12px;
  color: #999;
}

.message-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;

  &.status-pending {
    background-color: #fff7e6;
    color: #fa8c16;
  }

  &.status-replied {
    background-color: #f6ffed;
    color: #52c41a;
  }

  &.status-closed {
    background-color: #f5f5f5;
    color: #999;
  }
}

.message-content {
  margin-bottom: 15px;
}

.message-title {
  font-size: 16px;
  color: #333;
  font-weight: bold;
  margin-bottom: 8px;
  display: block;
}

.message-text {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.message-reply {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 15px;
}

.reply-header {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 8px;
}

.reply-label {
  font-size: 12px;
  color: #007aff;
  font-weight: bold;
}

.reply-time {
  font-size: 12px;
  color: #999;
  margin-left: auto;
}

.reply-content {
  font-size: 14px;
  color: #333;
  line-height: 1.5;
}

.message-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.empty-state {
  padding: 50px 20px;
  text-align: center;

  .van-button {
    margin-top: 20px;
  }
}

/* 浮动按钮 */
.fab-button {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, #007aff, #40a9ff);
  border-radius: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 16px rgba(0, 122, 255, 0.3);
  z-index: 10;
}

/* 留言表单 */
.message-form {
  padding: 20px;
  max-height: 80vh;
  overflow-y: auto;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.form-title {
  font-size: 18px;
  color: #333;
  font-weight: bold;
}

.form-content {
  margin-bottom: 30px;
}

.form-item {
  margin-bottom: 20px;
}

.form-label {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  display: block;
}

.picker-text {
  padding: 12px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  color: #333;
}

.form-input {
  padding: 12px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  font-size: 14px;

  &::placeholder {
    color: #999;
  }
}

.form-textarea {
  min-height: 100px;
  padding: 12px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  font-size: 14px;
  resize: none;

  &::placeholder {
    color: #999;
  }
}

.char-count {
  font-size: 12px;
  color: #999;
  text-align: right;
  margin-top: 5px;
  display: block;
}

.form-footer {
  display: flex;
  gap: 15px;

  .van-button {
    flex: 1;
  }
}
</style>
