<template>
  <div class="video-page" :style="{ height: contentHeight + 'px', paddingTop: topHeight + 'px' }">
    <navigation-bar showIcon />
    <div class="video-container" v-if="isHasVideoUrl">
      <!-- 视频加载提示层 -->
      <div class="video-loading" v-if="isVideoLoading">
        <van-loading type="spinner" color="#fff" size="24px" />
        <p class="loading-text">视频加载中...</p>
      </div>

      <!-- 微信小程序视频播放器组件 -->
      <video
        class="custom-video"
        id="video"
        :custom-cache="false"
        :enable-play-gesture="false"
        :show-center-play-btn="false"
        :controls="false"
        play-btn-position="center"
        :src="videoData.src"
        :autoplay="videoData.autoplay"
        :poster="videoData.poster"
        :show-progress="false"
        :enable-progress-gesture="false"
        :initial-time="videoData.initialTime"
        :playback-rate="videoData.playbackRate"
        @timeupdate="onVideoTimeUpdate"
        @touchstart="resetControlTimer"
        @touchmove="onTouchMove"
        @touchend="onTouchEnd"
        @waiting="onVideoWaiting"
        @play="onVideoPlay"
        @loadeddata="onVideoLoaded"
        @error="onVideoError"
      >
        <!-- 在控制条上方添加时间提示 -->
        <div class="seek-tip" v-if="isSwiping">
          {{ formatTime(seekTime) }}
        </div>

        <!-- 自定义控制条容器 -->
        <div class="custom-controls" :class="{ active: controlVisible }">
          <div class="control-buttons">
            <van-icon :name="isPlaying ? 'pause' : 'play'" size="mini" color="#ffffff" @click="togglePlay" />

            <!-- 将进度条移到控制按钮中间 -->
            <div class="progress-bar">
              <van-slider v-if="videoData.showProgress" :value="videoData.currentTime" :min="0" :max="videoData.duration" step="1" @drag-end="onSeek" @change="onSeek" inactive-color="#999999" bar-height="8rpx" />
            </div>
            <span class="time">{{ formatTime(videoData.currentTime) }}/{{ formatTime(videoData.duration) }}</span>
            <van-button size="mini" round class="speed-btn" @click.stop="showSpeed()"> {{ videoData.playbackRate }}x </van-button>
            <van-icon :name="isFullScreen ? 'replay' : 'expand-o'" size="mini" color="#ffffff" @click="toggleFullscreen" />
          </div>
          <div class="speed-panel" v-if="showSpeedOptions">
            <van-button v-for="rate in [1, 1.5]" :key="rate" size="mini" round :type="videoData.playbackRate === rate ? 'primary' : 'default'" @click="changeSpeed(rate)"> {{ rate }}x </van-button>
          </div>
        </div>
      </video>
    </div>
    <van-empty v-else image="error" description="暂时没有数据" />
    <!-- 学习ID显示区域 -->
    <div class="study-id-container" v-if="!isPublicCourse">
      <span class="study-id-label">学习ID：</span>
      <span class="study-id-value" @click="copyStudyId">{{ userInfo.id || "未知" }}</span>
    </div>

    <!-- 视频信息区域 -->
    <div class="video-info">
      <h2 class="video-title">{{ videoData.title }}</h2>
      <p class="video-desc" v-html="videoData.description"></p>
    </div>

    <div v-if="isShowVideoList || isPublicCourse">
      <!-- 课程列表 -->
      <div class="course-list-wrapper">
        <h2 class="section-title">课程列表（共 {{ allCourses.length }} 节）</h2>
        <scroll-view class="course-list" scroll-x :scroll-into-view="scrollToCourseId" scroll-with-animation>
          <view class="course-item" :id="'course-' + course.id" v-for="(course, index) in allCourses" :key="index" :class="{ active: course.id === currentCourseId }" @click="switchCourse(course)">
            {{ course.courseName }}
          </view>
        </scroll-view>
      </div>

      <!-- Tab切换区域 -->
      <van-tabs v-model="activeTab" class="course-tabs" animated swipeable>
        <!-- 课程详情Tab -->
        <van-tab title="课程详情">
          <div class="tab-content" v-if="videoData.courseDescription">
            <div class="course-description-content" v-html="videoData.courseDescription"></div>
          </div>
          <div class="empty-tip" v-else>暂无课程详情</div>
        </van-tab>

        <!-- 营销活动Tab -->
        <van-tab title="课程题目" v-if="videoData.activityInfo && videoData.activityInfo.activityType === ActivityType.QUESTION.code">
          <div class="tab-content">
            <p class="quiz-question">
              <span v-if="videoData.activityInfo.questionType === QuestionType.SINGLE_CHOICE.code" class="question-type-tag single-choice"> <van-icon name="success" size="24rpx" /> 单选 </span>
              <span v-else class="question-type-tag multiple-choice"> <van-icon name="success" size="24rpx" /> 多选 </span>
              {{ videoData.activityInfo.questionText }}
            </p>
            <p class="reward-info" v-if="videoData.activityInfo.config && !isPublicCourse">
              答对本题可获得
              <template v-if="videoData.activityInfo.config.type === 'FIXED'"> {{ videoData.activityInfo.config.amount }}奖励 </template>
              <template v-else-if="videoData.activityInfo.config.type === 'RANDOM'"> {{ videoData.activityInfo.config.minAmount }} - {{ videoData.activityInfo.config.maxAmount }}随机奖励 </template>
            </p>
            <van-radio-group v-if="videoData.activityInfo.questionType === QuestionType.SINGLE_CHOICE.code">
              <van-cell-group>
                <div v-for="(option, index) in videoData.activityInfo.answerOptions" :key="index">
                  <div v-for="opKey in optionsArr" :key="opKey">
                    <van-cell v-if="option[opKey]" :title="option[opKey]">
                      <template #right-icon>
                        <span v-if="option.isCorrect" style="margin-left: 8rpx; color: #07c160">✅</span>
                      </template>
                    </van-cell>
                  </div>
                </div>
              </van-cell-group>
            </van-radio-group>

            <van-checkbox-group v-else-if="videoData.activityInfo.questionType === QuestionType.MULTIPLE_CHOICE.code">
              <van-cell-group>
                <div v-for="(option, index) in videoData.activityInfo.answerOptions" :key="index">
                  <div v-for="opKey in optionsArr" :key="opKey">
                    <van-cell v-if="option[opKey]" :title="option[opKey]">
                      <template #right-icon>
                        <span v-if="option.isCorrect" style="margin-left: 8rpx; color: #07c160">✅</span>
                      </template>
                    </van-cell>
                  </div>
                </div>
              </van-cell-group>
            </van-checkbox-group>
          </div>
        </van-tab>

        <!-- 评价Tab -->
        <van-tab title="评价">
          <div class="tab-content">
            <div class="review-section" v-if="reviews.length > 0">
              <div class="review-item" v-for="(review, index) in reviews" :key="index">
                <div class="review-header">
                  <van-image round width="60rpx" height="60rpx" :src="review.avatar || 'https://img.yzcdn.cn/vant/cat.jpeg'" />
                  <div class="review-user">
                    <div class="username">{{ review.username }}</div>
                    <van-rate v-model="review.rating" readonly size="24rpx" color="#ffd21e" void-color="#c8c9cc" />
                  </div>
                  <div class="review-time">{{ review.time }}</div>
                </div>
                <div class="review-content">{{ review.content }}</div>
              </div>
            </div>
            <div class="empty-tip" v-else>暂无评价</div>

            <div class="add-review" v-if="!isPublicCourse">
              <h3 class="section-title">发表评价</h3>
              <van-rate v-model="newReview.rating" />
              <van-field v-model="newReview.content" type="textarea" placeholder="请输入评价内容" rows="3" autosize />
              <van-button type="primary" block @click="submitReview" :disabled="!newReview.content || newReview.rating === 0"> 提交评价 </van-button>
            </div>
          </div>
        </van-tab>
      </van-tabs>
    </div>

    <div v-else>
      <!-- 客户视频答题区域 -->
      <div class="quiz-section" v-if="videoData.activityInfo.activityType === ActivityType.QUESTION.code">
        <div class="quiz-content">
          <p class="quiz-question">
            <!-- 单选题标签 -->
            <span v-if="videoData.activityInfo.questionType === QuestionType.SINGLE_CHOICE.code" class="question-type-tag single-choice"> <van-icon name="success" size="24rpx" /> 单选 </span>
            <!-- 多选题标签 -->
            <span v-else class="question-type-tag multiple-choice"> <van-icon name="success" size="24rpx" /> 多选 </span>
            {{ videoData.activityInfo.questionText }}
          </p>
          <van-radio-group v-if="videoData.activityInfo.questionType === QuestionType.SINGLE_CHOICE.code" :disabled="!videoData.isFinished" :value="videoData.userActivityData.answers[0]">
            <van-cell-group>
              <div v-for="(option, index) in videoData.activityInfo.answerOptions" :key="index">
                <div v-for="opKey in optionsArr" :key="opKey">
                  <van-cell v-if="option[opKey]" :title="option[opKey]" @click="handleSelectAnswer(opKey, 'radio')">
                    <template #right-icon>
                      <van-radio :name="opKey" />
                    </template>
                  </van-cell>
                </div>
              </div>
            </van-cell-group>
          </van-radio-group>

          <van-checkbox-group v-else-if="videoData.activityInfo.questionType === QuestionType.MULTIPLE_CHOICE.code" :disabled="!videoData.isFinished" :value="videoData.userActivityData.answers">
            <van-cell-group>
              <div v-for="(option, index) in videoData.activityInfo.answerOptions" :key="index">
                <div v-for="opKey in optionsArr" :key="opKey">
                  <van-cell v-if="option[opKey]" :title="option[opKey]" @click="handleSelectAnswer(opKey, 'checkbox')">
                    <template #right-icon>
                      <van-checkbox shape="square" :name="opKey" />
                    </template>
                  </van-cell>
                </div>
              </div>
            </van-cell-group>
          </van-checkbox-group>
        </div>
      </div>

      <!-- 奖励弹窗 -->
      <van-popup :show="videoData.userActivityData.showRedEnvelope" position="center" round closeable @close="closeRedEnvelope">
        <div class="red-envelope-popup">
          <!-- 奖励主体 -->
          <div class="envelope-content">
            <!-- 顶部祝福语 -->
            <p class="title">{{ videoData.userActivityData.redPacketTitle }}</p>
            <!-- 分割线 -->
            <div class="divider">
              <span></span>
              <i class="diamond"></i>
              <span></span>
            </div>
            <!-- 奖励图标及展示 -->
            <div class="wechat-cash-container">
              <div class="wechat-cash">
                <img src="@/static/images/wechat_cash.png" alt="答题奖励" />
              </div>
              <p class="amount">{{ redPackAmount }} 元</p>
            </div>
            <!-- 提示文字 -->
            <p class="tip">稍后发送至微信钱包</p>
          </div>
          <!-- 底部弧形装饰 -->
          <div class="bottom-curve">
            <div class="bottom-content" v-if="!hasPhone && videoData.needPhone">
              <p class="bottom-tip">您未绑定手机号，请先绑定手机号</p>
              <van-button open-type="getPhoneNumber" @getphonenumber="onGetPhoneNumber" class="claim-button" type="primary">获取手机号</van-button>
            </div>
            <div class="bottom-content" v-else>
              <p class="bottom-tip">累积观看课程会获取更多奖励</p>
              <van-button type="primary" block @click="transferMoney()" class="claim-button"> 马上领取 </van-button>
            </div>
          </div>
        </div>
      </van-popup>
      <van-button v-if="videoData.activityInfo.activityType === ActivityType.QUESTION.code" type="primary" :disabled="videoData.userActivityData.isAttempted" block @click="submitQuiz" class="fixed-submit-button"> 提交答案 </van-button>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import { Component } from "vue-property-decorator";
import NavigationBar from "@/components/common/NavigationBar.vue";
import { getDailyCache, setDailyCache } from "@/utils/course-storage";
import { updCourseCustomerRel, getCourseVideo, submitAnswer, updateMobile, updCourseDuration } from "@/api/customer.api";
import { Code } from "@/constants/enum/code.enum";
import { ArrivalStatus } from "@/constants/enum/arrival-status.enum";
import { getAppId, handleLogin } from "@/api/auth.api";
import { getMoney, wechatLogin } from "@/utils/miniprogram";
import { wxPay } from "@/api/vx.api";
import { QuestionType } from "@/constants/enum/question-type.enum";
import { ActivityType } from "@/constants/enum/activity-type.enum";
import { deepParseJSON } from "@/utils/json-utils";
import { getUserPhone } from "@/api/user.api";
import Empty from "@/components/empty/empty.vue";
import { getLayoutHeights } from "@/utils/get-page-height.util";

/**
 * @Desc 课程视频播放页面
 */
@Component({
  name: "course",
  computed: {
    ActivityType() {
      return ActivityType;
    },
    QuestionType() {
      return QuestionType;
    },
  },
  components: { Empty, NavigationBar },
})
export default class Course extends Vue {
  videoContext: any = null; // 视频播放上下文
  controlVisible = false; // 控制面板显示状态
  timer: any = null; // 控制面板定时器
  hasPhone = false; // 是否存在手机号
  isVideoLoading = true; // 视频是否正在加载，默认为true，显示加载状态
  userInfo: any = null; // 用户信息
  isPublicCourse = false; // 是否是public模式
  topHeight = 88;
  contentHeight = 0;

  // 视频加载中事件处理
  onVideoWaiting() {
    console.log("视频加载中...");
    this.isVideoLoading = true;
  }

  // 视频开始播放事件处理
  onVideoPlay() {
    console.log("视频开始播放");
    this.isVideoLoading = false;
  }

  // 视频加载完成事件处理
  onVideoLoaded() {
    console.log("视频加载完成");
    this.isVideoLoading = false;
  }

  // 视频加载失败处理
  onVideoError() {
    console.log("视频加载失败");
    this.isVideoLoading = false;
    uni.showToast({
      title: "视频加载失败，请重试",
      icon: "none",
    });
  }

  isPlaying = true; // 是否正在播放
  showSpeedOptions = false; // 倍速选项显示状态
  isFullScreen = false; // 是否全屏
  touchStartX = 0; // 触摸起始X坐标
  touchStartTime = 0; // 触摸开始时间
  isSwiping = false; // 是否正在滑动
  seekTime = 0; // 快进播放时长
  checked = ["1", "2"];
  lastUpdateTime = 0; // 3秒节流时间戳
  lastFiveMinuteUpdate = 0; // 5分钟节流时间戳

  // 基础数据
  form: any = {
    columnId: "", // 栏目id <该参数从扫码进入时传递>
    companyId: "", // 公司(训练营)id <该参数从扫码进入时传递>
    salesGroupId: "", // 销售组id <该参数从扫码进入时传递>
    salesId: "", // 销售id <该参数从扫码进入时传递>
    campPeriodId: "", // 营期id <该参数从首页、扫码进入时传递>
    corpId: "", // 企微企业ID <该参数从分享链接进入时传递>
    qyUserId: "", // 企微用户ID <该参数从分享链接进入时传递>
  };
  redPackAmount = "0.0";
  optionsArr = ["A", "B", "C", "D"];
  isShowVideoList = false; // 是否显示所有视频数据
  currentCourseId: string | null = null;
  scrollToCourseId: string | null = null;
  allCourses: any = null;
  // 防止重复提交标志
  isTransferring = false;
  activeTab = 0; // 当前激活的tab
  reviews = []; // 评价列表
  newReview = {
    rating: 0,
    content: "",
  };
  // 课程视频数据
  videoData: any = {
    src: "",
    autoplay: true,
    poster: "", // 初始封面图
    title: "", // 视频标题
    initialTime: 0, // 初始播放时长
    playbackRate: 1, // 播放速率
    duration: 0, // 视频总时长
    // 非 video 组件字段
    id: "", // 课程id
    description: "", // 视频描述
    currentTime: 0, // 当前播放时长
    isFinished: false, // 是否已完课
    isArrived: false, // 是否已到课
    showProgress: false, // 是否显示进度条
    enableProgressGesture: false, // 是否开启进度条手势
    needPhone: false, // 领取奖励是否需要获取用户手机号
    campperiodRedPack: false, // 营期奖励开关
    campperiodRedPackAmount: "0.0", // 营期奖励
    // 新增字段
    currentVideoIndex: 0, // 当前播放的视频索引
    // 营销活动数据
    activityInfo: {
      id: "",
      activityType: "", // 活动类型
      // 答题
      questionText: "", // 题目内容
      questionType: "", // 题目类型 (1、单选 2、多选)
      answerOptions: [], // 答案选项 [{"A": "是的", "isCorrect": true}, {"B": "是的吗", "isCorrect": true}, {"C": "不是的", "isCorrect": false}, {"D": "不是", "isCorrect": false}]
      correctAnswer: [], // 正确选项 [1]
      maxAttempts: "1", // 最大尝试次数(默认一次)
      config: {
        type: "FIXED", // FIXED 固定 RANDOM 随机
        amount: "0.0", // 固定 奖励
        maxAmount: "0.0", // 随机 最大奖励
        minAmount: "0.0", // 随机 最小奖励
      },
    },
    // 用户活动数据
    userActivityData: {
      redPacketTitle: "恭喜您，答题正确！", // 祝福语
      randomAmount: "0.0", // 随机奖励
      answers: [], // 用户答案
      attempts: 0, // 用户尝试次数
      isCorrect: false, // 答题是否正确
      isAttempted: false, // 是否已提交答案
      showRedEnvelope: false, // 是否显示答题奖励弹窗
      arrivalData: {}, // 确认收款数据
    },
  };

  onLoad(options: any) {
    console.log("进入参数：", options);

    // 检查是否是从首页进入的public
    if (options.data) {
      this.isPublicCourse = true;
      try {
        // 解析public数据
        let data = JSON.parse(options.data);
        this.videoData.src = data.videoPath; // 设置视频地址
        this.videoData.title = data.title || data.courseName; // 设置视频标题
        this.videoData.poster = data.cover; // 设置封面图
        this.videoData.description = data.desc; // 设置课程简介
        this.videoData.courseDescription = data.courseDescription; // 设置课程详细描述
        this.videoData.activityInfo = deepParseJSON(data.activityInfo);
        this.currentCourseId = data.id;

        // 获取课程列表
        this.allCourses = uni.getStorageSync("currentCampCourses");

        this.$nextTick(() => {
          setTimeout(() => {
            this.scrollToCurrentCourse();
          }, 100);
        });

        return;
      } catch (error) {
        console.error("解析public数据失败：", error);
        // 解析失败时回退到普通课程模式
        this.isPublicCourse = false;
      }
    }

    // 普通课程模式的处理逻辑
    const SAFE_FIELDS = ["companyId", "columnId", "salesGroupId", "salesId", "campPeriodId", "corpId", "qyUserId"];
    try {
      const storedUserInfo = uni.getStorageSync("userInfo");
      const userInfo = storedUserInfo && typeof storedUserInfo === "object" ? { ...storedUserInfo } : {};

      // 合并字段处理逻辑
      SAFE_FIELDS.forEach((field: string) => {
        if (options[field] !== undefined) {
          // 基础类型校验（仅允许字符串/数字）
          if (typeof options[field] === "string" || typeof options[field] === "number") {
            this.form[field] = options[field];
            if (userInfo) userInfo[field] = options[field];
          } else {
            console.warn(`字段 ${field} 类型非法，已忽略`);
          }
        }
      });
      if (userInfo) {
        uni.setStorageSync("userInfo", userInfo);
      }
    } catch (error) {
      console.error("用户信息处理失败：", error);
    }
  }

  onShow() {
    const { topHeight, contentHeight } = getLayoutHeights();
    this.topHeight = topHeight;
    this.contentHeight = contentHeight;
    if (this.isPublicCourse) {
      // 初始化视频上下文
      this.videoContext = uni.createVideoContext("video");
      this.videoData.showProgress = true; // 开启进度条
      this.videoData.enableProgressGesture = true; // 开启进度条手势
      return;
    }

    // 普通课程模式需要登录验证
    if (!uni.getStorageSync("wxUserInfo")) {
      // 获取微信用户信息
      uni.navigateTo({ url: "/pages/auth/web-auth" });
    } else {
      this.loginOrGetVideoUrl();
    }
  }

  loginOrGetVideoUrl() {
    // 确保显示加载状态
    this.isVideoLoading = true;

    // 获取 token 并检查有效期
    const item = uni.getStorageSync("token");
    if (item && item.expires > Date.now()) {
      // token 有效 查询课程信息
      const userInfo = uni.getStorageSync("userInfo");
      this.getVideoUrl(userInfo);
    } else {
      console.log("token 失效，重新登录");
      this.handleLogin();
    }
    // 获取并设置用户信息
    this.userInfo = uni.getStorageSync("userInfo") || {};
    this.videoContext = uni.createVideoContext("video");
    this.videoContext.playbackRate(this.videoData.playbackRate);
  }

  mounted() {}

  get isHasVideoUrl() {
    return !!this.videoData.src;
  }

  /**
   * 用户登录
   */
  async handleLogin() {
    try {
      const userInfo = await handleLogin("2");
      userInfo.columnId = this.form.columnId;
      userInfo.companyId = this.form.companyId;
      userInfo.salesGroupId = this.form.salesGroupId;
      userInfo.salesId = this.form.salesId;
      userInfo.campPeriodId = this.form.campPeriodId;
      // 保存企微参数到用户信息
      if (this.form.corpId) userInfo.corpId = this.form.corpId;
      if (this.form.qyUserId) userInfo.qyUserId = this.form.qyUserId;
      uni.setStorageSync("userInfo", userInfo);
      // 更新组件的userInfo属性，确保学习ID立即显示
      this.userInfo = userInfo;
      // 登录成功，查询课程信息
      await this.getVideoUrl(userInfo);
    } catch (error) {
      console.error("登录流程失败:", error);
      uni.showToast({
        title: error.message || "登录失败，请重试",
        icon: "none",
      });
    }
  }

  /**
   * @Description: 切换播放速度
   * <AUTHOR>
   * @date 2025/4/10
   * @time 17:38
   */
  changeSpeed(rate: number) {
    console.log("切换播放速度", rate);
    this.videoData.playbackRate = rate;
    // 通过视频上下文强制更新倍速
    this.videoContext.playbackRate(rate);
    this.showSpeedOptions = false;
  }

  showControl() {
    clearTimeout(this.timer);
    this.controlVisible = true;
    this.timer = setTimeout(() => {
      this.controlVisible = false;
      this.showSpeedOptions = false;
    }, 3000); // 3秒无操作后隐藏
  }

  resetControlTimer(e: any) {
    this.showControl();
    // 记录初始触摸位置
    if (!this.videoData.enableProgressGesture) return;
    this.touchStartX = e.touches[0].clientX;
    this.touchStartTime = Date.now();
    this.isSwiping = false;
  }

  async onTouchEnd(e: TouchEvent) {
    if (!this.videoData.enableProgressGesture) return;
    if (!this.isSwiping) return;
    const deltaX = e.changedTouches[0].clientX - this.touchStartX;
    // 计算时间调整量（灵敏度系数0.5可根据需求调整）
    const timeDelta = (deltaX / uni.getSystemInfoSync().windowWidth) * this.videoData.duration * 0.1;
    // 更新当前时间并跳转
    this.seekTime = Math.max(0, Math.min(this.videoData.currentTime + timeDelta, this.videoData.duration));
    // 异步保证视频状态更新
    await this.$nextTick();
    this.videoData.currentTime = this.seekTime;
    this.videoContext.seek(this.videoData.currentTime);
    // 自动续播
    if (!this.isPlaying) {
      this.videoContext.play();
      this.isPlaying = true;
    }
    this.isSwiping = false;
  }

  // 新增touchmove处理
  onTouchMove(e: TouchEvent) {
    if (!this.videoData.enableProgressGesture) return;
    this.isSwiping = true;
    const deltaX = e.touches[0].clientX - this.touchStartX;
    // 实时更新显示时间（不实际跳转）
    const timeDelta = (deltaX / uni.getSystemInfoSync().windowWidth) * this.videoData.duration * 0.1;
    this.seekTime = Math.max(0, Math.min(this.videoData.currentTime + timeDelta, this.videoData.duration));
  }

  /**
   * @Description: 获取课程的视频地址
   * <AUTHOR>
   * @date 2025/4/10
   * @time 17:38
   */
  async getVideoUrl(userInfo: any) {
    if (!userInfo.campPeriodId) {
      console.error("营期id不能为空");
      return;
    }

    try {
      // 每次都请求后端获取最新课程数据
      const params = {
        columnId: userInfo.columnId,
        companyId: userInfo.companyId,
        campPeriodId: userInfo.campPeriodId,
        salesGroupId: userInfo.salesGroupId,
        salesId: userInfo.salesId,
        customerId: userInfo.id,
        userType: userInfo.userType,
        ...(this.form.corpId && { corpId: this.form.corpId }),
        ...(this.form.qyUserId && { qyUserId: this.form.qyUserId }),
      };

      const response = await getCourseVideo(params);

      if (response.code !== Code.OK.code) {
        uni.showModal({
          content: response.msg,
          showCancel: false,
        });
        console.error("获取视频失败:", response);
        return;
      }
      const data = response.data;
      if (!data.hasCoursePermission) {
        uni.redirectTo({
          url: `/pages/course/no-permission?data=${JSON.stringify(data)}`,
        });
        return;
      }

      // 设置视频列表和进度条显示状态
      if (data.startingFlag === "0" && data.courseVideoList.length > 1) {
        this.isShowVideoList = true;
        this.videoData.showProgress = true;
        this.videoData.enableProgressGesture = true;
      } else {
        this.isShowVideoList = false;
        this.videoData.showProgress = false;
        this.videoData.enableProgressGesture = false;
      }

      // 获取本地缓存
      const dailyCache = getDailyCache(userInfo.campPeriodId);
      // 比对课程数据并处理
      await this.handleCourseComparison(data.courseVideoList, dailyCache, data);

      // 设置needPhone字段
      this.videoData.needPhone = data.needPhone;
      this.isNeedPhone();
    } catch (error) {
      console.error("获取视频失败catch", error);
      if (error.code) {
        this.handleLogin();
      }
    }
  }

  /**
   * 比对课程数据并处理缓存
   */
  async handleCourseComparison(newCourseList: any[], dailyCache: any, data: any) {
    if (!dailyCache) {
      console.log("今日课程为空");
      // 没有缓存，直接加载第一个视频
      this.videoData.currentVideoIndex = 0;
      this.loadCurrentVideo(newCourseList);
      if (data.startingFlag != "0" && newCourseList.length === 1) {
        setDailyCache(this.form.campPeriodId, this.videoData);
      }
      return;
    }

    // 有缓存，需要比对课程
    const cachedVideoIndex = dailyCache.currentVideoIndex || 0;
    this.allCourses = uni.getStorageSync("cacheCourseList");

    console.log("有缓存");
    // 检查当前播放的课程是否与缓存中的课程一致
    let coursesChanged = false;

    // 比对从第一节课开始到当前播放的课程
    for (let i = 0; i <= cachedVideoIndex; i++) {
      if (!newCourseList[i] || !this.allCourses[i] || newCourseList[i].id !== this.allCourses[i].id) {
        coursesChanged = true;
        break;
      }
    }

    if (coursesChanged) {
      console.log("课程变化");
      // 清除缓存并从第一节开始播放
      uni.removeStorageSync(`daily_course_cache_${this.form.campPeriodId}`);
      this.videoData.currentVideoIndex = 0;
      this.loadCurrentVideo(newCourseList);
      if (data.startingFlag != "0" && newCourseList.length === 1) {
        setDailyCache(this.form.campPeriodId, this.videoData);
      }

      // 提示用户课程已更新
      uni.showToast({
        title: "课程已更新，请重新观看",
        icon: "none",
        duration: 2000,
      });
    } else {
      console.log("课程没变化");
      // 课程未变化，保留原有课程列表
      // 使用缓存数据继续播放
      this.videoData = dailyCache;
      this.videoData.initialTime = this.videoData.currentTime || 0;
      this.videoData.autoplay = true;
      this.isPlaying = true;

      if (this.videoData.userActivityData.isAttempted) {
        this.redPackAmount = this.videoData.userActivityData.randomAmount;
      }
    }
  }

  /**
   * @Description: 加载当前索引的视频
   * <AUTHOR> @date 2025/5/15
   */
  loadCurrentVideo(courseList: any) {
    // 课程发生变化，更新课程列表和缓存
    this.allCourses = courseList;
    uni.setStorageSync("cacheCourseList", courseList);

    if (!this.allCourses || this.allCourses.length === 0) {
      console.error("视频列表为空");
      return;
    }

    // 获取当前索引的视频数据
    const currentVideo = this.allCourses[this.videoData.currentVideoIndex];
    if (!currentVideo) {
      console.error("当前索引视频不存在");
      return;
    }

    // 重置视频播放状态
    this.videoData.isFinished = false;
    this.videoData.isArrived = false;
    this.videoData.currentTime = 0;
    // 设置当前视频信息
    this.videoData.id = currentVideo.id; // 设置课程id
    this.videoData.src = currentVideo.videoPath; // 设置视频地址
    this.videoData.title = currentVideo.courseName; // 设置视频标题
    this.videoData.poster = currentVideo.coverPath; // 设置封面图
    this.videoData.description = currentVideo.courseIntroduction; // 设置课程简介
    // 处理活动信息
    const activityInfo = currentVideo.activityInfo;
    if (activityInfo) {
      this.videoData.activityInfo = deepParseJSON(activityInfo); // 设置营销活动数据
      // 设置奖励名称
      this.setRedPacketTitle(this.videoData.activityInfo.activityType);
      // 设置营期奖励相关数据
      this.videoData.campperiodRedPack = currentVideo.campperiodRedPack; // 营期奖励开关
      this.videoData.campperiodRedPackAmount = currentVideo.campperiodRedPackAmount; // 营期奖励
    } else {
      // 如果 activityInfo 没有值，清空相关数据
      this.videoData.activityInfo = null; // 或 undefined 或 {}，根据你的业务需求
      this.videoData.campperiodRedPack = false; // 清空营期奖励开关
      this.videoData.campperiodRedPackAmount = null; // 清空营期奖励
    }
    // 重置用户活动数据
    this.videoData.userActivityData = {
      redPacketTitle: "恭喜！获得奖励",
      randomAmount: "0.0",
      answers: [],
      attempts: 0,
      isCorrect: false,
      isAttempted: false,
      showRedEnvelope: false,
      arrivalData: {},
    };

    // 设置加载状态
    this.isVideoLoading = true;

    // 如果视频上下文存在，重新加载视频
    if (this.videoContext) {
      this.$nextTick(() => {
        this.videoContext.stop();
        this.videoContext.seek(0);
        this.videoContext.play();
        this.isPlaying = true;
      });
    }
    console.log("加载的课程是：", this.videoData);
  }

  /**
   * @Description: 切换到下一个视频
   * <AUTHOR> @date 2025/5/15
   */
  playNextVideo() {
    // 检查是否还有下一个视频
    if (this.videoData.currentVideoIndex < this.allCourses.length - 1) {
      this.videoData.showProgress = false;
      this.videoData.enableProgressGesture = false;

      // 增加索引并加载下一个视频
      this.videoData.currentVideoIndex++;
      this.loadCurrentVideo();

      // 更新缓存（仅普通课程模式）
      if (!this.isPublicCourse) {
        setDailyCache(this.form.campPeriodId, this.videoData);
      }

      uni.showToast({
        title: `正在播放第${this.videoData.currentVideoIndex + 1}个视频`,
        icon: "none",
        duration: 2000,
      });
    }
  }
  // 更新课程时长
  updDuration(playProgress: number) {
    const userInfo = uni.getStorageSync("userInfo");
    const params = {
      campPeriodId: userInfo.campPeriodId, // 营期id
      customerId: userInfo.id, // 客户id
      courseId: this.videoData.id, // 课程id
      playProgress: playProgress, // 播放进度
      salesId: userInfo.salesId,
    };
    updCourseDuration(params)
      .then((response: any) => {
        if (response.code === Code.OK.code) {
          console.log("更新课程时长成功", response);
        }
      })
      .catch((error: any) => {
        console.error("更新课程时长失败", error);
      });
  }

  updRel(arrivalStatus: any) {
    const userInfo = uni.getStorageSync("userInfo");
    const params = {
      campPeriodId: userInfo.campPeriodId, // 营期id
      customerId: userInfo.id, // 客户id
      courseId: this.videoData.id, // 课程id
      arrivalStatus: arrivalStatus, // 到课状态
      salesId: userInfo.salesId, // 销售id
    };
    updCourseCustomerRel(params)
      .then((response: any) => {
        if (response.code === Code.OK.code) {
          console.log("更新客户关系成功", response);
        }
      })
      .catch((error: any) => {
        console.error("更新客户关系失败", error);
      });
  }

  // 视频播放进度更新
  onVideoTimeUpdate(event: any) {
    const duration = Number(event.detail.duration); // 视频总时长
    const currentTime = Number(event.detail.currentTime); // 当前播放时长
    this.videoData.currentTime = currentTime; // 记录初始播放时长
    this.videoData.duration = duration; // 记录视频总时长

    if (this.isPublicCourse) {
      return;
    }

    // 以下是普通课程模式的处理逻辑
    if (this.isShowVideoList) {
      return;
    }
    if (this.userInfo.userType === "1") {
      return;
    }
    // 3秒钟节流处理
    let now = Date.now();
    if (now - this.lastUpdateTime < 3000) {
      return;
    }
    this.lastUpdateTime = now;

    // 5分钟调用接口逻辑（每5分钟更新播放视频进度）
    if (now - this.lastFiveMinuteUpdate >= 5 * 60 * 1000 && !this.videoData.isFinished) {
      // currentTime 去掉小数位
      let playProgress = Math.floor(currentTime);
      console.log("每五分钟更新视频播放进度", currentTime, duration, playProgress);
      this.updDuration(playProgress);
      this.lastFiveMinuteUpdate = now; // 更新时间戳
    }
    setDailyCache(this.form.campPeriodId, this.videoData);
    if (!this.videoData.isArrived && currentTime >= 5 * 60 && !this.isShowVideoList) {
      // 播放超过5分钟时 为已到课
      // 调用接口记录观看记录（已到课）
      this.videoData.isArrived = true;
      this.updRel(ArrivalStatus.ATTENDED.code);
    }
    if (!this.videoData.isFinished && currentTime >= duration * 0.9) {
      // 播放到90%时 为已完播
      this.videoData.isFinished = true;
      this.videoData.showProgress = true; // 开启进度条
      this.videoData.enableProgressGesture = true; // 开启进度条手势
      setDailyCache(this.form.campPeriodId, this.videoData);
      // 调用接口记录观看记录（已完播）
      this.updRel(ArrivalStatus.COMPLETED.code);
      // 检查是否有有效的活动信息
      // 判断activityInfo是否为有效对象且包含activityType属性
      if (this.videoData.activityInfo && typeof this.videoData.activityInfo === "object" && this.videoData.activityInfo !== "") {
        // 完播奖励
        this.getRedPacket();
      }
    }

    // 播放完成时（达到总时长）
    if (this.videoData.isFinished && currentTime >= duration) {
      this.videoData.isFinished = true;
      setDailyCache(this.form.campPeriodId, this.videoData);
      // 直接播放下一个视频
      setTimeout(() => {
        this.playNextVideo();
      }, 1500);
    }
  }

  // 格式化时间显示
  formatTime(seconds: number) {
    if (seconds === 0) {
      return "00:00";
    }
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  }

  // 跳转播放位置
  onSeek(e: any) {
    if (e.type === "change") {
      this.videoData.currentTime = e.detail;
    }
    if (e.type === "drag-end") {
      this.videoData.currentTime = e.detail.value;
    }
    this.videoContext.seek(this.videoData.currentTime);
  }

  // 切换播放状态
  togglePlay() {
    this.isPlaying ? this.videoContext.pause() : this.videoContext.play();
    this.isPlaying = !this.isPlaying;
  }

  // 显示倍速选择
  showSpeed() {
    this.showSpeedOptions = !this.showSpeedOptions;
  }

  // 切换全屏
  toggleFullscreen() {
    if (this.isFullScreen) {
      this.videoContext.exitFullScreen();
    } else {
      this.videoContext.requestFullScreen();
    }
    this.isFullScreen = !this.isFullScreen;
  }

  /**
   * @Description: 设置奖励标题
   * <AUTHOR>
   * @date 2025/5/9
   * @time 1:54
   */
  setRedPacketTitle(activityType: any) {
    switch (activityType) {
      case ActivityType.RED_PACKET.code:
        this.videoData.userActivityData.redPacketTitle = "恭喜您获得奖励";
        break;
      case ActivityType.INTEGRAL.code:
        this.videoData.userActivityData.redPacketTitle = "恭喜您获得积分";
        break;
      case ActivityType.GIFT.code:
        this.videoData.userActivityData.redPacketTitle = "恭喜您获得礼品";
        break;
      case ActivityType.COUPON.code:
        this.videoData.userActivityData.redPacketTitle = "恭喜您获得优惠券";
        break;
      case ActivityType.QUESTION.code:
        this.videoData.userActivityData.redPacketTitle = "恭喜您获得答题奖励";
        break;
      case ActivityType.FINISH_COURSE.code:
        this.videoData.userActivityData.redPacketTitle = "恭喜您获得完播奖励";
        break;
      default:
        break;
    }
  }

  /**
   * @Description: 选择答案
   * <AUTHOR>
   * @date 2025/5/8
   * @time 22:40
   */
  handleSelectAnswer(option: any, type: string) {
    // 已完课
    if (!this.videoData.isFinished) {
      uni.showToast({
        title: "请先观看完整视频后再答题哦",
        icon: "none",
      });
      return;
    }
    if (type === "radio") {
      console.log(this.videoData.userActivityData);
      this.videoData.userActivityData.answers = [option];
    } else if (type === "checkbox") {
      if (this.videoData.userActivityData.answers.includes(option)) {
        this.videoData.userActivityData.answers = this.videoData.userActivityData.answers.filter((item: any) => item !== option);
      } else {
        this.videoData.userActivityData.answers.push(option);
      }
      console.log("this.questionData.answers:", this.videoData.userActivityData.answers);
    }
  }

  /**
   * @Description: 获取完播奖励
   * <AUTHOR>
   * @date 2025/5/9
   * @time 1:25
   */
  getRedPacket() {
    // 非 完播奖励活动
    if (this.videoData.activityInfo.activityType != ActivityType.FINISH_COURSE.code) {
      return;
    }
    this.openRedPacket();
  }

  /**
   * @Description: 提交答案
   * <AUTHOR>
   * @date 2025/5/7
   * @time 23:28
   */
  submitQuiz() {
    if (this.isPublicCourse) {
      return;
    }
    console.log("提交答案", this.videoData.userActivityData.answers);
    if (this.videoData.userActivityData.answers.length === 0) {
      uni.showToast({
        title: "请选择答案",
        icon: "none",
      });
      return;
    }
    // if (this.videoData.userActivityData.attempts > this.videoData.activityInfo.maxAttempts) {
    //   uni.showToast({
    //     title: "答题次数已用完",
    //     icon: "none",
    //   });
    //   return;
    // }
    // 校验正确答案
    const correctAnswer: any = this.videoData.activityInfo.correctAnswer;
    const isCorrect = this.videoData.userActivityData.answers.every((item: any) => correctAnswer.includes(item));
    this.videoData.userActivityData.isCorrect = isCorrect;
    // this.videoData.userActivityData.attempts++;
    // 答题正确，提交答案
    if (isCorrect) {
      // 提交答案
      this.submitAnswer(() => {
        this.videoData.userActivityData.isAttempted = true;
        // 展示奖励弹窗
        this.openRedPacket();
      });
    } else {
      uni.showToast({
        title: "答案错误，请重新选择",
        icon: "none",
      });
    }
    // 更新缓存
    setDailyCache(this.form.campPeriodId, this.videoData);
  }

  // 提交答题  接口
  submitAnswer(_callback?: any) {
    uni.showLoading({ title: "请稍后..." });
    const userInfo = uni.getStorageSync("userInfo");
    const params = {
      columnId: userInfo.columnId, // 栏目id
      companyId: userInfo.companyId, // 公司id
      campPeriodId: userInfo.campPeriodId, // 营期id
      salesId: userInfo.salesId, // 销售id
      customerId: userInfo.id, // 客户id
      courseId: this.videoData.id, // 课程id
    };
    submitAnswer(params)
      .then((response: any) => {
        uni.hideLoading();
        if (response.code === Code.OK.code) {
          console.log("提交答案成功", response);
          _callback();
        }
      })
      .catch((error: any) => {
        uni.hideLoading();
        console.error("提交答案失败", error);
      });
  }

  // 打开领取奖励弹窗
  openRedPacket() {
    this.isNeedPhone();
    this.getAmount();
    this.videoData.userActivityData.showRedEnvelope = true;
    this.videoData.userActivityData.randomAmount = this.redPackAmount;
    // 更新缓存
    setDailyCache(this.form.campPeriodId, this.videoData);
  }
  // 是否需要获取用户手机号
  isNeedPhone() {
    const userInfo = uni.getStorageSync("userInfo");
    // 如果needPhone为false，直接设置hasPhone为true，允许领取奖励
    // 如果needPhone为true，则需要检查用户是否有手机号
    const needPhone = this.videoData.needPhone;
    console.log("领取奖励是否需要手机号:", needPhone);
    console.log("userInfo.mobile:", userInfo.mobile);

    if (!needPhone || userInfo.mobile) {
      this.hasPhone = true;
    } else {
      this.hasPhone = false;
    }
  }
  /**
   * @Description: zz
   * <AUTHOR>
   * @date 2025/5/9
   * @time 1:22
   */
  transferMoney() {
    if (this.isPublicCourse) {
      return;
    }
    // 如果已经在处理请求，则直接返回，防止重复点击
    if (this.isTransferring) {
      uni.showToast({
        title: "请求处理中，请勿重复点击",
        icon: "none",
      });
      return;
    }

    // 设置标志为正在处理
    this.isTransferring = true;

    const userInfo = uni.getStorageSync("userInfo");
    uni.showLoading({ title: "请稍后..." });
    const params = {
      appId: process.env.VUE_APP_COM_APPID,
      // 奖励 若营期有奖励，则按营期奖励，否则按活动配置
      amount: this.redPackAmount, // 奖励
      openid: uni.getStorageSync("wxUserInfo")?.openid, // 公众号openid
      columnId: userInfo.columnId, // 栏目id
      companyId: userInfo.companyId, // 公司(训练营)id
      salesGroupId: userInfo.salesGroupId, // 销售组id
      salesId: userInfo.salesId, // 销售id
      campPeriodId: userInfo.campPeriodId, // 营期id
      customerId: userInfo.id, // 客户id
      customerName: userInfo.nickname, // 客户名称
      courseId: this.videoData.id, // 课程id
      courseName: this.videoData.title, // 课程名称
    };
    wxPay(params)
      .then((response: any) => {
        uni.hideLoading();
        if (response.code === Code.OK.code) {
          console.log("调用zf成功", response);
          const data: any = response.data;
          // keyVersion 密钥版本（0：旧版，1：新版）
          if (data && data.keyVersion) {
            if (data.keyVersion === "1") {
              this.videoData.userActivityData.arrivalData = data;
              this.claimRedEnvelope(); // 确定收款并播放下一个视频
            } else {
              this.videoData.userActivityData.showRedEnvelope = false;
              // 提示奖励即将到账，请注意查收
              uni.showToast({
                title: "奖励即将到达，请注意查收",
                icon: "none",
                duration: 3000,
              });
              // 延迟一段时间后播放下一个视频
              setTimeout(() => {
                this.playNextVideo();
              }, 3000);
            }
          }
        } else {
          uni.showToast({
            title: response.msg,
            icon: "none",
            duration: 3000,
          });
          // 延迟一段时间后播放下一个视频
          setTimeout(() => {
            this.playNextVideo();
          }, 1500);
        }
      })
      .catch((error: any) => {
        uni.hideLoading();
        console.error("奖励领取失败", error);
        uni.showToast({
          title: "奖励领取失败，请稍后重试" + error.msg,
          icon: "none",
          duration: 3000,
        });
      })
      .finally(() => {
        // 无论成功还是失败，都重置防抖标志
        this.isTransferring = false;
      });
  }

  /**
   * @Description: 确定并播放下一个视频
   * <AUTHOR>
   * @date 2025/5/7
   * @time 23:25
   */
  claimRedEnvelope() {
    this.videoData.userActivityData.showRedEnvelope = false;
    console.log("领取奖励数据", this.videoData.userActivityData.arrivalData);
    getMoney({
      mchId: this.videoData.userActivityData.arrivalData.mchId,
      appId: process.env.VUE_APP_COM_APPID,
      package: this.videoData.userActivityData.arrivalData.packageInfo,
    });
    // 延迟一段时间后播放下一个视频
    setTimeout(() => {
      this.playNextVideo();
    }, 1500);
  }

  /**
   * @Description: 关闭奖励弹窗
   * <AUTHOR>
   * @date 2025/5/7
   * @time 23:25
   */
  closeRedEnvelope() {
    // 确定关闭奖励弹窗
    uni.showModal({
      title: "提示",
      content: "确定不领取奖励吗？退出后可能无法继续领取奖励。",
      confirmText: "取消",
      cancelText: "确定",
      cancelColor: "#888888",
      success: (sm) => {
        // 取消 和 确定 按钮逻辑相反
        if (sm.cancel) {
          // 用户点击了确定，关闭奖励弹窗
          this.videoData.userActivityData.showRedEnvelope = false;
          // 延迟一段时间后播放下一个视频
          setTimeout(() => {
            this.playNextVideo();
          }, 1500);
        }
      },
    });
  }

  /**
   * @Description:获取手机号
   * <AUTHOR>
   * @date 2025/5/13
   * @time 17:01
   */
  async onGetPhoneNumber(e: any) {
    try {
      const { encryptedData, iv } = e.detail;
      if (!encryptedData || !iv) {
        uni.showToast({ title: "获取手机号失败", icon: "none" });
        return;
      }
      // 2. 获取微信临时登录凭证 (code)
      const loginRes = await wechatLogin();
      if (!loginRes.code) {
        uni.showToast({ title: "微信登录失败，请重试", icon: "none" });
        return;
      }
      // 3. 调用后端接口解密手机号
      await getUserPhone({
        encryptedData: encodeURIComponent(encryptedData),
        iv: encodeURIComponent(iv),
        vxCode: loginRes.code,
        appId: getAppId(),
      }).then((resp) => {
        console.log(resp);
        const { code, data, msg } = resp;
        if (code == Code.OK.code) {
          const mobile = data;
          const userInfo = uni.getStorageSync("userInfo");
          userInfo.mobile = mobile;
          this.updateMobile(userInfo);
        } else {
          uni.showToast({ title: msg, icon: "error" });
        }
      });
    } catch (error) {
      console.error("获取手机号失败:", error);
      uni.showToast({ title: "获取手机号异常", icon: "error" });
    }
  }

  /**
   * @Description: 更新用户信息
   * <AUTHOR>
   * @date 2025/5/13
   * @time 17:06
   */
  updateMobile(userInfo: any) {
    const params = {
      id: userInfo.id, // 客户id
      mobile: userInfo.mobile, // 手机号
      unionId: userInfo.unionId, // 微信unionId
    };
    updateMobile(params)
      .then((response: any) => {
        if (response.code === Code.OK.code) {
          console.log("更新用户信息成功", response);
          uni.setStorageSync("userInfo", userInfo);
          this.hasPhone = true;
          uni.showToast({ title: "绑定手机号成功", icon: "success" });
        } else {
          uni.showToast({ title: "绑定手机号失败", icon: "error" });
        }
      })
      .catch((error: any) => {
        console.error("更新用户信息异常", error);
        uni.showToast({ title: "绑定手机号异常", icon: "error" });
      });
  }

  // 获取奖励
  getAmount() {
    const { campperiodRedPack, campperiodRedPackAmount, activityInfo } = this.videoData;
    // 优先返回 campperiodRedPack
    if (campperiodRedPack) {
      return (this.redPackAmount = campperiodRedPackAmount);
    }
    const config = activityInfo?.config;
    if (!config) {
      throw new Error("Activity config is missing");
    }
    const { type, amount, maxAmount, minAmount } = config;
    // 处理 FIXED 类型
    if (type === "FIXED") {
      return (this.redPackAmount = amount);
    }
    // 处理 RANDOM 类型
    if (type === "RANDOM") {
      const max = Number(maxAmount);
      const min = Number(minAmount);
      // 验证数值有效性
      if (!isFinite(max) || !isFinite(min) || max < min) {
        throw new Error(`Invalid random range: min=${min}, max=${max}`);
      }
      const rand = Math.random() * (max - min) + min;
      return (this.redPackAmount = rand.toFixed(2));
    }
    // 未知类型兜底
    throw new Error(`Unsupported activity type: ${type}`);
  }
  /*注册营期课程数据大于一节课*/
  switchCourse(course: any) {
    console.log(course);
    // 设置加载状态
    this.isVideoLoading = true;

    this.currentCourseId = course.id;
    this.videoData.src = course.videoPath;
    this.videoData.title = course.courseName || course.title;
    this.videoData.poster = course.coverPath || course.cover;

    // 根据模式设置不同的描述字段
    if (this.isPublicCourse) {
      this.videoData.description = course.desc;
      this.videoData.courseDescription = course.courseDescription;
    } else {
      this.videoData.description = course.courseDescription;
    }

    this.videoData.activityInfo = deepParseJSON(course.activityInfo);
    // 跳到开头播放
    this.videoData.initialTime = 0;
    this.$nextTick(() => {
      this.videoContext.seek(0);
      this.videoContext.play();
      this.scrollToCurrentCourse();
    });
  }
  scrollToCurrentCourse() {
    this.scrollToCourseId = "course-" + this.currentCourseId;
  }
  /**
   * 提交评价
   */
  submitReview() {
    if (!this.newReview.content || this.newReview.rating === 0) {
      uni.showToast({
        title: "请填写评价内容和评分",
        icon: "none",
      });
      return;
    }

    // 模拟提交评价
    this.reviews.unshift({
      username: this.userInfo?.nickname || "匿名用户",
      avatar: this.userInfo?.avatar,
      rating: this.newReview.rating,
      content: this.newReview.content,
      time: "刚刚",
    });

    uni.showToast({
      title: "评价提交成功",
      icon: "success",
    });

    // 清空表单
    this.newReview = {
      rating: 0,
      content: "",
    };
  }

  copyStudyId() {
    if (!this.userInfo?.id) {
      uni.showToast({
        title: "无学习ID可复制",
        icon: "none",
      });
      return;
    }

    uni.setClipboardData({
      data: this.userInfo.id.toString(),
      success: () => {
        uni.showToast({
          title: "学习ID已复制",
          icon: "success",
        });
      },
      fail: () => {
        uni.showToast({
          title: "复制失败",
          icon: "none",
        });
      },
    });
  }
}
</script>

<style lang="scss" scoped>
.study-id-container {
  padding: 16rpx 40rpx;
  background: #fff;
  border-radius: 12rpx 12rpx 0 0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  margin: 24rpx 20rpx 0;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;

  .study-id-label {
    font-size: 28rpx;
    color: #666;
    margin-right: 10rpx;
  }

  .study-id-value {
    font-size: 28rpx;
    color: #007aff;
    font-weight: 500;
    position: relative;
    padding-right: 36rpx;
    cursor: pointer;

    &::after {
      content: "";
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 28rpx;
      height: 28rpx;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23007aff'%3E%3Cpath d='M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z'/%3E%3C/svg%3E");
      background-size: contain;
      background-repeat: no-repeat;
    }

    &:active {
      opacity: 0.7;
    }
  }
}

.video-container {
  width: 100%;
  padding: 0 0;
  position: relative;
}

.video-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 999;
  pointer-events: none;

  .loading-text {
    color: #fff;
    font-size: 28rpx;
    margin-top: 20rpx;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  }
}

.video-info {
  padding: 20rpx;
  background: #fff;
  border-radius: 0 0 12rpx 12rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08); // 更柔和的阴影
  margin: 0 20rpx 24rpx; // 移除顶部边距，保留左右和底部边距

  .video-title {
    font-size: 38rpx;
    color: #1a1a1a; // 更深黑色
    font-weight: 650; // 更明显的加粗
    position: relative;
    padding-left: 20rpx;
    margin-left: 20rpx;

    &::before {
      // 添加装饰线
      content: "";
      position: absolute;
      left: 0;
      top: 8rpx;
      bottom: 8rpx;
      width: 6rpx;
      background: #007aff; // 品牌色点缀
      border-radius: 4rpx;
    }
  }

  .video-desc {
    font-size: 24rpx;
    color: #4d4d4d;
    line-height: 1.8; // 更大的行高
    text-align: justify; // 两端对齐
    margin-top: 16rpx;
    text-indent: 2em; // 直接作用于整个段落
    text-align: justify;
  }
}

.custom-video {
  // 保持现有无圆角样式
  width: 100%;
  height: 400rpx;
  background: #000; // 添加黑色背景防止视频加载前留白
}

.custom-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16rpx 32rpx;
  z-index: 999;
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.6) 30%, rgba(0, 0, 0, 0) 100%);
  transform: translateY(100%); // 初始隐藏在下界外
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform;

  &.active {
    transform: translateY(0); // 显示时恢复原位
    background: linear-gradient(0deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.6) 30%, rgba(0, 0, 0, 0) 100%);
  }

  .control-buttons {
    display: flex;
    align-items: center;
    gap: 10rpx; // 添加素间距

    .progress-bar {
      flex: 1;
      margin: 0 10rpx;
      ::v-deep .van-slider {
        &__button {
          width: 18rpx;
          height: 18rpx;
          box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.12);
        }
        &__button-wrapper {
          right: -8rpx; // 调整滑块位置
        }
      }
    }

    .van-icon {
      flex-shrink: 0; // 防止图标被压缩
      font-size: 40rpx; // 调大图标尺寸
      padding: 8rpx;
    }

    .time {
      flex-shrink: 0;
      font-size: 24rpx;
      color: #fff;
      min-width: 140rpx; // 保证时间显示完整
      text-align: center;
    }
    .speed-btn {
      flex-shrink: 0;
      margin-right: 10rpx;
      ::v-deep .van-button {
        pointer-events: auto !important;
        background: rgba(0, 0, 0, 0.5);
        color: #fff;
        border: none;
        font-size: 24rpx;
        height: 40rpx;
        line-height: 40rpx;
        min-width: 80rpx;
      }
    }
  }
}
.speed-panel {
  position: absolute;
  bottom: 70rpx;
  right: 30rpx;
  display: flex;
  gap: 15rpx;
  background: rgba(0, 0, 0, 0.7);
  padding: 15rpx;
  border-radius: 30rpx;
  z-index: 3;

  ::v-deep .van-button {
    background: rgba(255, 255, 255, 0.2);
    color: #fff;
    border: none;
    font-size: 24rpx;
    height: 40rpx;
    line-height: 40rpx;
    min-width: 80rpx;
    &--primary {
      background: #007aff;
    }
  }
}
.course-description-section {
  margin: 24rpx 20rpx 0;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  padding: 0 20rpx 20rpx;

  .course-description-content {
    font-size: 28rpx;
    color: #4d4d4d;
    line-height: 1.8;
    text-align: justify;
    padding: 0 20rpx;
    margin-bottom: 20rpx;
  }
}

.seek-tip {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.7);
  color: #fff;
  padding: 24rpx 32rpx;
  border-radius: 48rpx;
  font-size: 32rpx;
  z-index: 10000;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
}

.quiz-section {
  margin: 24rpx 20rpx 0;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  padding: 0 20rpx;
  height: 60vh; /* 固定高度 */
  display: flex;
  flex-direction: column;

  .quiz-content {
    flex: 1;
    overflow-y: auto; /* 只有内容区域可滚动 */
    -webkit-overflow-scrolling: touch;
    padding-bottom: 120rpx; /* 留出底部空间 */
  }
  .quiz-question {
    font-size: 36rpx;
    font-weight: bold;
    margin-bottom: 24rpx;
    .question-type-tag {
      display: inline-flex;
      align-items: center;
      font-size: 32rpx;
      font-weight: bold;
      padding: 6rpx 16rpx;
      border-radius: 40rpx;
      margin-right: 20rpx;
      vertical-align: middle;

      &.single-choice {
        background-color: #e8f4ff;
        color: #007aff;
      }

      &.multiple-choice {
        background-color: #f6eaff;
        color: #8b5cf6;
      }
      .van-icon {
        margin-right: 8rpx;
      }
    }
  }
  .reward-info {
    font-size: 34rpx;
    color: #fa6400;
    margin-bottom: 24rpx;
  }
  ::v-deep .van-cell {
    margin-bottom: 20rpx;
    .van-cell__title {
      font-size: 34rpx;
    }
  }
}
.fixed-submit-button {
  position: fixed;
  bottom: 0;
  left: 50%; // 先定位到左半区
  transform: translateX(-50%); // 再左移自身宽度50%
  width: 90%;
  margin: 40rpx 0;
  padding: 0 32rpx;
  z-index: 2;
  ::v-deep .van-button {
    width: 80%;
    background-color: #f15e4c;
    font-size: 32rpx;
    color: #f7e1ba;
    border: none;
    border-radius: 40rpx !important;
  }
}
.red-envelope-popup {
  height: 740rpx;
  width: 500rpx;
  max-width: 600rpx;
  background: #f35d4c;
  text-align: center;
  padding: 20rpx 20rpx 0 20rpx;
  position: relative;

  .envelope-content {
    background: #ffffff;
    border-radius: 20rpx;
    padding: 40rpx;
    position: relative;
    z-index: 1;

    .title {
      font-size: 42rpx;
      font-weight: bold;
      color: #f35d4c;
      margin-bottom: 40rpx;
    }

    .divider {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 60rpx;

      span {
        display: inline-block;
        width: 120rpx;
        height: 2rpx;
        background: #e07f00;
      }

      .diamond {
        display: inline-block;
        width: 10rpx;
        height: 10rpx;
        border: 1rpx solid #e07f00;
        border-radius: 50%;
        margin: 0 20rpx;
      }
    }

    .wechat-cash-container {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 40rpx 0;
    }

    .wechat-cash {
      img {
        width: 120rpx;
        height: 120rpx;
      }
    }

    .amount {
      font-size: 60rpx;
      color: #f35d4c;
      margin-left: 20rpx; /* 调整与图标的间距 */
    }

    .tip {
      font-size: 30rpx;
      color: #999999;
      margin: 20rpx 0 80rpx 0;
    }
  }

  .bottom-curve {
    z-index: 0;
    --circleValue: 500rpx;
    position: absolute;
    top: 20rpx;
    left: 50%;
    width: 100%;
    height: 740rpx;
    transform: translateX(-50%);
    background: radial-gradient(var(--circleValue) at top, transparent 495rpx, #f15e4c var(--circleValue));
  }

  .bottom-content {
    padding: 20rpx 0;
    position: absolute;
    bottom: 40rpx;
    left: 0;
    right: 0;

    .bottom-tip {
      font-size: 30rpx;
      color: #ffb992;
      margin-bottom: 20rpx;
    }

    .claim-button {
      height: 80rpx;
      border: none;
      border-radius: 40rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      ::v-deep .van-button {
        width: 80%;
        background-color: #f7e1ba;
        font-size: 36rpx;
        font-weight: bold;
        color: #f15e4c;
        border: none;
        border-radius: 40rpx !important;
      }
    }
  }
}
.course-list-wrapper {
  margin: 24rpx 20rpx;
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  padding: 20rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #1a1a1a;
  margin: 0 20rpx 16rpx;
  padding: 0 20rpx;
  position: relative;

  &::before {
    content: "";
    position: absolute;
    left: 0;
    top: 8rpx;
    bottom: 8rpx;
    width: 6rpx;
    background: #007aff;
    border-radius: 4rpx;
  }
}

.course-list {
  display: flex;
  overflow-x: auto;
  white-space: nowrap;
  padding: 0 20rpx;
  margin: 0 20rpx;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;

  &.scroll-view {
    display: flex;
    white-space: nowrap;
    padding: 0 20rpx;
    margin: 0 20rpx;
  }

  .course-item {
    flex: 0 0 auto;
    background: #f5f5f5;
    padding: 16rpx 24rpx;
    margin-right: 16rpx;
    border-radius: 12rpx;
    font-size: 28rpx;
    color: #333;
    cursor: pointer;
    white-space: nowrap;
  }

  .course-item:hover {
    background-color: #e0e0e0;
  }

  .course-item.active {
    background-color: #007aff;
    color: #fff;
  }
}

.course-list {
  display: flex;
  flex-direction: row; // 强制横向
  overflow-x: scroll;
}
.course-item {
  flex: 0 0 auto;
  display: inline-block;
  padding: 16rpx 24rpx;
  margin-right: 16rpx;
  border-radius: 12rpx;
  background: #f5f5f5;
  color: #333;
  font-size: 28rpx;
}
.course-item:hover {
  background-color: #e0e0e0;
}
.course-item.active {
  background-color: #007aff;
  color: #fff;
}

/* Tab样式修复 */
.course-tabs {
  margin: 24rpx 20rpx 0;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);

  ::v-deep .van-tabs__wrap {
    border-radius: 16rpx 16rpx 0 0;
    overflow: hidden;
  }

  ::v-deep .van-tab {
    font-size: 28rpx;
    color: #666;

    &--active {
      font-weight: bold;
      color: #007aff;
    }
  }

  ::v-deep .van-tabs__line {
    background-color: #007aff;
    height: 4rpx;
  }
}

.tab-content {
  padding: 20rpx;
  min-height: 200rpx;

  /* 课程详情内容样式 */
  .course-description-content {
    font-size: 28rpx;
    color: #4d4d4d;
    line-height: 1.8;
    text-align: justify;
    padding: 0 20rpx;
    margin-bottom: 20rpx;
    text-indent: 2em;
  }

  /* 营销活动内容样式 */
  .quiz-question {
    font-size: 36rpx;
    font-weight: bold;
    margin-bottom: 24rpx;

    .question-type-tag {
      display: inline-flex;
      align-items: center;
      font-size: 32rpx;
      font-weight: bold;
      padding: 6rpx 16rpx;
      border-radius: 40rpx;
      margin-right: 20rpx;
      vertical-align: middle;

      &.single-choice {
        background-color: #e8f4ff;
        color: #007aff;
      }

      &.multiple-choice {
        background-color: #f6eaff;
        color: #8b5cf6;
      }

      .van-icon {
        margin-right: 8rpx;
      }
    }
  }

  .reward-info {
    font-size: 34rpx;
    color: #fa6400;
    margin-bottom: 24rpx;
  }

  /* 评价内容样式 */
  .review-section {
    padding: 20rpx;

    .review-item {
      margin-bottom: 30rpx;
      padding-bottom: 20rpx;
      border-bottom: 1rpx solid #f0f0f0;

      .review-header {
        display: flex;
        align-items: center;
        margin-bottom: 15rpx;

        .review-user {
          flex: 1;
          margin-left: 20rpx;

          .username {
            font-size: 28rpx;
            color: #333;
            margin-bottom: 5rpx;
          }
        }

        .review-time {
          font-size: 24rpx;
          color: #999;
        }
      }

      .review-content {
        font-size: 28rpx;
        color: #333;
        line-height: 1.6;
      }
    }
  }

  .add-review {
    padding: 20rpx;
    margin-top: 30rpx;
    background: #fff;
    border-radius: 16rpx;

    .van-rate {
      margin: 20rpx 0;
    }

    .van-button {
      margin-top: 20rpx;
    }
  }
}

.empty-tip {
  text-align: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 28rpx;
}

/* 评价区域样式 */
.review-section {
  padding: 20rpx;
}

.review-item {
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.review-header {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.review-user {
  flex: 1;
  margin-left: 20rpx;
}

.username {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 5rpx;
}

.review-time {
  font-size: 24rpx;
  color: #999;
}

.review-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}

.add-review {
  padding: 20rpx;
  margin-top: 30rpx;
  background: #fff;
  border-radius: 16rpx;
}

.add-review .van-rate {
  margin: 20rpx 0;
}

.add-review .van-button {
  margin-top: 20rpx;
}
</style>
