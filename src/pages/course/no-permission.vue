<template>
  <div :style="{ height: contentHeight  + 'px', paddingTop: topHeight + 'px' }">
    <NavigationBar text="提示" showText showIcon />
    <div class="no-permission-page">
      <!-- 提示信息区域 -->
      <div class="content-container">
        <!-- 图标 -->
        <div class="icon-container">
          <van-icon name="info-o" size="40" color="#409EFF" />
        </div>

        <!-- 主标题 -->
        <h2 class="main-title">请从正确的链接进入</h2>

        <!-- 副标题 -->
        <p class="sub-title">
          尊敬的用户<br />
          您不是当前群主的会员<br />
          请从您所属群主的分享链接进入
        </p>

        <!-- 新增二维码展示区域 -->
        <div v-if="salesQrCode" class="qrcode-container">
          <image :src="salesQrCode" class="qrcode-img" :show-menu-by-longpress="true" data-weixin-preview="1" />
          <p class="qrcode-tip">长按识别二维码添加群主企业微信</p>
        </div>

        <!-- 用户信息 -->
        <div class="user-info">
          <!-- 客户信息组 -->
          <div class="info-group">
            <div class="info-item" @click="copyText(`${nickname} (ID: ${userId})`)">
              <van-icon name="user-o" color="#409EFF" />
              <div class="info-content">
                <p>您的信息：{{ nickname }}</p>
                <p class="sub-info">ID: {{ userId }}</p>
              </div>
              <van-icon name="description" class="copy-icon" color="#409EFF" size="14" />
            </div>
          </div>

          <!-- 所属销售信息组 -->
          <div class="info-group" v-if="!salesQrCode">
            <div class="info-item" @click="copyText(`${salesName} (ID: ${salesId})`)">
              <van-icon name="shop-o" color="#409EFF" />
              <div class="info-content">
                <p>您所属的群主：{{ salesName }}</p>
                <p class="sub-info">ID: {{ salesId }}</p>
              </div>
              <van-icon name="description" class="copy-icon" color="#409EFF" size="14" />
            </div>
          </div>

          <!-- 当前链接销售信息组 -->
          <div class="info-group" v-if="!salesQrCode">
            <div class="info-item" @click="copyText(`${urlSalesName} (ID: ${urlSalesId})`)">
              <van-icon name="cluster-o" color="#409EFF" />
              <div class="info-content">
                <p>当前链接的群主：{{ urlSalesName }}</p>
                <p class="sub-info">ID: {{ urlSalesId }}</p>
              </div>
              <van-icon name="description" class="copy-icon" color="#409EFF" size="14" />
            </div>
          </div>

          <!-- 复制提示 -->
          <div class="copy-tip">
            <van-icon name="description" color="#ff4d4f" size="14" />
            <span>点击信息即可复制</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component } from "vue-property-decorator";
import Vue from "vue";
import NavigationBar from "@/components/common/NavigationBar.vue";
import {getLayoutHeights} from "@/utils/get-page-height.util";

@Component({
  name: "no-permission",
  components: { NavigationBar },
})
export default class Course extends Vue {
  urlSalesName = ""; // 本次链接的销售人员姓名
  salesName = ""; // 客户当前所属销售人员
  nickname = "";
  userId = ""; // 当前用户ID
  salesId = ""; // 客户当前所属销售ID
  urlSalesId = ""; // 本次链接的销售人员ID
  salesQrCode = ""; // 销售企微二维码

  topHeight = 88;
  contentHeight = 0;

  onLoad(options: any) {
    console.log("options", options);
    let data = JSON.parse(options.data || "{}");
    this.urlSalesName = data.urlSalesName;
    this.salesName = data.salesName;
    this.salesId = data.salesId || "未知";
    this.urlSalesId = data.urlSalesId || "未知";
    const userInfo = uni.getStorageSync("userInfo");
    this.nickname = userInfo.nickname || "未知";
    this.userId = userInfo.id || "未知";
    this.salesQrCode = data.salesQrCode;
  }

  onShow() {
    const { topHeight, contentHeight } = getLayoutHeights();
    this.topHeight = topHeight;
    this.contentHeight = contentHeight;
  }

  // 复制文本到剪贴板
  copyText(text: string) {
    uni.setClipboardData({
      data: text,
      success: () => {
        uni.showToast({
          title: "复制成功",
          icon: "success",
          duration: 2000,
        });
      },
    });
  }
}
</script>

<style lang="scss" scoped>
.no-permission-page {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 20px 20px 20px;
  font-family: "Arial", sans-serif;

  .content-container {
    width: 100%;
    max-width: 600px;
    margin-top: 40rpx;
    max-height: 70vh;
    padding: 40px 30px;
    background: rgba(250, 250, 255, 0.95);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    text-align: center;
    overflow-y: auto; // 允许容器内部滚动

    .icon-container {
      position: relative;
      margin: 0 auto 20px;
      width: 60px;
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: center;

      /* 删除: 装饰圆环样式
      .icon-decorate {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 100%;
        height: 100%;
        border: 2px dashed rgba(64, 158, 255, 0.3);
        border-radius: 50%;
        animation: rotate 10s linear infinite;
      }
      */
    }

    .main-title {
      font-size: 28px;
      font-weight: 600;
      margin-bottom: 20px;
      color: #1a1a1a;
    }

    .sub-title {
      font-size: 18px;
      line-height: 1.7;
      margin: 0 auto;
      color: #5e6e82;
      position: relative;
      padding: 12px 0;
      max-width: 480px;

      &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 40px;
        height: 3px;
        background: #409eff;
        border-radius: 2px;
      }

      br {
        display: block;
        //margin: 10px 0;
        position: relative;
      }
    }

    .qrcode-container {
      text-align: center;
      margin: 20px auto;
      .qrcode-img {
        max-width: 250px;
        max-height: 250px;
        display: block;
        margin: 0 auto;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
      .qrcode-tip {
        font-size: 15px;
        color: #409eff;
        margin-top: 12px;
        font-weight: 500;
      }
    }
    .user-info {
      margin-top: 20px;

      .info-group {
        margin-bottom: 16px;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);

        &:last-child {
          margin-bottom: 8px; // 修改最后一个info-group的margin，为copy-tip留出空间
        }
      }

      .copy-tip {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        padding: 8px;
        color: #ff4d4f;
        font-size: 13px;
        opacity: 0.9;

        .van-icon {
          margin-top: 1px; // 微调图标位置以更好地对齐文本
        }

        span {
          font-weight: 500;
        }
      }

      .info-item {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 12px;
        padding: 16px;
        background: #f8f9fa;
        text-align: left;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;

        &:hover {
          background: #f0f7ff;
          transform: translateX(4px);
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
        }

        &:active {
          transform: translateX(4px) scale(0.98);
        }

        .info-content {
          flex: 1;
        }

        p {
          margin: 0;
          font-size: 15px;
          color: #333;
        }

        .sub-info {
          font-size: 13px;
          color: #666;
          margin-top: 4px;
        }

        .van-icon {
          flex-shrink: 0;
        }

        .copy-icon {
          opacity: 0.6;
          margin-left: auto;
          transition: opacity 0.3s ease;
        }

        &:hover .copy-icon {
          opacity: 1;
        }
      }
    }
  }
}

@keyframes rotate {
  from {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

@media (max-width: 768px) {
  .no-permission-page {
    padding: 15px;

    .content-container {
      padding: 30px 20px;

      .main-title {
        font-size: 24px;
      }

      .sub-title {
        font-size: 16px;
      }

      .qrcode-container {
        margin: 20px auto;
        .qrcode-img {
          max-width: 200px;
          max-height: 200px;
        }
        .qrcode-tip {
          font-size: 14px;
        }
      }
      .user-info {
        .info-item {
          padding: 10px 16px;
          font-size: 14px;
        }
      }
    }
  }
}
</style>
