<template>
  <view class="coupons-page" :style="{ height: contentHeight  + 'px', paddingTop: topHeight + 'px' }">
    <navigation-bar style="width: 100%; z-index: 99" :showIcon="true" :showText="true" text="我的卡券"></navigation-bar>

    <view class="coupons-content">
      <!-- 卡券状态筛选 -->
      <view class="coupon-tabs">
        <view v-for="(tab, index) in couponTabs" :key="index" class="tab-item" :class="activeTab === index ? 'active' : ''" @click="switchTab(index)">
          <text class="tab-text">{{ tab.name }}</text>
          <view v-if="tab.count > 0" class="tab-badge">{{ tab.count }}</view>
        </view>
      </view>

      <!-- 卡券列表 -->
      <view class="coupon-list">
        <view v-for="(coupon, index) in currentCoupons" :key="index" class="coupon-item" :class="'status-' + coupon.status">
          <view class="coupon-left">
            <view class="coupon-amount">
              <text class="amount-symbol">¥</text>
              <text class="amount-value">{{ coupon.amount }}</text>
            </view>
            <text class="coupon-condition">{{ coupon.condition }}</text>
          </view>

          <view class="coupon-right">
            <view class="coupon-info">
              <text class="coupon-name">{{ coupon.name }}</text>
              <text class="coupon-desc">{{ coupon.description }}</text>
              <text class="coupon-time">有效期：{{ coupon.validTime }}</text>
            </view>

            <view class="coupon-action">
              <van-button v-if="coupon.status === 'unused'" size="small" type="primary" @click="useCoupon(coupon)"> 立即使用 </van-button>
              <text v-else-if="coupon.status === 'used'" class="status-text used">已使用</text>
              <text v-else-if="coupon.status === 'expired'" class="status-text expired">已过期</text>
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <view v-if="currentCoupons.length === 0" class="empty-state">
          <van-empty description="暂无卡券" />
          <view class="empty-actions">
            <van-button type="primary" @click="getCoupons">去领取卡券</van-button>
            <van-button type="default" @click="refreshData">刷新数据</van-button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts">
import Vue from "vue";
import { Component } from "vue-property-decorator";
import NavigationBar from "@/components/common/NavigationBar.vue";
import { getUserCoupons, getUserCouponsStats } from "@/api/mall-extended.api";
import { authManager } from "../../utils/auth";
import { Code } from "@/constants/enum/code.enum";
import {getLayoutHeights} from "@/utils/get-page-height.util";

interface Coupon {
  id: string;
  name: string;
  description: string;
  amount: number;
  condition: string;
  validTime: string;
  status: "unused" | "used" | "expired";
  type: "discount" | "cash";
  couponType?: string;
  discountValue?: number;
  minAmount?: number;
  receiveTime?: string;
  expireTime?: string;
}

@Component({
  name: "coupons",
  components: { NavigationBar },
})
export default class Coupons extends Vue {
  activeTab = 0;
  loading = false;
  customerId = "";
  topHeight = 88;
  contentHeight = 0;

  couponTabs = [
    { name: "全部", count: 0 },
    { name: "可使用", count: 0 },
    { name: "已使用", count: 0 },
    { name: "已过期", count: 0 },
  ];

  allCoupons: Coupon[] = [];

  get currentCoupons() {
    if (this.activeTab === 0) {
      return this.allCoupons;
    }
    const statusMap = ["", "unused", "used", "expired"];
    return this.allCoupons.filter((coupon) => coupon.status === statusMap[this.activeTab]);
  }

  async onLoad() {
    // 获取用户ID
    const userId = await this.getUserId();
    if (userId) {
      this.customerId = userId;
      await Promise.all([
        this.loadUserCoupons(),
        this.loadCouponsStats()
      ]);
    } else {
      // 用户取消登录，跳转到首页
      uni.switchTab({
        url: '/pages/home/<USER>'
      });
    }
  }

  onShow() {
    const { topHeight, contentHeight } = getLayoutHeights();
    this.topHeight = topHeight;
    this.contentHeight = contentHeight;
  }

  switchTab(index: number) {
    this.activeTab = index;
  }

  updateTabCounts() {
    this.couponTabs[0].count = this.allCoupons.length;
    this.couponTabs[1].count = this.allCoupons.filter((c) => c.status === "unused").length;
    this.couponTabs[2].count = this.allCoupons.filter((c) => c.status === "used").length;
    this.couponTabs[3].count = this.allCoupons.filter((c) => c.status === "expired").length;
  }

  // 获取用户ID（使用新的认证管理器）
  async getUserId(): Promise<string | null> {
    return await authManager.getUserId(true);
  }

  // 加载用户优惠券
  async loadUserCoupons() {
    this.loading = true;
    try {
      console.log("优惠券页面 - 开始加载用户优惠券");
      const response = await getUserCoupons(this.customerId);
      console.log("优惠券页面 - API响应:", response);

      if (response.code === Code.OK.code) {
        const coupons = response.data?.records || response.data || [];
        console.log("优惠券页面 - 优惠券数据:", coupons);

        // 转换数据格式
        this.allCoupons = coupons.map((item: any) => ({
          id: String(item.id),
          name: item.couponName || item.name,
          description: this.getCouponDescription(item),
          amount: this.getCouponAmount(item),
          condition: this.getCouponCondition(item),
          validTime: this.formatDate(item.expireTime),
          status: item.couponStatus || "unused",
          type: item.couponType || "cash",
          couponType: item.couponType,
          discountValue: item.discountValue,
          minAmount: item.minAmount,
          receiveTime: item.receiveTime,
          expireTime: item.expireTime,
        }));

        console.log("优惠券页面 - 转换后的优惠券:", this.allCoupons);
      } else {
        console.error("优惠券页面 - 获取优惠券失败:", response.msg);
        uni.showToast({
          title: response.msg || "获取优惠券失败",
          icon: "none"
        });
      }
    } catch (error) {
      console.error("优惠券页面 - 加载优惠券异常:", error);
      uni.showToast({
        title: "网络错误，请重试",
        icon: "none"
      });
    } finally {
      this.loading = false;
    }
  }

  useCoupon(coupon: Coupon) {
    uni.showModal({
      title: "使用卡券",
      content: `确定要使用${coupon.name}吗？`,
      success: (res) => {
        if (res.confirm) {
          // 跳转到商城使用卡券
          uni.switchTab({
            url: "/pages/index/index",
          });
        }
      },
    });
  }

  getCoupons() {
    uni.switchTab({
      url: "/pages/index/index",
    });
  }

  // 刷新数据
  async refreshData() {
    if (this.customerId) {
      this.loading = true;
      try {
        await Promise.all([
          this.loadUserCoupons(),
          this.loadCouponsStats()
        ]);
        uni.showToast({
          title: "刷新成功",
          icon: "success"
        });
      } catch (error) {
        console.error("刷新数据失败:", error);
        uni.showToast({
          title: "刷新失败",
          icon: "none"
        });
      } finally {
        this.loading = false;
      }
    }
  }

  // 加载优惠券统计数据
  async loadCouponsStats() {
    try {
      console.log("优惠券页面 - 开始加载统计数据");
      const response = await getUserCouponsStats(this.customerId);
      console.log("优惠券页面 - 统计API响应:", response);

      if (response.code === Code.OK.code) {
        const stats = response.data || {};
        console.log("优惠券页面 - 统计数据:", stats);

        // 更新标签数量
        this.couponTabs[0].count = stats.total || 0;
        this.couponTabs[1].count = stats.unused || 0;
        this.couponTabs[2].count = stats.used || 0;
        this.couponTabs[3].count = stats.expired || 0;

        console.log("优惠券页面 - 更新后的标签数量:", this.couponTabs);
      } else {
        console.error("优惠券页面 - 获取统计数据失败:", response.msg);
        // 如果API失败，使用本地数据计算
        this.updateTabCounts();
      }
    } catch (error) {
      console.error("优惠券页面 - 加载统计数据异常:", error);
      // 如果API失败，使用本地数据计算
      this.updateTabCounts();
    }
  }

  // 获取优惠券描述
  getCouponDescription(item: any): string {
    if (item.couponType === "discount") {
      return `${item.discountValue}折优惠券`;
    } else {
      return `现金优惠券`;
    }
  }

  // 获取优惠券金额
  getCouponAmount(item: any): number {
    if (item.couponType === "discount") {
      return Math.round((10 - item.discountValue) * 10); // 折扣转换为优惠金额显示
    } else {
      return item.discountValue || 0;
    }
  }

  // 获取优惠券使用条件
  getCouponCondition(item: any): string {
    if (item.minAmount && item.minAmount > 0) {
      return `满${item.minAmount}元可用`;
    } else {
      return "无门槛";
    }
  }

  // 格式化日期
  formatDate(dateString: string): string {
    if (!dateString) return "";
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN');
  }
}
</script>

<style lang="scss" scoped>
.coupons-page {
  background-color: #f5f5f5;
}

.coupons-content {}

/* 卡券状态筛选 */
.coupon-tabs {
  background-color: #fff;
  display: flex;
  border-bottom: 1px solid #f0f0f0;
  position: fixed;
  width: 100%;
  z-index: 10;
}

.tab-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15px 0;
  position: relative;

  &.active {
    .tab-text {
      color: #007aff;
      font-weight: bold;
    }

    &::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 30px;
      height: 2px;
      background-color: #007aff;
    }
  }
}

.tab-text {
  font-size: 14px;
  color: #666;
}

.tab-badge {
  background-color: #ff4444;
  color: #fff;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 8px;
  margin-left: 5px;
  min-width: 16px;
  text-align: center;
}

/* 卡券列表 */
.coupon-list {
  padding: 15px;
}

.coupon-item {
  background-color: #fff;
  border-radius: 12px;
  margin-bottom: 15px;
  overflow: hidden;
  display: flex;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  &.status-used {
    opacity: 0.6;
  }

  &.status-expired {
    opacity: 0.4;
  }
}

.coupon-left {
  width: 120px;
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #fff;
  position: relative;

  &::after {
    content: "";
    position: absolute;
    right: -8px;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    background-color: #f5f5f5;
    border-radius: 50%;
  }
}

.coupon-amount {
  display: flex;
  align-items: baseline;
  margin-bottom: 5px;
}

.amount-symbol {
  font-size: 14px;
  margin-right: 2px;
}

.amount-value {
  font-size: 28px;
  font-weight: bold;
}

.coupon-condition {
  font-size: 12px;
  opacity: 0.9;
}

.coupon-right {
  flex: 1;
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.coupon-info {
  flex: 1;
}

.coupon-name {
  font-size: 16px;
  color: #333;
  font-weight: bold;
  margin-bottom: 5px;
  display: block;
}

.coupon-desc {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
  display: block;
}

.coupon-time {
  font-size: 12px;
  color: #999;
}

.coupon-action {
  margin-left: 15px;
}

.status-text {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;

  &.used {
    background-color: #f0f0f0;
    color: #999;
  }

  &.expired {
    background-color: #fff2f0;
    color: #ff4444;
  }
}

.empty-state {
  padding: 50px 20px;
  text-align: center;

  .van-button {
    margin-top: 20px;
  }
}

.empty-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
  margin-top: 20px;

  .van-button {
    margin-top: 0;
    flex: 1;
    max-width: 120px;
  }
}
</style>
