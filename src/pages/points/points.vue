<template>
  <view class="points-page" :style="{ height: contentHeight  + 'px', paddingTop: topHeight + 'px' }">
    <navigation-bar style="width: 100%; z-index: 99" :showIcon="true" :showText="true" text="我的积分"></navigation-bar>

    <view class="points-content">
      <!-- 积分概览 -->
      <view class="points-overview">
        <view class="points-header">
          <view class="points-info">
            <text class="points-title">当前积分</text>
            <text class="points-number">{{ currentPoints }}</text>
            <text class="points-desc">积分永久有效</text>
          </view>
          <view class="points-icon">
            <van-icon name="gold-coin-o" size="40" color="#ffd700" />
          </view>
        </view>

        <view class="points-stats">
          <view class="stat-item">
            <text class="stat-number">{{ totalEarned }}</text>
            <text class="stat-label">累计获得</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{ totalUsed }}</text>
            <text class="stat-label">累计使用</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{ expiringSoon }}</text>
            <text class="stat-label">即将过期</text>
          </view>
        </view>
      </view>

      <!-- 积分任务 -->
      <view class="points-tasks">
        <view class="section-title">
          <text class="title-text">每日任务</text>
          <text class="title-desc">完成任务获得积分</text>
        </view>

        <view class="task-list">
          <view v-for="(task, index) in dailyTasks" :key="index" class="task-item" @click="doTask(task)">
            <view class="task-icon">
              <van-icon :name="task.icon" size="24" :color="task.iconColor" />
            </view>
            <view class="task-info">
              <text class="task-name">{{ task.name }}</text>
              <text class="task-desc">{{ task.description }}</text>
            </view>
            <view class="task-reward">
              <text class="reward-points">+{{ task.points }}</text>
              <text class="reward-status" :class="task.completed ? 'completed' : ''">
                {{ task.completed ? "已完成" : "去完成" }}
              </text>
            </view>
          </view>
        </view>
      </view>

      <!-- 积分记录 -->
      <view class="points-records">
        <view class="section-title">
          <text class="title-text">积分记录</text>
          <text class="filter-btn" @click="showFilterPopup = true">筛选</text>
        </view>

        <view class="record-list">
          <view v-for="(record, index) in filteredRecords" :key="index" class="record-item">
            <view class="record-info">
              <text class="record-title">{{ record.title }}</text>
              <text class="record-time">{{ record.time }}</text>
            </view>
            <text class="record-points" :class="record.type"> {{ record.type === "earn" ? "+" : "-" }}{{ record.points }} </text>
          </view>
        </view>

        <!-- 加载更多 -->
        <view class="load-more" @click="loadMoreRecords">
          <text class="load-text">加载更多</text>
        </view>
      </view>
    </view>

    <!-- 筛选弹窗 -->
    <van-popup :show="showFilterPopup" position="bottom" round @close="showFilterPopup = false">
      <view class="filter-popup">
        <view class="filter-header">
          <text class="filter-title">筛选记录</text>
          <van-icon name="cross" size="20" @click="showFilterPopup = false" />
        </view>

        <view class="filter-options">
          <view class="filter-group">
            <text class="group-title">记录类型</text>
            <view class="option-list">
              <text v-for="(option, index) in typeOptions" :key="index" class="option-item" :class="selectedType === option.value ? 'active' : ''" @click="selectType(option.value)">
                {{ option.label }}
              </text>
            </view>
          </view>

          <view class="filter-group">
            <text class="group-title">时间范围</text>
            <view class="option-list">
              <text v-for="(option, index) in timeOptions" :key="index" class="option-item" :class="selectedTime === option.value ? 'active' : ''" @click="selectTime(option.value)">
                {{ option.label }}
              </text>
            </view>
          </view>
        </view>

        <view class="filter-footer">
          <van-button type="default" @click="resetFilter">重置</van-button>
          <van-button type="primary" @click="applyFilter">确定</van-button>
        </view>
      </view>
    </van-popup>
  </view>
</template>

<script lang="ts">
import Vue from "vue";
import { Component } from "vue-property-decorator";
import NavigationBar from "@/components/common/NavigationBar.vue";
import {getLayoutHeights} from "@/utils/get-page-height.util";

interface DailyTask {
  id: number;
  name: string;
  description: string;
  points: number;
  icon: string;
  iconColor: string;
  completed: boolean;
  action: string;
}

interface PointsRecord {
  id: number;
  title: string;
  time: string;
  points: number;
  type: "earn" | "use";
}

@Component({
  name: "points",
  components: { NavigationBar },
})
export default class Points extends Vue {
  currentPoints = 1258;
  totalEarned = 3680;
  totalUsed = 2422;
  expiringSoon = 0;

  topHeight = 88;
  contentHeight = 0;

  showFilterPopup = false;
  selectedType = "all";
  selectedTime = "all";

  dailyTasks: DailyTask[] = [
    {
      id: 1,
      name: "每日签到",
      description: "连续签到获得更多积分",
      points: 10,
      icon: "calendar-o",
      iconColor: "#52c41a",
      completed: true,
      action: "signin",
    },
    {
      id: 2,
      name: "浏览商品",
      description: "浏览5个商品",
      points: 5,
      icon: "eye-o",
      iconColor: "#1890ff",
      completed: false,
      action: "browse",
    },
    {
      id: 3,
      name: "分享商品",
      description: "分享1个商品给好友",
      points: 15,
      icon: "share",
      iconColor: "#722ed1",
      completed: false,
      action: "share",
    },
    {
      id: 4,
      name: "完成购买",
      description: "完成一笔订单",
      points: 50,
      icon: "shopping-cart-o",
      iconColor: "#fa8c16",
      completed: false,
      action: "purchase",
    },
  ];

  allRecords: PointsRecord[] = [
    {
      id: 1,
      title: "每日签到",
      time: "2023-12-15 09:30",
      points: 10,
      type: "earn",
    },
    {
      id: 2,
      title: "购买商品",
      time: "2023-12-14 16:20",
      points: 25,
      type: "earn",
    },
    {
      id: 3,
      title: "兑换优惠券",
      time: "2023-12-14 14:15",
      points: 100,
      type: "use",
    },
    {
      id: 4,
      title: "分享商品",
      time: "2023-12-13 11:45",
      points: 15,
      type: "earn",
    },
    {
      id: 5,
      title: "每日签到",
      time: "2023-12-13 08:20",
      points: 10,
      type: "earn",
    },
    {
      id: 6,
      title: "积分抵扣",
      time: "2023-12-12 19:30",
      points: 50,
      type: "use",
    },
    {
      id: 7,
      title: "浏览商品",
      time: "2023-12-12 15:10",
      points: 5,
      type: "earn",
    },
    {
      id: 8,
      title: "每日签到",
      time: "2023-12-12 09:15",
      points: 10,
      type: "earn",
    },
  ];

  typeOptions = [
    { label: "全部", value: "all" },
    { label: "获得", value: "earn" },
    { label: "使用", value: "use" },
  ];

  timeOptions = [
    { label: "全部", value: "all" },
    { label: "今天", value: "today" },
    { label: "最近7天", value: "week" },
    { label: "最近30天", value: "month" },
  ];

  onShow() {
    const { topHeight, contentHeight } = getLayoutHeights();
    this.topHeight = topHeight;
    this.contentHeight = contentHeight;
  }

  get filteredRecords() {
    let records = this.allRecords;

    // 按类型筛选
    if (this.selectedType !== "all") {
      records = records.filter((record) => record.type === this.selectedType);
    }

    // 按时间筛选
    if (this.selectedTime !== "all") {
      const now = new Date();
      const cutoffDate = new Date();

      switch (this.selectedTime) {
        case "today":
          cutoffDate.setHours(0, 0, 0, 0);
          break;
        case "week":
          cutoffDate.setDate(now.getDate() - 7);
          break;
        case "month":
          cutoffDate.setDate(now.getDate() - 30);
          break;
      }

      records = records.filter((record) => new Date(record.time) >= cutoffDate);
    }

    return records;
  }

  doTask(task: DailyTask) {
    if (task.completed) {
      uni.showToast({ title: "任务已完成", icon: "none" });
      return;
    }

    switch (task.action) {
      case "signin":
        this.handleSignin(task);
        break;
      case "browse":
        this.handleBrowse(task);
        break;
      case "share":
        this.handleShare(task);
        break;
      case "purchase":
        this.handlePurchase(task);
        break;
    }
  }

  handleSignin(task: DailyTask) {
    task.completed = true;
    this.currentPoints += task.points;
    this.addRecord("每日签到", task.points, "earn");
    uni.showToast({ title: `签到成功，获得${task.points}积分`, icon: "success" });
  }

  handleBrowse(task: DailyTask) {
    uni.switchTab({
      url: "/pages/category/category",
    });
  }

  handleShare(task: DailyTask) {
    uni.showToast({ title: "分享功能开发中", icon: "none" });
  }

  handlePurchase(task: DailyTask) {
    uni.switchTab({
      url: "/pages/index/index",
    });
  }

  addRecord(title: string, points: number, type: "earn" | "use") {
    const newRecord: PointsRecord = {
      id: Date.now(),
      title,
      time: new Date().toLocaleString(),
      points,
      type,
    };
    this.allRecords.unshift(newRecord);
  }

  selectType(value: string) {
    this.selectedType = value;
  }

  selectTime(value: string) {
    this.selectedTime = value;
  }

  resetFilter() {
    this.selectedType = "all";
    this.selectedTime = "all";
  }

  applyFilter() {
    this.showFilterPopup = false;
  }

  loadMoreRecords() {
    uni.showToast({ title: "没有更多记录了", icon: "none" });
  }
}
</script>

<style lang="scss" scoped>
.points-page {
  background-color: #f5f5f5;
}

.points-content {
  padding-bottom: 20px; /* 底部间距 */
}

.points-overview {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  margin: 0 15px 20px;
  border-radius: 16px;
  padding: 25px 20px;
  color: #fff;
}

.points-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}

.points-info {
  flex: 1;
}

.points-title {
  font-size: 14px;
  opacity: 0.9;
  margin-bottom: 8px;
  display: block;
}

.points-number {
  font-size: 36px;
  font-weight: bold;
  margin-bottom: 5px;
  display: block;
}

.points-desc {
  font-size: 12px;
  opacity: 0.8;
}

.points-icon {
  margin-left: 20px;
}

.points-stats {
  display: flex;
  justify-content: space-between;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 15px;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-number {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 5px;
  display: block;
}

.stat-label {
  font-size: 12px;
  opacity: 0.9;
}

.points-tasks,
.points-records {
  background-color: #fff;
  margin: 0 15px 20px;
  border-radius: 12px;
  padding: 20px;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.title-text {
  font-size: 18px;
  color: #333;
  font-weight: bold;
}

.title-desc {
  font-size: 12px;
  color: #999;
}

.filter-btn {
  font-size: 14px;
  color: #007aff;
}

.task-list,
.record-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.task-item {
  display: flex;
  align-items: center;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 12px;
}

.task-icon {
  margin-right: 15px;
}

.task-info {
  flex: 1;
}

.task-name {
  font-size: 16px;
  color: #333;
  margin-bottom: 5px;
  display: block;
}

.task-desc {
  font-size: 12px;
  color: #666;
}

.task-reward {
  text-align: right;
}

.reward-points {
  font-size: 16px;
  color: #ff4444;
  font-weight: bold;
  margin-bottom: 5px;
  display: block;
}

.reward-status {
  font-size: 12px;
  color: #007aff;

  &.completed {
    color: #999;
  }
}

.record-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f5f5f5;

  &:last-child {
    border-bottom: none;
  }
}

.record-info {
  flex: 1;
}

.record-title {
  font-size: 14px;
  color: #333;
  margin-bottom: 5px;
  display: block;
}

.record-time {
  font-size: 12px;
  color: #999;
}

.record-points {
  font-size: 16px;
  font-weight: bold;

  &.earn {
    color: #52c41a;
  }

  &.use {
    color: #ff4444;
  }
}

.load-more {
  text-align: center;
  padding: 20px 0;
  border-top: 1px solid #f5f5f5;
  margin-top: 10px;
}

.load-text {
  font-size: 14px;
  color: #666;
}

/* 筛选弹窗样式 */
.filter-popup {
  padding: 20px;
  max-height: 60vh;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.filter-title {
  font-size: 18px;
  color: #333;
  font-weight: bold;
}

.filter-options {
  margin-bottom: 30px;
}

.filter-group {
  margin-bottom: 25px;
}

.group-title {
  font-size: 14px;
  color: #333;
  margin-bottom: 15px;
  display: block;
}

.option-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.option-item {
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 20px;
  font-size: 14px;
  color: #666;

  &.active {
    border-color: #007aff;
    color: #007aff;
    background-color: #f0f8ff;
  }
}

.filter-footer {
  display: flex;
  gap: 15px;

  .van-button {
    flex: 1;
  }
}
</style>
