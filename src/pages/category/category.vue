<template>
  <view class="category-page" :style="{ height: contentHeight  + 'px', paddingTop: topHeight + 'px' }">
    <navigation-bar style="width: 100%; z-index: 99" :showIcon="false" :showText="true" text="分类"></navigation-bar>

    <!-- 固定搜索栏 -->
    <view class="search-section-fixed">
      <view class="search-bar" @click="focusSearch" v-if="!showSearchInput">
        <van-icon name="search" size="16" color="#999" />
        <text class="search-placeholder">搜索分类</text>
      </view>

      <view class="search-bar-active" v-if="showSearchInput && !searchValue">
        <van-icon name="search" size="16" color="#999" />
        <text class="search-placeholder-active">搜索分类</text>
      </view>

      <view class="search-bar-with-text" v-if="showSearchInput && searchValue">
        <van-icon name="search" size="16" color="#999" />
        <text class="search-text">{{ searchValue }}</text>
      </view>

      <!-- 实际的搜索输入框（隐藏） -->
      <input
        v-if="showSearchInput"
        class="search-input-real"
        v-model="searchValue"
        placeholder="搜索分类"
        @confirm="onSearch"
        @focus="showSearchHistory = true"
        @blur="hideSearchHistory"
        :focus="searchInputFocus"
      />

      <!-- 搜索历史和热门搜索 -->
      <view v-if="showSearchHistory" class="search-suggestions">
        <view class="search-history" v-if="searchHistory.length > 0">
          <view class="suggestion-title">
            <text>分类搜索历史</text>
            <text class="delete-icon" @click="clearHistory">🗑️</text>
          </view>
          <view class="history-tags">
            <text v-for="(item, index) in searchHistory" :key="index" class="history-tag" @click="searchHistoryItem(item)">
              {{ item }}
            </text>
          </view>
        </view>

        <view class="hot-search" v-if="hotSearches.length > 0">
          <view class="suggestion-title">热门分类</view>
          <view class="hot-tags">
            <text v-for="(item, index) in hotSearches" :key="index" class="hot-tag" @click="searchHotItem(item)">
              {{ item }}
            </text>
          </view>
        </view>
        <view class="hot-search" v-else>
          <view class="suggestion-title">热门分类</view>
          <view class="empty-hot-search">
            <text class="empty-text">暂无热门分类数据</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 主内容区域 -->
    <view class="category-main">
      <!-- 左侧分类列表 - 固定 -->
      <view class="category-sidebar-fixed">
        <scroll-view scroll-y class="category-sidebar-scroll" :scroll-top="scrollTop">
          <view v-for="(category, index) in categories" :key="index" class="category-item" :class="{ active: activeCategory === index }" @click="selectCategory(index)">
            <text class="category-name">{{ category.name }}</text>
          </view>
        </scroll-view>
      </view>

      <!-- 右侧二级分类列表 - 可滚动 -->
      <scroll-view scroll-y class="subcategory-content" enable-back-to-top>
          <!-- 搜索结果 -->
          <view v-if="isSearchMode" class="search-results">
            <view class="search-result-header">
              <text class="result-count">找到 {{ searchResults.length }} 个分类</text>
              <text class="clear-search" @click="clearSearch">取消</text>
            </view>
            <view v-if="searchLoading" class="loading-container">
              <text class="loading-text">搜索中...</text>
            </view>
            <view v-else-if="searchResults.length === 0" class="no-results">
              <text class="no-results-text">没有找到相关分类</text>
            </view>
            <view v-else class="result-list">
              <view
                v-for="(item, index) in searchResults"
                :key="index"
                class="result-item"
                @click="selectSearchResult(item)"
              >
                <view class="result-info">
                  <text class="result-name">{{ item.name }}</text>
                  <text v-if="item.type === 'subcategory'" class="result-category">{{ item.categoryName }}</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 二级分类列表 -->
          <view v-else class="subcategory-list">
            <view v-if="loading" class="loading-container">
              <van-loading size="24px" color="#007aff">加载中...</van-loading>
            </view>
            <view v-else-if="currentSubcategories.length === 0" class="empty-container">
              <van-empty description="暂无分类数据" />
              <van-button type="primary" size="small" @click="loadSubcategories">重新加载</van-button>
            </view>
            <view v-else class="subcategory-grid">
              <view v-for="(subcategory, index) in currentSubcategories" :key="index" class="subcategory-item" @click="goProductList(subcategory)">
                <view v-if="subcategory.image" class="subcategory-image-container">
                  <image :src="subcategory.image" class="subcategory-image" mode="aspectFill" />
                </view>
                <view v-else class="subcategory-image-placeholder">
                  <text class="placeholder-text">无图片</text>
                </view>
                <text class="subcategory-name">{{ subcategory.name }}</text>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
  </view>
</template>

<script lang="ts">
import Vue from "vue";
import { Component } from "vue-property-decorator";
import NavigationBar from "@/components/common/NavigationBar.vue";
import {getLayoutHeights} from "@/utils/get-page-height.util";

@Component({
  name: "category",
  components: { NavigationBar },
})
export default class Category extends Vue {
  searchValue = "";
  activeCategory = 0;
  showSearchHistory = false;
  showSearchInput = false;
  searchInputFocus = false;
  searchHistory: string[] = [];
  hotSearches: string[] = [];
  loading = false;
  searchLoading = false;
  searchResults: any[] = [];
  isSearchMode = false;
  customerId = "";
  scrollTop = 0;

  topHeight = 88;
  contentHeight = 0;

  categories: any[] = [];
  currentSubcategories: any[] = [];

  async getUserId(): Promise<string | null> {
    const { authManager } = await import("../../utils/auth");
    return await authManager.getUserId(false);
  }

  async onLoad() {
    console.log("分类页面 - onLoad 开始");

    await this.initializeUserId();

    await this.loadCategories();
    await this.loadSearchHistory();
    await this.loadHotKeywords();
    await this.loadSubcategories();

    console.log("分类页面 - onLoad 完成，当前二级分类数量:", this.currentSubcategories.length);
  }

  onShow() {
    const {topHeight, contentHeight} = getLayoutHeights();
    this.topHeight = topHeight;
    this.contentHeight = contentHeight;
  }

  async initializeUserId() {
    try {
      const userId = await this.getUserId();
      if (userId && userId.trim() !== "") {
        this.customerId = userId;
        console.log("分类页面 - 用户ID获取成功:", this.customerId);
      } else {
        console.log("分类页面 - 用户未登录或ID获取失败");
        this.customerId = "";
      }
    } catch (error) {
      console.error("分类页面 - 获取用户ID失败:", error);
      this.customerId = "";
    }
  }

  async loadCategories() {
    console.log("分类页面 - 开始加载分类列表");
    try {
      const mallApi = await import("@/api/mall.api");
      const result = await mallApi.getTopCategories();

      if (result && result.data && Array.isArray(result.data)) {
        this.categories = [
          { id: 0, name: "热门推荐", parentId: 0, sortOrder: 0 },
          ...result.data
        ];
        console.log("分类页面 - API分类加载成功:", this.categories.length);
      } else {
        console.error("分类页面 - API返回数据格式错误:", result);
        this.categories = [{ id: 0, name: "热门推荐", parentId: 0, sortOrder: 0 }];

        uni.showToast({
          title: "分类数据加载失败",
          icon: "none"
        });
      }
    } catch (error) {
      console.error("加载分类失败:", error);
      this.categories = [{ id: 0, name: "热门推荐", parentId: 0, sortOrder: 0 }];

      uni.showToast({
        title: "分类数据加载失败",
        icon: "none"
      });
    }
    console.log("分类页面 - 分类列表加载完成，总数:", this.categories.length);
  }

  async loadSearchHistory() {
    if (!this.customerId) {
      console.log("用户未登录，跳过加载搜索历史");
      this.searchHistory = [];
      return;
    }

    try {
      const extendedApi = await import("@/api/mall-extended.api");

      const result = await extendedApi.getSearchHistory(this.customerId, 10);

      console.log("搜索历史API返回:", result);

      if (result && result.data && Array.isArray(result.data)) {
        this.searchHistory = result.data;
      } else {
        console.warn("搜索历史数据格式不正确:", result.data);
        this.searchHistory = [];
      }
    } catch (error) {
      console.error("加载搜索历史失败:", error);
      this.searchHistory = [];
    }
  }

  async loadHotKeywords() {
    try {
      const extendedApi = await import("@/api/mall-extended.api");

      const result = await extendedApi.getHotKeywords(6);

      if (result && result.data && Array.isArray(result.data) && result.data.length > 0) {
        this.hotSearches = result.data;
      } else {
        console.warn("热门关键词数据为空:", result);
        this.hotSearches = [];
      }
    } catch (error) {
      console.error("加载热门关键词失败:", error);
      this.hotSearches = [];
      uni.showToast({
        title: "热门关键词加载失败",
        icon: "none"
      });
    }
  }

  async loadSubcategories() {
    console.log("分类页面 - loadSubcategories 开始");
    console.log("分类页面 - 当前分类索引:", this.activeCategory);
    console.log("分类页面 - 分类列表:", this.categories);

    try {
      this.loading = true;
      const categoryId = this.categories[this.activeCategory]?.id;
      console.log("分类页面 - 当前分类ID:", categoryId);

      if (categoryId === 0 || !categoryId) {
        try {
          console.log("分类页面 - 开始加载热门推荐分类");

          const mallApi = await import("@/api/mall.api");

          const hotSubcategories: any[] = [];
          const topCategories = this.categories.slice(1, 4);

          for (const category of topCategories) {
            try {
              const result = await mallApi.getCategoriesByParentId(category.id);

              if (result && result.data && Array.isArray(result.data)) {
                const subcats = result.data.slice(0, 2).map((item: any) => ({
                  id: item.id,
                  name: item.name,
                  image: item.image || "",
                  categoryId: item.id,
                  parentId: item.parentId
                }));
                hotSubcategories.push(...subcats);
              }
            } catch (subError) {
              console.warn(`获取分类 ${category.name} 的子分类失败:`, subError);
            }
          }

          this.currentSubcategories = hotSubcategories;
          console.log("分类页面 - 热门推荐加载成功:", this.currentSubcategories.length);

          if (hotSubcategories.length === 0) {
            uni.showToast({
              title: "暂无热门分类数据",
              icon: "none"
            });
          }
        } catch (error) {
          console.error("分类页面 - 热门推荐加载失败:", error);
          this.currentSubcategories = [];

          uni.showToast({
            title: "热门分类加载失败",
            icon: "none"
          });
        }
      } else {
        console.log("分类页面 - 开始加载二级分类，分类ID:", categoryId);

        try {
          const mallApi = await import("@/api/mall.api");
          const result = await mallApi.getCategoriesByParentId(categoryId);

          if (result && result.data && Array.isArray(result.data)) {
            this.currentSubcategories = result.data.map((item: any) => ({
              id: item.id,
              name: item.name,
              image: item.image || "",
              categoryId: item.id,
              parentId: item.parentId
            }));
            console.log("分类页面 - 从后端加载二级分类成功:", this.currentSubcategories.length);
          } else {
            console.warn("分类页面 - 后端返回的二级分类数据为空:", result);
            this.currentSubcategories = [];
          }
        } catch (apiError) {
          console.error("分类页面 - 调用后端二级分类接口失败:", apiError);
          this.currentSubcategories = [];

          uni.showToast({
            title: "分类数据加载失败",
            icon: "none"
          });
        }
      }
    } catch (error) {
      console.error("分类页面 - 加载二级分类失败:", error);
      this.currentSubcategories = [];

      uni.showToast({
        title: "分类数据加载失败",
        icon: "none"
      });
    } finally {
      this.loading = false;
      console.log("分类页面 - loadSubcategories 完成，二级分类数量:", this.currentSubcategories.length);
    }
  }

  onSearchInput(e: any) {
    console.log('搜索输入事件:', e);
  }

  async onSearch() {
    if (this.searchValue.trim()) {
      console.log("开始搜索:", this.searchValue.trim(), "用户登录状态:", !!this.customerId);

      if (this.customerId && this.customerId.trim() !== "") {
        console.log("用户已登录，记录搜索历史到后端");
        await this.addToSearchHistoryRecord(this.searchValue.trim());
      } else {
        console.log("用户未登录，只更新本地搜索历史");
        this.addToLocalSearchHistory(this.searchValue.trim());
      }

      await this.performCategorySearch(this.searchValue.trim());
      this.showSearchHistory = false;
    }
  }

  async performCategorySearch(keyword: string) {
    try {
      this.searchLoading = true;
      this.isSearchMode = true;

      const searchResults: any[] = [];
      const lowerKeyword = keyword.toLowerCase();

      this.categories.forEach((category, categoryIndex) => {
        if (category.name.toLowerCase().includes(lowerKeyword)) {
          searchResults.push({
            type: 'category',
            categoryIndex,
            name: category.name,
            id: category.id
          });
        }
      });

      try {
        const mallApi = await import("@/api/mall.api");

        const realCategories = this.categories.filter(cat => cat.id !== 0);

        for (const category of realCategories) {
          try {
            const result = await mallApi.getCategoriesByParentId(category.id);

            if (result && result.data && Array.isArray(result.data)) {
              const matchingSubcategories = result.data.filter((item: any) =>
                item.name.toLowerCase().includes(lowerKeyword)
              );

              matchingSubcategories.forEach((subcategory: any) => {
                const parentCategoryIndex = this.categories.findIndex(cat => cat.id === category.id);

                searchResults.push({
                  type: 'subcategory',
                  categoryIndex: parentCategoryIndex !== -1 ? parentCategoryIndex : 0,
                  categoryName: category.name,
                  name: subcategory.name,
                  subcategory: {
                    id: subcategory.id,
                    name: subcategory.name,
                    image: subcategory.image || "",
                    categoryId: subcategory.id,
                    parentId: subcategory.parentId
                  }
                });
              });
            }
          } catch (categoryError) {
            console.warn(`搜索分类 ${category.name} 的子分类时失败:`, categoryError);
          }
        }
      } catch (apiError) {
        console.error("搜索二级分类时API调用失败:", apiError);
      }

      this.searchResults = searchResults;

      console.log("分类搜索结果:", searchResults);

      if (searchResults.length === 0) {
        uni.showToast({
          title: "未找到相关分类",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("分类搜索失败:", error);
      this.searchResults = [];
      uni.showToast({
        title: "搜索失败",
        icon: "none",
      });
    } finally {
      this.searchLoading = false;
    }
  }



  hideSearchHistory() {
    setTimeout(() => {
      this.showSearchHistory = false;
      if (!this.searchValue) {
        this.showSearchInput = false;
        this.searchInputFocus = false;
      }
    }, 200);
  }

  async addToSearchHistoryRecord(keyword: string) {
    try {
      console.log("尝试添加搜索历史到后端:", keyword, "用户ID:", this.customerId);

      const extendedApi = await import("@/api/mall-extended.api");

      const requestBody = {
        customerId: parseInt(this.customerId),
        keyword: keyword
      };

      await extendedApi.addSearchHistory(requestBody);

      console.log("搜索历史添加成功");
      this.addToLocalSearchHistory(keyword);
    } catch (error) {
      console.error("添加搜索历史失败:", error);
      this.addToLocalSearchHistory(keyword);
    }
  }

  addToLocalSearchHistory(keyword: string) {
    if (!this.searchHistory.includes(keyword)) {
      this.searchHistory.unshift(keyword);
      if (this.searchHistory.length > 10) {
        this.searchHistory.pop();
      }
      console.log("本地搜索历史已更新:", this.searchHistory);
    }
  }

  clearHistory() {
    this.searchHistory = [];
  }

  clearSearch() {
    console.log("清除搜索状态");
    this.searchValue = "";
    this.isSearchMode = false;
    this.searchResults = [];
    this.showSearchHistory = false;
    this.showSearchInput = false;
    this.searchInputFocus = false;
  }

  focusSearch() {
    this.showSearchInput = true;
    this.searchInputFocus = true;
    this.showSearchHistory = true;
  }

  searchHistoryItem(keyword: string) {
    this.searchValue = keyword;
    this.onSearch();
  }

  searchHotItem(keyword: string) {
    this.searchValue = keyword;
    this.onSearch();
  }

  selectSearchResult(result: any) {
    console.log("选择搜索结果:", result);

    if (result.type === 'category') {
      this.selectCategory(result.categoryIndex);
    } else if (result.type === 'subcategory') {
      this.selectCategory(result.categoryIndex);

      setTimeout(() => {
        this.goProductList(result.subcategory);
      }, 100);
    }

    this.clearSearch();
  }

  async selectCategory(index: number) {
    if (this.activeCategory === index) return;

    this.activeCategory = index;

    const itemHeight = 50;
    const containerHeight = 400;
    const targetScrollTop = Math.max(0, index * itemHeight - containerHeight / 2 + itemHeight / 2);

    this.$nextTick(() => {
      this.scrollTop = targetScrollTop;
    });

    await this.loadSubcategories();
  }

  goProductList(category: any) {
    uni.navigateTo({
      url: `/pages/product/product-list?categoryId=${category.categoryId || category.id}&categoryName=${encodeURIComponent(category.name)}`,
    });
  }

  goProductDetail(product: any) {
    uni.navigateTo({
      url: `/pages/product/product-detail?id=${product.id}`,
    });
  }

  async quickAddToCart(product: any) {
    const { authManager } = await import("../../utils/auth");
    let userId = this.customerId;
    if (!userId) {
      userId = await authManager.getUserId(false);
      if (!userId) {
        uni.showToast({
          title: "请先登录",
          icon: "none",
        });
        setTimeout(() => {
          uni.switchTab({
            url: "/pages/user-center/user-center",
          });
        }, 1500);
        return;
      }
      this.customerId = userId;
    }

    try {
      const mallApi = await import("@/api/mall.api");
      await mallApi.addToCart({
        customerId: this.customerId,
        productId: product.id,
        name: product.name,
        spec: "",
        price: product.price,
        quantity: 1,
        image: product.image || "",
        selected: 1,
      });

      uni.showToast({
        title: "已加入购物车",
        icon: "success",
      });
    } catch (error) {
      console.error("加入购物车失败:", error);
      uni.showToast({
        title: "加入失败",
        icon: "none",
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.category-page {
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  position: relative;
}

.search-section-fixed {
  background-color: #fff;
  padding: 10px 15px;
  position: fixed;
  top: 88px;
  left: 0;
  right: 0;
  z-index: 95;
  border-bottom: 1px solid #eee;
  height: 60px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

.search-bar,
.search-bar-active,
.search-bar-with-text {
  flex: 1;
  background-color: #f5f5f5;
  border-radius: 20px;
  padding: 8px 15px;
  display: flex;
  align-items: center;
  gap: 8px;
  height: 40px;
  box-sizing: border-box;
}

.search-bar-active {
  border: 1px solid #007aff;
}

.search-placeholder,
.search-placeholder-active {
  font-size: 14px;
  color: #999;
}

.search-text {
  font-size: 14px;
  color: #333;
}

.search-input-real {
  position: absolute;
  top: 50%;
  left: 15px;
  right: 15px;
  transform: translateY(-50%);
  height: 40px;
  background: transparent;
  border: none;
  border-radius: 20px;
  padding: 0 45px 0 45px;
  font-size: 14px;
  color: #333;
  outline: none;
  z-index: 10;
}

.search-input-real::placeholder {
  color: transparent;
}

.search-suggestions {
  position: absolute;
  top: 100%;
  left: 15px;
  right: 15px;
  background-color: #fff;
  border: 1px solid #eee;
  border-top: none;
  border-radius: 0 0 8px 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1001;
  max-height: 300px;
  overflow-y: auto;
}

.search-history,
.hot-search {
  padding: 15px;
  border-bottom: 1px solid #f5f5f5;
}

.suggestion-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

.delete-icon {
  font-size: 16px;
  color: #999;
  padding: 4px;
}

.history-tags,
.hot-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.history-tag,
.hot-tag {
  background-color: #f5f5f5;
  color: #666;
  font-size: 12px;
  padding: 6px 12px;
  border-radius: 15px;

  &:active {
    background-color: #e0e0e0;
  }
}

.hot-tag {
  background-color: #fff2e8;
  color: #ff6b35;
}

.empty-hot-search {
  padding: 15px;
  text-align: center;
}

.empty-text {
  font-size: 13px;
  color: #999;
}

.category-main {
  flex: 1;
  display: flex;
  height: calc(100vh - 88px - 60px);
  margin-top: 60px;
  overflow: hidden;
}

.category-sidebar-fixed {
  width: 100px;
  background-color: #f8f8f8;
  border-right: 1px solid #eee;
  position: fixed;
  left: 0;
  top: 88px;
  bottom: 50px;
  z-index: 90;
  display: flex;
  flex-direction: column;
}

.category-sidebar-fixed::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background-color: #f8f8f8;
  border-right: 1px solid #eee;
}

.category-sidebar-scroll {
  flex: 1;
  height: 100%;
  margin-top: 60px;
}

.category-item {
  min-height: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #eee;
  padding: 8px 4px;
  box-sizing: border-box;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:active {
    background-color: #e8f4fd;
  }

  &.active {
    background-color: #fff;
    border-right: 2px solid #007aff;

    .category-name {
      color: #007aff;
      font-weight: bold;
    }
  }
}

.category-name {
  font-size: 12px;
  color: #333;
  text-align: center;
  line-height: 1.2;
  word-break: break-all;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.subcategory-content {
  flex: 1;
  background-color: #f5f5f5;
  height: 100%;
  margin-left: 100px;
  width: calc(100% - 100px);
}

.subcategory-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  padding: 15px;
}

.subcategory-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px 10px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
  min-height: 100px;
  box-sizing: border-box;
}

.subcategory-item:active {
  transform: scale(0.95);
}

.subcategory-image {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  margin-bottom: 10px;
}

.subcategory-image-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
}

.subcategory-image-placeholder {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  background-color: #f5f5f5;
  border: 1px dashed #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
}

.placeholder-text {
  font-size: 12px;
  color: #999;
}

.subcategory-name {
  font-size: 14px;
  color: #333;
  text-align: center;
  line-height: 1.3;
  font-weight: 500;
}

.product-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  padding: 15px;
}

.product-item {
  width: calc(50% - 5px);
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.product-image {
  width: 100%;
  height: 120px;
}

.product-info {
  padding: 8px;
}

.product-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 5px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.3;
  height: 38px;
}

.price-section {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.product-price {
  font-size: 16px;
  color: #ff4444;
  font-weight: bold;
}

.original-price {
  font-size: 12px;
  color: #999;
  text-decoration: line-through;
}

.product-actions {
  display: flex;
  justify-content: flex-end;
}
.search-results {
  padding: 15px;
}

.category-products {
  height: 100%;
}

.loading-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 250px);
  padding: 50px 15px;
  gap: 20px;
}

.search-result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.result-count {
  font-size: 14px;
  color: #666;
}

.clear-search {
  font-size: 14px;
  color: #007aff;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 50px 0;
}

.loading-text {
  font-size: 14px;
  color: #999;
}

.no-results {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 50px 0;
}

.no-results-text {
  font-size: 14px;
  color: #999;
}

.result-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.result-item:active {
  background-color: #e9ecef;
}

.result-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.result-name {
  font-size: 15px;
  color: #333;
  font-weight: 500;
}

.result-category {
  font-size: 12px;
  color: #666;
}

.result-type {
  font-size: 12px;
  color: #007aff;
  background-color: #e8f4fd;
  padding: 4px 8px;
  border-radius: 12px;
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 20px;
  gap: 20px;
}
</style>
