<template>
  <view class="product-detail-page" :style="{ height: contentHeight  + 'px', paddingTop: topHeight + 'px' }">
    <navigation-bar style="width: 100%; z-index: 99" :showIcon="true" :showText="true" text="商品详情"></navigation-bar>

    <scroll-view scroll-y class="detail-content">
      <!-- 商品图片 -->
      <view class="product-images">
        <swiper class="image-swiper" indicator-dots indicator-color="rgba(255,255,255,0.5)" indicator-active-color="#fff">
          <swiper-item v-for="(image, index) in productImages" :key="index">
            <image :src="image" class="product-image" mode="aspectFill" />
          </swiper-item>
        </swiper>
      </view>

      <!-- 商品信息 -->
      <view class="product-info">
        <view class="price-section">
          <text class="current-price">¥{{ product.price }}</text>
          <text class="original-price" v-if="product.originalPrice">¥{{ product.originalPrice }}</text>
        </view>
        <view class="product-title">{{ product.name }}</view>
        <view class="product-subtitle">{{ product.subtitle }}</view>

        <!-- 优惠信息 -->
        <view class="promotion-section" v-if="promotions.length > 0">
          <view class="promotion-item" v-for="(promo, index) in promotions" :key="index">
            <text class="promotion-tag">{{ promo.type }}</text>
            <text class="promotion-text">{{ promo.text }}</text>
          </view>
        </view>

        <!-- 规格选择 -->
        <view v-if="specs.length > 0" class="spec-section" @click.stop="handleSpecClick">
          <text class="spec-label">规格</text>
          <text class="spec-value">{{ selectedSpec.name || "请选择规格" }}</text>
          <van-icon name="arrow" size="16" color="#999" />
        </view>
      </view>

      <!-- 商品详情 -->
      <view class="detail-section">
        <van-tabs :active="activeTab" @change="onTabChange" sticky>
          <van-tab title="商品详情">
            <view class="tab-content">
              <view class="detail-content-text">
                <text>{{ product.description }}</text>
              </view>
              <view class="detail-images">
                <image v-for="(image, index) in detailImages" :key="index" :src="image" class="detail-image" mode="widthFix" />
              </view>
            </view>
          </van-tab>

          <van-tab title="商品评价" :info="reviews.length > 0 ? reviews.length : ''">
            <view class="tab-content">
              <view class="review-summary">
                <view class="rating-overview">
                  <text class="rating-score">{{ averageRating }}</text>
                  <van-rate :value="averageRating" readonly size="16" />
                  <text class="rating-count">{{ reviews.length }}条评价</text>
                </view>
              </view>

              <view v-if="reviews.length === 0" class="no-reviews">
                <van-empty description="暂无评价" />
              </view>

              <view v-else class="review-list">
                <view v-for="(review, index) in reviews" :key="index" class="review-item">
                  <view class="review-header">
                    <view v-if="review.avatar" class="reviewer-avatar-container">
                      <image :src="review.avatar" class="reviewer-avatar" mode="aspectFill" />
                    </view>
                    <view v-else class="reviewer-avatar-placeholder">
                      <text class="avatar-placeholder-text">用户</text>
                    </view>
                    <view class="reviewer-info">
                      <text class="reviewer-name">{{ review.username }}</text>
                      <van-rate :value="review.rating" readonly size="12" />
                    </view>
                    <text class="review-date">{{ review.date }}</text>
                  </view>
                  <view class="review-content">{{ review.content }}</view>
                  <view v-if="review.images && review.images.length > 0" class="review-images">
                    <view v-for="(img, imgIndex) in review.images.slice(0, 3)" :key="imgIndex" class="review-image-wrapper" @click="handleImageClick(review.id, imgIndex)">
                      <image :src="img" class="review-image" mode="aspectFill" />
                      <view v-if="imgIndex === 2 && review.images.length > 3" class="image-count-overlay">
                        <text class="count-text">+{{ review.images.length - 3 }}</text>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </van-tab>

          <van-tab title="相关推荐">
            <view class="tab-content">
              <view class="recommend-grid">
                <view v-for="(item, index) in recommendProducts" :key="index" class="recommend-item" @click.stop="handleRecommendClick($event, item)">
                  <view v-if="item.image" class="recommend-image-container">
                    <image :src="item.image" class="recommend-image" mode="aspectFill" />
                  </view>
                  <view v-else class="recommend-image-placeholder">
                    <text class="placeholder-text">无图片</text>
                  </view>
                  <view class="recommend-info">
                    <text class="recommend-name">{{ item.name }}</text>
                    <text class="recommend-price">¥{{ item.price }}</text>
                  </view>
                </view>
              </view>
            </view>
          </van-tab>
        </van-tabs>
      </view>
    </scroll-view>

    <!-- 底部操作栏 -->
    <view class="bottom-actions" @click.stop>
      <view class="action-buttons">
        <view class="action-btn" @click.stop="handleActionClick($event, 'favorite')">
          <van-icon :name="isFavorite ? 'star' : 'star-o'" size="20" :color="isFavorite ? '#ff4444' : '#666'" />
          <text class="action-text">收藏</text>
        </view>
        <view class="action-btn" @click.stop="handleActionClick($event, 'share')">
          <van-icon name="share-o" size="20" color="#666" />
          <text class="action-text">分享</text>
        </view>
        <view class="action-btn" @click.stop="handleActionClick($event, 'cart')">
          <van-icon name="shopping-cart-o" size="20" color="#666" />
          <text class="action-text">购物车</text>
          <view v-if="cartCount > 0" class="cart-badge">{{ cartCount }}</view>
        </view>
      </view>
      <view class="purchase-buttons">
        <view class="btn btn-secondary" @click.stop="handleActionClick($event, 'addToCart')">加入购物车</view>
        <view class="btn btn-primary" @click.stop="handleActionClick($event, 'buyNow')">立即购买</view>
      </view>
    </view>

    <!-- 规格选择弹窗 -->
    <van-popup :show="showSpecPopup" position="bottom" round @close="closeSpecPopup">
      <view class="spec-popup" @click.stop>
        <view class="spec-header">
          <view class="spec-product-info">
            <image :src="productImages[0] || ''" class="spec-image" mode="aspectFill" />
            <view class="spec-info">
              <text class="spec-price">¥{{ selectedSpec.price || product.price }}</text>
              <text class="spec-stock">库存{{ selectedSpec.stock || product.stock }}件</text>
            </view>
          </view>
          <van-icon name="cross" size="20" @click.stop="closeSpecPopup" />
        </view>

        <view class="spec-options">
          <view class="spec-group">
            <text class="spec-group-title">规格</text>
            <view class="spec-items">
              <text v-for="(spec, index) in specs" :key="index" class="spec-item" :class="{ active: selectedSpecIndex === index }" @click.stop="handleSpecSelect($event, index)">
                {{ spec.name }}
              </text>
            </view>
          </view>
        </view>

        <view class="spec-footer">
          <van-button type="primary" block @click.stop="handleConfirmSpec">
            {{ specAction === "addToCart" ? "加入购物车" : specAction === "buyNow" ? "立即购买" : "确定" }}
          </van-button>
        </view>
      </view>
    </van-popup>
  </view>
</template>

<script lang="ts">
import Vue from "vue";
import { Component } from "vue-property-decorator";
import NavigationBar from "../../components/common/NavigationBar.vue";
import { addToCart, getProductDetail, getCartItemCount, addFavorite, removeFavorite, getRecommendProducts, checkFavorite } from "../../api/mall.api";
import { addFootprint } from "../../api/mall-extended.api";
import { authManager } from "../../utils/auth";
import {getLayoutHeights} from "@/utils/get-page-height.util";
import {Code} from "@/constants/enum/code.enum";
import {Product} from "@/types/mall.types";

@Component({
  name: "product-detail",
  components: { NavigationBar },
})
export default class ProductDetail extends Vue {
  showSpecPopup = false;
  selectedSpecIndex = 0;
  isFavorite = false;
  activeTab = 0;
  cartCount = 0;
  loading = true;
  productId = "";
  customerId = ""; // 从用户信息获取
  quantity = 1;
  specAction = ""; // 记录规格弹窗的操作类型：'addToCart' 或 'buyNow'

  product: Product = {
    id: 0,
    name: "",
    subtitle: "",
    description: "",
    price: 0,
    originalPrice: 0,
    stock: 0,
    image: "",
    images: [],
    detailImages: [],
    categoryId: 0,
    specs: "",
    tags: [],
    promotions: [],
    isActive: 1,
    salesCount: 0,
    createdAt: "",
    updatedAt: "",
  };

  reviews: any[] = [];
  reviewsLoading = false;

  recommendProducts: any[] = [];

  topHeight = 88;
  contentHeight = 0;

  // 计算属性：处理商品图片
  get productImages() {
    const images = this.parseJsonArray(this.product.images);
    return images.length > 0 ? images : [this.product.image || ""];
  }

  // 计算属性：处理详情图片
  get detailImages() {
    return this.parseJsonArray(this.product.detailImages);
  }

  // 计算属性：处理商品规格
  get specs() {
    const specs = this.parseJsonObjectArray(this.product.specs);
    console.log("解析后的规格数据:", specs);
    return specs;
  }

  // 计算属性：处理促销信息
  get promotions() {
    return this.parseJsonObjectArray(this.product.promotions);
  }

  get selectedSpec() {
    if (!this.specs || this.specs.length === 0) {
      return {
        name: "",
        price: this.product.price,
        stock: this.product.stock,
      };
    }

    const spec = this.specs[this.selectedSpecIndex];
    console.log("当前选中规格索引:", this.selectedSpecIndex, "规格数据:", spec);
    return (
      spec || {
        name: "",
        price: this.product.price,
        stock: this.product.stock,
      }
    );
  }

  get averageRating() {
    if (this.reviews.length === 0) return 0;
    const total = this.reviews.reduce((sum, review) => sum + review.rating, 0);
    return (total / this.reviews.length).toFixed(1);
  }

  // 获取用户ID（静默获取，不弹窗）
  async getUserId(): Promise<string | null> {
    return await authManager.getUserId(false);
  }

  // 通用的JSON数组解析方法
  parseJsonArray(data: any): string[] {
    if (!data) return [];

    try {
      // 如果已经是数组，直接返回
      if (Array.isArray(data)) {
        return data.filter((item) => item && typeof item === "string" && item.trim() !== "");
      }

      // 如果是字符串，尝试解析JSON
      if (typeof data === "string") {
        console.log("解析JSON字符串:", data);
        const parsed = JSON.parse(data);
        if (Array.isArray(parsed)) {
          const filtered = parsed.filter((item) => item && typeof item === "string" && item.trim() !== "").map((item) => item.trim()); // 清理空格
          console.log("解析后的数组:", filtered);
          return filtered;
        }
      }

      return [];
    } catch (e) {
      console.warn("解析JSON数组失败:", e, "原始数据:", data);
      return [];
    }
  }

  // 通用的JSON对象数组解析方法
  parseJsonObjectArray(data: any): any[] {
    if (!data) return [];

    try {
      // 如果已经是数组，直接返回
      if (Array.isArray(data)) {
        return data;
      }

      // 如果是字符串，尝试解析JSON
      if (typeof data === "string") {
        const parsed = JSON.parse(data);
        if (Array.isArray(parsed)) {
          return parsed;
        }
      }

      return [];
    } catch (e) {
      console.warn("解析JSON对象数组失败:", e, "原始数据:", data);
      return [];
    }
  }

  // 页面加载时调用
  async onLoad(options: any) {
    // 静默获取用户ID（不弹窗），用于后续需要登录的操作
    const userId = await this.getUserId();
    if (userId) {
      this.customerId = userId;
    }

    // 无论是否登录都加载商品信息，保证浏览体验
    this.productId = options.id || "";
    if (this.productId) {
      await this.loadProductDetail();
      await this.loadRecommendProducts();
      await this.loadProductReviews();

      // 只有登录用户才加载个人相关数据
      if (this.customerId) {
        await this.checkFavoriteStatus();
        await this.loadCartCount();
        await this.addToFootprint();
      }
    }
  }

  onShow() {
    const {topHeight, contentHeight} = getLayoutHeights();
    this.topHeight = topHeight;
    this.contentHeight = contentHeight;
  }

  // 加载商品详情
  async loadProductDetail() {
    try {
      this.loading = true;
      const result = await getProductDetail(this.productId);
      if (result.code === Code.OK.code) {
        this.product = {...result.data};
      }

      // 调试信息
      console.log("商品详情数据:", this.product);
      console.log("原始规格数据:", this.product.specs);
      console.log("解析后规格数据:", this.specs);

      // 初始化选中规格
      if (this.specs.length > 0) {
        this.selectedSpecIndex = 0;
        console.log("选中的规格:", this.selectedSpec);
      }
    } catch (error) {
      console.error("加载商品详情失败:", error);
      uni.showToast({
        title: "加载失败",
        icon: "none",
      });
    } finally {
      this.loading = false;
    }
  }

  // 检查收藏状态
  async checkFavoriteStatus() {
    try {
      // 暂时设置为未收藏，等待API实现
      const result = await checkFavorite(this.customerId, this.productId);
      this.isFavorite = result.data;
    } catch (error) {
      console.error("检查收藏状态失败:", error);
    }
  }

  // 加载购物车数量
  async loadCartCount() {
    try {
      const result = await getCartItemCount(this.customerId);
      this.cartCount = result.data;
    } catch (error) {
      console.error("加载购物车数量失败:", error);
    }
  }

  // 添加浏览足迹
  async addToFootprint() {
    try {
      await addFootprint(this.customerId, this.productId);
    } catch (error) {
      console.error("添加浏览足迹失败:", error);
    }
  }

  // 统一的动作点击处理方法，防止事件穿透
  handleActionClick(event: any, action: string) {
    // 阻止事件冒泡和默认行为
    if (event) {
      event.stopPropagation();
      event.preventDefault();
    }

    // 防止重复点击
    if (this.loading) {
      return;
    }

    // 根据动作类型调用相应的方法
    switch (action) {
      case "favorite":
        this.addToFavorites();
        break;
      case "share":
        this.shareProduct();
        break;
      case "cart":
        this.goCart();
        break;
      case "addToCart":
        this.addToCartAction();
        break;
      case "buyNow":
        this.buyNow();
        break;
      default:
        console.warn("未知的动作类型:", action);
    }
  }

  // 处理规格点击
  handleSpecClick() {
    this.showSpecPopup = true;
  }

  // 处理规格选择
  handleSpecSelect(event: any, index: number) {
    if (event) {
      event.stopPropagation();
      event.preventDefault();
    }
    this.selectSpec(index);
  }

  // 处理确认规格
  handleConfirmSpec(event?: any) {
    if (event) {
      event.stopPropagation();
      event.preventDefault();
    }
    this.confirmSpec();
  }

  // 关闭规格弹窗
  closeSpecPopup() {
    this.showSpecPopup = false;
  }

  // 处理推荐商品点击
  handleRecommendClick(event: any, product: any) {
    if (event) {
      event.stopPropagation();
      event.preventDefault();
    }
    this.goToProduct(product);
  }

  selectSpec(index: number) {
    console.log("选择规格索引:", index, "规格数据:", this.specs[index]);
    this.selectedSpecIndex = index;
    console.log("更新后的选中规格:", this.selectedSpec);
  }

  async confirmSpec() {
    // 根据操作类型执行相应的逻辑
    if (this.specAction === "addToCart") {
      await this.confirmAddToCart();
    } else if (this.specAction === "buyNow") {
      this.confirmBuyNow();
    } else {
      // 默认关闭弹窗
      this.showSpecPopup = false;
    }
  }

  // 切换收藏状态
  async addToFavorites() {
    console.log("商品详情页面 - 开始收藏操作");
    console.log("商品详情页面 - productId:", this.productId);
    console.log("商品详情页面 - 当前收藏状态:", this.isFavorite);

    // 检查商品ID
    if (!this.productId) {
      uni.showToast({
        title: "商品信息错误",
        icon: "none",
      });
      return;
    }

    // 静默检查登录状态，未登录时直接跳转到登录页面
    let userId = this.customerId;
    if (!userId) {
      userId = await authManager.getUserId(false); // 静默获取，不弹窗
      if (!userId) {
        // 未登录，跳转到登录页面或用户中心
        uni.showToast({
          title: "请先登录",
          icon: "none",
        });
        setTimeout(() => {
          uni.switchTab({
            url: "/pages/user-center/user-center",
          });
        }, 1500);
        return;
      }
      this.customerId = userId;
    }

    try {
      if (this.isFavorite) {
        console.log("商品详情页面 - 执行取消收藏");
        await removeFavorite(this.customerId, this.productId);
        this.isFavorite = false;
        uni.showToast({
          title: "已取消收藏",
          icon: "success",
        });
      } else {
        console.log("商品详情页面 - 执行添加收藏");
        await addFavorite(this.customerId, this.productId);
        this.isFavorite = true;
        uni.showToast({
          title: "已添加收藏",
          icon: "success",
        });
      }
      console.log("商品详情页面 - 收藏操作成功");
    } catch (error) {
      console.error("收藏操作失败:", error);
      uni.showToast({
        title: "操作失败：" + (error.message || "未知错误"),
        icon: "none",
      });
    }
  }

  // 加入购物车
  async addToCartAction() {
    if (this.specs.length > 0) {
      this.specAction = "addToCart"; // 设置操作类型
      this.showSpecPopup = true;
    } else {
      await this.confirmAddToCart();
    }
  }

  // 确认加入购物车
  async confirmAddToCart() {
    try {
      // 检查商品信息
      if (!this.productId) {
        uni.showToast({
          title: "商品信息错误",
          icon: "none",
        });
        return;
      }

      // 静默检查登录状态，未登录时直接跳转
      let userId = this.customerId;
      if (!userId) {
        userId = await authManager.getUserId(false); // 静默获取，不弹窗
        if (!userId) {
          // 未登录，跳转到登录页面或用户中心
          uni.showToast({
            title: "请先登录",
            icon: "none",
          });
          setTimeout(() => {
            uni.switchTab({
              url: "/pages/user-center/user-center",
            });
          }, 1500);
          return;
        }
        this.customerId = userId;
      }

      // 构造加入购物车的数据
      const cartData = {
        customerId: String(this.customerId),
        productId: String(this.productId),
        name: this.product.name || "商品",
        spec: this.selectedSpec?.name || "",
        price: String(this.selectedSpec?.price || this.product.price || 0), // 后端期望字符串格式的价格
        quantity: Number(this.quantity),
        image: this.productImages[0] || "",
        selected: 1,
      };

      console.log("=== 加入购物车调试信息 ===");
      console.log("用户ID:", this.customerId);
      console.log("商品ID:", this.productId);
      console.log("商品信息:", this.product);
      console.log("选中规格:", this.selectedSpec);
      console.log("购买数量:", this.quantity);
      console.log("加入购物车数据:", cartData);
      console.log("========================");

      // 显示加载提示
      uni.showLoading({
        title: "添加中...",
      });

      const response = await addToCart(cartData);
      console.log("加入购物车响应:", response);

      uni.hideLoading();
      uni.showToast({
        title: "已加入购物车",
        icon: "success",
      });

      this.showSpecPopup = false;
      await this.loadCartCount();
    } catch (error) {
      uni.hideLoading();
      console.error("加入购物车失败:", error);

      // 显示具体的错误信息
      let errorMsg = "加入购物车失败";
      if (error && error.message) {
        errorMsg = error.message;
      } else if (error && error.msg) {
        errorMsg = error.msg;
      }

      uni.showToast({
        title: errorMsg,
        icon: "none",
        duration: 3000,
      });
    }
  }

  // 立即购买
  buyNow() {
    if (this.specs.length > 0) {
      this.specAction = "buyNow"; // 设置操作类型
      this.showSpecPopup = true;
    } else {
      this.confirmBuyNow();
    }
  }

  // 确认立即购买
  async confirmBuyNow() {
    try {
      // 验证商品信息
      if (!this.product || !this.product.id) {
        uni.showToast({
          title: "商品信息异常",
          icon: "none",
        });
        return;
      }

      // 验证购买数量
      if (this.quantity <= 0) {
        uni.showToast({
          title: "请选择购买数量",
          icon: "none",
        });
        return;
      }

      // 静默检查登录状态，未登录时直接跳转
      let userId = this.customerId;
      if (!userId) {
        userId = await authManager.getUserId(false); // 静默获取，不弹窗
        if (!userId) {
          // 未登录，跳转到登录页面或用户中心
          uni.showToast({
            title: "请先登录",
            icon: "none",
          });
          setTimeout(() => {
            uni.switchTab({
              url: "/pages/user-center/user-center",
            });
          }, 1500);
          return;
        }
        this.customerId = userId;
      }

      console.log("商品详情页面 - 立即购买商品信息:", {
        productId: this.product.id,
        productName: this.product.name,
        productPrice: this.product.price,
        productQuantity: this.quantity,
        productImage: this.product.image,
        selectedSpec: this.selectedSpec,
      });

      // 构造跳转参数（小程序兼容方式）
      const params = [];
      params.push("from=buyNow");
      params.push(`productId=${this.product.id}`);
      params.push(`productName=${encodeURIComponent(this.product.name)}`);
      params.push(`productPrice=${this.product.price}`);
      params.push(`productQuantity=${this.quantity}`);

      if (this.product.image) {
        params.push(`productImage=${encodeURIComponent(this.product.image)}`);
      }

      if (this.selectedSpec && this.selectedSpec.name) {
        params.push(`productSpec=${encodeURIComponent(this.selectedSpec.name)}`);
      }

      const url = `/pages/orders/order-confirm?${params.join("&")}`;

      console.log("商品详情页面 - 跳转URL:", url);

      // 跳转到订单确认页面
      uni.navigateTo({
        url: url,
      });

      this.showSpecPopup = false;
    } catch (error) {
      console.error("商品详情页面 - 立即购买失败:", error);
      uni.showToast({
        title: "立即购买失败，请重试",
        icon: "none",
      });
    }
  }

  goCart() {
    uni.switchTab({
      url: "/pages/cart/cart",
    });
  }

  onTabChange(index: number) {
    this.activeTab = index;
  }

  shareProduct() {
    uni.showActionSheet({
      itemList: ["微信好友", "朋友圈", "复制链接"],
      success: (res) => {
        const actions = ["微信好友", "朋友圈", "复制链接"];
        uni.showToast({
          title: `分享到${actions[res.tapIndex]}`,
          icon: "success",
        });
      },
    });
  }

  // 加载推荐商品
  async loadRecommendProducts() {
    try {
      console.log("商品详情页 - 开始加载推荐商品");
      const response = await getRecommendProducts(4); // 商品详情页显示4个推荐商品
      console.log("商品详情页 - 推荐商品API响应:", response);

      if (response.code === 0) {
        const products = response.data || [];
        console.log("商品详情页 - 推荐商品数据:", products);

        // 转换数据格式
        this.recommendProducts = products.map((product: any) => ({
          id: String(product.id),
          name: product.name,
          price: product.price,
          image: product.image || "",
        }));

        console.log("商品详情页 - 转换后的推荐商品:", this.recommendProducts);
      } else {
        console.error("商品详情页 - 获取推荐商品失败:", response.msg);
      }
    } catch (error) {
      console.error("商品详情页 - 加载推荐商品异常:", error);
    }
  }

  // 加载商品评价
  async loadProductReviews() {
    try {
      this.reviewsLoading = true;
      console.log("商品详情页 - 开始加载商品评价");

      const mallExtendedApi = await import("@/api/mall-extended.api");
      const response = await mallExtendedApi.getProductReviews(parseInt(this.productId), 1, 10);
      console.log("商品详情页 - 评价API响应:", response);

      if (response.code === 0) {
        const reviews = response.data?.records || response.data || [];
        console.log("商品详情页 - 评价数据:", reviews);

        // 转换数据格式
        this.reviews = reviews.map((review: any, index: number) => {
          // 处理评论图片
          const reviewImages = this.parseJsonArray(review.images).slice(0, 6); // 最多显示6张图片

          return {
            id: review.id || `review_${index}_${Date.now()}`, // 确保每个评论都有唯一ID
            username: this.formatUsername(review.customerName || review.username),
            avatar: review.customerAvatar || "",
            rating: review.rating || 5,
            date: this.formatReviewDate(review.createdAt || review.reviewDate),
            content: review.content || review.reviewContent,
            images: reviewImages,
          };
        });

        console.log("商品详情页 - 转换后的评价数据:", this.reviews);
      } else {
        console.error("商品详情页 - 获取评价失败:", response.msg);
        this.reviews = [];
      }
    } catch (error) {
      console.error("商品详情页 - 加载评价异常:", error);
      this.reviews = [];
    } finally {
      this.reviewsLoading = false;
    }
  }

  // 格式化用户名（脱敏处理）
  formatUsername(username: string): string {
    if (!username) return "匿名用户";
    if (username.length <= 2) return username;
    return username.charAt(0) + "*".repeat(username.length - 2) + username.charAt(username.length - 1);
  }

  // 格式化评价日期
  formatReviewDate(dateStr: string): string {
    if (!dateStr) return "";
    const date = new Date(dateStr);
    return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, "0")}-${date.getDate().toString().padStart(2, "0")}`;
  }

  // 跳转到推荐商品详情
  goToProduct(product: any) {
    if (!product || !product.id) {
      console.error("商品信息不完整:", product);
      return;
    }
    uni.navigateTo({
      url: `/pages/product/product-detail?id=${product.id}`,
    });
  }

  // 处理图片点击事件
  handleImageClick(reviewId: any, imageIndex: number) {
    try {
      console.log("图片点击事件:", { reviewId, imageIndex });

      // 验证参数
      if (typeof imageIndex !== "number" || imageIndex < 0) {
        console.error("无效的图片索引:", imageIndex);
        uni.showToast({
          title: "图片索引错误",
          icon: "none",
        });
        return;
      }

      // 查找对应的评论
      const review = this.reviews.find((r) => r.id === reviewId);
      if (!review) {
        console.error("未找到对应的评论:", reviewId);
        uni.showToast({
          title: "评论数据错误",
          icon: "none",
        });
        return;
      }

      // 验证评论图片数据
      if (!review.images || !Array.isArray(review.images) || review.images.length === 0) {
        console.error("评论图片数据无效:", review.images);
        uni.showToast({
          title: "图片数据无效",
          icon: "none",
        });
        return;
      }

      // 获取当前点击的图片
      const currentImage = review.images[imageIndex];
      if (!currentImage || typeof currentImage !== "string") {
        console.error("当前图片无效:", currentImage);
        uni.showToast({
          title: "图片数据错误",
          icon: "none",
        });
        return;
      }

      console.log("准备预览图片:", { currentImage, allImages: review.images });

      // 调用安全预览方法
      this.safePreviewImage(currentImage, review.images);
    } catch (error) {
      console.error("处理图片点击事件异常:", error);
      uni.showToast({
        title: "图片处理失败",
        icon: "none",
      });
    }
  }

  // 安全的图片预览包装方法
  safePreviewImage(currentImage: any, allImages: any) {
    try {
      console.log("安全预览被调用:", { currentImage, allImages });

      // 严格验证和转换参数
      if (!currentImage) {
        console.error("当前图片为空:", currentImage);
        uni.showToast({ title: "图片参数为空", icon: "none" });
        return;
      }

      if (typeof currentImage !== "string") {
        console.error("当前图片类型错误:", typeof currentImage, currentImage);
        uni.showToast({ title: "图片参数类型错误", icon: "none" });
        return;
      }

      if (currentImage.trim() === "") {
        console.error("当前图片为空字符串");
        uni.showToast({ title: "图片参数为空", icon: "none" });
        return;
      }

      // 处理图片数组
      let imageArray: string[] = [];

      if (!allImages) {
        console.warn("图片列表为空，使用单张图片");
        imageArray = [currentImage.trim()];
      } else if (Array.isArray(allImages)) {
        // 过滤有效图片
        imageArray = allImages
          .filter((img) => {
            return img && typeof img === "string" && img.trim() !== "";
          })
          .map((img) => img.trim());

        if (imageArray.length === 0) {
          console.warn("过滤后无有效图片，使用单张图片");
          imageArray = [currentImage.trim()];
        }
      } else {
        console.warn("图片列表不是数组，使用单张图片:", typeof allImages, allImages);
        imageArray = [currentImage.trim()];
      }

      console.log("处理后的图片数组:", imageArray);

      // 确保数组不为空
      if (!imageArray || imageArray.length === 0) {
        console.error("最终图片数组为空");
        uni.showToast({ title: "图片数据处理失败", icon: "none" });
        return;
      }

      // 调用预览方法
      this.previewImage(currentImage.trim(), imageArray);
    } catch (error) {
      console.error("安全预览异常:", error);
      uni.showToast({ title: "图片预览失败", icon: "none" });
    }
  }

  // 预览评论图片
  previewImage(currentImage: string, allImages: string[]) {
    try {
      console.log("预览图片调用参数:", { currentImage, allImages });

      // 验证参数
      if (!currentImage || typeof currentImage !== "string") {
        console.error("当前图片参数无效:", currentImage);
        uni.showToast({
          title: "图片参数错误",
          icon: "none",
        });
        return;
      }

      if (!Array.isArray(allImages)) {
        console.error("图片数组参数无效:", allImages);
        uni.showToast({
          title: "图片数据错误",
          icon: "none",
        });
        return;
      }

      // 严格过滤和验证URL
      const urls = allImages
        .filter((img) => {
          // 检查是否为有效字符串
          if (!img || typeof img !== "string") {
            console.warn("过滤掉无效图片项:", img);
            return false;
          }

          const trimmed = img.trim();
          // 检查是否为空字符串
          if (trimmed === "") {
            console.warn("过滤掉空字符串图片");
            return false;
          }

          // 检查是否为有效的URL格式
          if (!trimmed.startsWith("http://") && !trimmed.startsWith("https://") && !trimmed.startsWith("/")) {
            console.warn("过滤掉无效URL格式:", trimmed);
            return false;
          }

          return true;
        })
        .map((img) => img.trim()); // 清理空格

      console.log("过滤后的有效图片URLs:", urls);

      if (urls.length === 0) {
        uni.showToast({
          title: "暂无有效图片",
          icon: "none",
        });
        return;
      }

      // 确保当前图片在有效列表中
      const current = urls.includes(currentImage.trim()) ? currentImage.trim() : urls[0];
      console.log("预览当前图片:", current);

      // 再次验证数据
      if (!current || urls.length === 0) {
        console.error("最终验证失败, current:", current, "urls:", urls);
        uni.showToast({
          title: "图片数据验证失败",
          icon: "none",
        });
        return;
      }

      uni.previewImage({
        current: current,
        urls: urls,
        success: () => {
          console.log("图片预览成功");
        },
        fail: (err) => {
          console.error("预览图片失败:", err);
          uni.showToast({
            title: "图片预览失败",
            icon: "none",
          });
        },
      });
    } catch (error) {
      console.error("预览图片异常:", error);
      uni.showToast({
        title: "图片预览失败",
        icon: "none",
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.product-detail-page {
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.detail-content {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 80px; /* 为底部固定操作栏留出空间 */
}

.product-images {
  height: 375px;
  background-color: #fff;
}

.image-swiper {
  height: 100%;
}

.product-image {
  width: 100%;
  height: 100%;
}

.product-info {
  background-color: #fff;
  padding: 20px;
  margin-bottom: 10px;
}

.price-section {
  display: flex;
  align-items: baseline;
  margin-bottom: 10px;
}

.current-price {
  font-size: 24px;
  color: #ff4444;
  font-weight: bold;
  margin-right: 10px;
}

.original-price {
  font-size: 14px;
  color: #999;
  text-decoration: line-through;
}

.product-title {
  font-size: 18px;
  color: #333;
  font-weight: bold;
  margin-bottom: 5px;
}

.product-subtitle {
  font-size: 14px;
  color: #666;
  margin-bottom: 15px;
}

.promotion-section {
  margin-bottom: 15px;
}

.promotion-item {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.promotion-tag {
  background-color: #ff4444;
  color: #fff;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 2px;
  margin-right: 8px;
}

.promotion-text {
  font-size: 12px;
  color: #ff4444;
}

.spec-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-top: 1px solid #f5f5f5;
}

.spec-label {
  font-size: 14px;
  color: #333;
}

.spec-value {
  font-size: 14px;
  color: #666;
  flex: 1;
  text-align: right;
  margin-right: 10px;
}

.detail-section {
  background-color: #fff;
  padding: 20px;
}

.detail-title {
  font-size: 16px;
  color: #333;
  font-weight: bold;
  margin-bottom: 15px;
}

.detail-content-text {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

.detail-images {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.detail-image {
  width: 100%;
}

.bottom-actions {
  background-color: #fff;
  padding: 12px 16px calc(12px + env(safe-area-inset-bottom));
  display: flex;
  align-items: center;
  border-top: 1px solid #eee;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.06);
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
}

/* iOS <= 11 的兼容写法（constant） */
@supports (padding-bottom: constant(safe-area-inset-bottom)) {
  .bottom-actions {
    padding-bottom: calc(12px + constant(safe-area-inset-bottom));
  }
}

.action-buttons {
  display: flex;
  margin-right: 15px;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 20px;
  position: relative;
}

.action-text {
  font-size: 10px;
  color: #666;
  margin-top: 2px;
}

.purchase-buttons {
  flex: 1;
  display: flex;
  gap: 10px;
}

.purchase-buttons {
  flex: 1;
  display: flex;
  gap: 8px;
}
.btn {
  flex: 1;
  height: 36px;
  border-radius: 18px;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.btn-primary {
  background: linear-gradient(90deg, #0289ea 0%, #95c4f6 100%);
  color: #fff;
}
.btn-secondary {
  background: #ff8f1f;
  color: #fff;
}

/* 规格弹窗样式 */
.spec-popup {
  padding: 20px;
  max-height: 60vh;
}

.spec-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.spec-product-info {
  display: flex;
  flex: 1;
}

.spec-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  margin-right: 15px;
}

.spec-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.spec-price {
  font-size: 18px;
  color: #ff4444;
  font-weight: bold;
  margin-bottom: 5px;
}

.spec-stock {
  font-size: 12px;
  color: #666;
}

.spec-options {
  margin-bottom: 30px;
}

.spec-group-title {
  font-size: 14px;
  color: #333;
  margin-bottom: 10px;
}

.spec-items {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.spec-item {
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  color: #333;

  &.active {
    border-color: #007aff;
    color: #007aff;
    background-color: #f0f8ff;
  }
}

/* 新增样式 */
.cart-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: #ff4444;
  color: #fff;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 16px;
  text-align: center;
}

.tab-content {
  padding: 20px;
}

/* 评价相关样式 */
.review-summary {
  background-color: #f8f8f8;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.rating-overview {
  display: flex;
  align-items: center;
  gap: 10px;
}

.rating-score {
  font-size: 24px;
  font-weight: bold;
  color: #ff4444;
}

.rating-count {
  font-size: 14px;
  color: #666;
}

.no-reviews {
  padding: 50px 0;
}

.review-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.review-item {
  background-color: #f8f8f8;
  padding: 15px;
  border-radius: 8px;
}

.review-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.reviewer-avatar-container {
  margin-right: 10px;
}

.reviewer-avatar {
  width: 40px;
  height: 40px;
  border-radius: 20px;
}

.reviewer-avatar-placeholder {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}

.avatar-placeholder-text {
  font-size: 12px;
  color: #999;
}

.reviewer-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.reviewer-name {
  font-size: 14px;
  color: #333;
  font-weight: bold;
}

.review-date {
  font-size: 12px;
  color: #999;
}

.review-content {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 10px;
}

.review-images {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-top: 10px;
}

.review-image-wrapper {
  position: relative;
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
}

.review-image {
  width: 80px;
  height: 80px;
  border-radius: 6px;
  transition: transform 0.2s ease;
}

.review-image-wrapper:active .review-image {
  transform: scale(0.95);
}

.image-count-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
}

.count-text {
  color: #fff;
  font-size: 16px;
  font-weight: bold;
}

/* 推荐商品样式 */
.recommend-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  padding: 0 5px;
}

.recommend-item {
  width: calc(50% - 5px);
  background-color: #f8f8f8;
  border-radius: 8px;
  overflow: hidden;
}

.recommend-image-container {
  width: 100%;
  height: 120px;
}

.recommend-image {
  width: 100%;
  height: 120px;
}

.recommend-image-placeholder {
  width: 100%;
  height: 120px;
  background-color: #f5f5f5;
  border: 1px dashed #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
}

.placeholder-text {
  font-size: 12px;
  color: #999;
}

.recommend-info {
  padding: 8px;
}

.recommend-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 5px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.3;
  height: 38px; /* 确保2行文字完整显示 */
}

.recommend-price {
  font-size: 16px;
  color: #ff4444;
  font-weight: bold;
}
</style>
