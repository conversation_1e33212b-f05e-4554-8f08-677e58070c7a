<template>
  <view class="add-product-page">
    <navigation-bar style="width: 100%; z-index: 99" :showIcon="true" :showText="true" text="添加商品"></navigation-bar>

    <scroll-view scroll-y class="form-content">
      <view class="form-container">
        <!-- 商品图片 -->
        <view class="form-section">
          <view class="section-title">商品图片</view>
          <view class="image-upload-section">
            <view class="main-image-upload">
              <text class="upload-label">主图 (必填)</text>
              <view class="image-grid">
                <view v-for="(image, index) in productForm.images" :key="index" class="image-item">
                  <image :src="image" class="uploaded-image" mode="aspectFill" />
                  <view class="image-delete" @click="removeImage(index)">
                    <van-icon name="cross" size="12" color="#fff" />
                  </view>
                </view>
                <view v-if="productForm.images.length < 5" class="upload-placeholder" @click="uploadImage">
                  <van-icon name="plus" size="24" color="#999" />
                  <text class="upload-text">添加图片</text>
                </view>
              </view>
              <text class="upload-tip">最多上传5张图片，建议尺寸750x750</text>
            </view>
          </view>
        </view>

        <!-- 基本信息 -->
        <view class="form-section">
          <view class="section-title">基本信息</view>
          <van-field v-model="productForm.name" label="商品名称" placeholder="请输入商品名称" required />
          <van-field v-model="productForm.subtitle" label="商品副标题" placeholder="请输入商品副标题" />
          <van-field v-model="productForm.description" label="商品描述" type="textarea" placeholder="请输入商品描述" autosize />
        </view>

        <!-- 分类选择 -->
        <view class="form-section">
          <view class="section-title">商品分类</view>
          <van-field :value="selectedCategoryName" label="选择分类" placeholder="请选择商品分类" readonly @click="showCategoryPicker = true" />
        </view>

        <!-- 价格设置 -->
        <view class="form-section">
          <view class="section-title">价格设置</view>
          <van-field v-model="productForm.price" label="销售价格" placeholder="0.00" type="digit" required />
          <van-field v-model="productForm.originalPrice" label="原价" placeholder="0.00" type="digit" />
        </view>

        <!-- 库存设置 -->
        <view class="form-section">
          <view class="section-title">库存设置</view>
          <van-field v-model="productForm.stock" label="库存数量" placeholder="0" type="number" required />
        </view>

        <!-- 商品规格 -->
        <view class="form-section">
          <view class="section-title">
            <text>商品规格</text>
            <van-button size="mini" type="primary" @click="addSpec">添加规格</van-button>
          </view>
          <view v-if="productForm.specs.length === 0" class="no-specs">
            <text>暂无规格，点击上方按钮添加</text>
          </view>
          <view v-else class="spec-list">
            <view v-for="(spec, index) in productForm.specs" :key="index" class="spec-item">
              <van-field v-model="spec.name" label="规格名称" placeholder="如：500g装" />
              <van-field v-model="spec.price" label="规格价格" placeholder="0.00" type="digit" />
              <van-field v-model="spec.stock" label="规格库存" placeholder="0" type="number" />
              <van-button size="mini" type="danger" @click="removeSpec(index)">删除</van-button>
            </view>
          </view>
        </view>

        <!-- 商品标签 -->
        <view class="form-section">
          <view class="section-title">商品标签</view>
          <view class="tag-input-section">
            <van-field v-model="newTag" placeholder="输入标签名称" />
            <van-button size="small" type="primary" @click="addTag">添加</van-button>
          </view>
          <view v-if="productForm.tags.length > 0" class="tag-list">
            <view v-for="(tag, index) in productForm.tags" :key="index" class="tag-item">
              <text>{{ tag }}</text>
              <van-icon name="cross" size="12" @click="removeTag(index)" />
            </view>
          </view>
        </view>

        <!-- 商品状态 -->
        <view class="form-section">
          <view class="section-title">商品状态</view>
          <van-cell title="立即上架" center>
            <van-switch v-model="productForm.isActive" />
          </van-cell>
        </view>
      </view>
    </scroll-view>

    <!-- 底部操作栏 -->
    <view class="bottom-actions">
      <van-button class="save-draft-btn" @click="saveDraft">保存草稿</van-button>
      <van-button type="primary" class="submit-btn" @click="submitProduct">{{ productForm.isActive ? '发布商品' : '保存商品' }}</van-button>
    </view>

    <!-- 分类选择弹窗 -->
    <van-popup :show="showCategoryPicker" position="bottom" round @close="showCategoryPicker = false">
      <view class="category-picker">
        <view class="picker-header">
          <text class="picker-title">选择分类</text>
          <van-icon name="cross" size="20" @click="showCategoryPicker = false" />
        </view>
        <view class="category-list">
          <view v-for="(category, index) in categories" :key="index" class="category-option" :class="{ active: selectedCategory === category.id }" @click="selectCategory(category)">
            <text>{{ category.name }}</text>
            <van-icon v-if="selectedCategory === category.id" name="success" size="16" color="#007aff" />
          </view>
        </view>
      </view>
    </van-popup>
  </view>
</template>

<script lang="ts">
import Vue from "vue";
import { Component } from "vue-property-decorator";
import NavigationBar from "@/components/common/NavigationBar.vue";

interface ProductSpec {
  name: string;
  price: string;
  stock: string;
}

interface ProductForm {
  name: string;
  subtitle: string;
  description: string;
  price: string;
  originalPrice: string;
  stock: string;
  images: string[];
  specs: ProductSpec[];
  tags: string[];
  categoryId: number;
  isActive: boolean;
}

@Component({
  name: "add-product",
  components: { NavigationBar },
})
export default class AddProduct extends Vue {
  showCategoryPicker = false;
  selectedCategory = 0;
  newTag = "";

  productForm: ProductForm = {
    name: "",
    subtitle: "",
    description: "",
    price: "",
    originalPrice: "",
    stock: "",
    images: [],
    specs: [],
    tags: [],
    categoryId: 0,
    isActive: true,
  };

  categories = [
    { name: "热门推荐", id: 1 },
    { name: "新鲜水果", id: 2 },
    { name: "蔬菜蛋品", id: 3 },
    { name: "肉禽水产", id: 4 },
    { name: "米面粮油", id: 5 },
    { name: "休闲零食", id: 6 },
    { name: "酒水饮料", id: 7 },
    { name: "家居用品", id: 8 },
  ];

  get selectedCategoryName() {
    const category = this.categories.find(cat => cat.id === this.selectedCategory);
    return category ? category.name : "请选择分类";
  }

  uploadImage() {
    uni.chooseImage({
      count: 5 - this.productForm.images.length,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        this.productForm.images.push(...res.tempFilePaths);
      }
    });
  }

  removeImage(index: number) {
    this.productForm.images.splice(index, 1);
  }

  selectCategory(category: any) {
    this.selectedCategory = category.id;
    this.productForm.categoryId = category.id;
    this.showCategoryPicker = false;
  }

  addSpec() {
    this.productForm.specs.push({
      name: "",
      price: "",
      stock: "",
    });
  }

  removeSpec(index: number) {
    this.productForm.specs.splice(index, 1);
  }

  addTag() {
    if (this.newTag.trim() && !this.productForm.tags.includes(this.newTag.trim())) {
      this.productForm.tags.push(this.newTag.trim());
      this.newTag = "";
    }
  }

  removeTag(index: number) {
    this.productForm.tags.splice(index, 1);
  }

  validateForm() {
    if (!this.productForm.name.trim()) {
      uni.showToast({ title: "请输入商品名称", icon: "none" });
      return false;
    }
    if (this.productForm.images.length === 0) {
      uni.showToast({ title: "请上传商品图片", icon: "none" });
      return false;
    }
    if (!this.productForm.price.trim()) {
      uni.showToast({ title: "请输入销售价格", icon: "none" });
      return false;
    }
    if (!this.productForm.stock.trim()) {
      uni.showToast({ title: "请输入库存数量", icon: "none" });
      return false;
    }
    if (this.productForm.categoryId === 0) {
      uni.showToast({ title: "请选择商品分类", icon: "none" });
      return false;
    }
    return true;
  }

  saveDraft() {
    uni.showToast({ title: "草稿已保存", icon: "success" });
  }

  submitProduct() {
    if (!this.validateForm()) return;
    
    uni.showLoading({ title: "提交中..." });
    
    // 模拟提交延迟
    setTimeout(() => {
      uni.hideLoading();
      uni.showToast({ 
        title: this.productForm.isActive ? "商品发布成功" : "商品保存成功", 
        icon: "success" 
      });
      
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    }, 2000);
  }
}
</script>

<style lang="scss" scoped>
.add-product-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.form-content {
  flex: 1;
  overflow-y: auto;
}

.form-container {
  padding: 15px;
}

.form-section {
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 15px;
  overflow: hidden;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  font-size: 16px;
  font-weight: bold;
  color: #333;
  border-bottom: 1px solid #f0f0f0;
}

/* 图片上传样式 */
.image-upload-section {
  padding: 20px;
}

.upload-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
  display: block;
}

.image-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 10px;
}

.image-item {
  position: relative;
  width: 80px;
  height: 80px;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  border-radius: 6px;
}

.image-delete {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: #ff4444;
  border-radius: 10px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-placeholder {
  width: 80px;
  height: 80px;
  border: 2px dashed #ddd;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
}

.upload-text {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

.upload-tip {
  font-size: 12px;
  color: #999;
}

/* 规格相关样式 */
.no-specs {
  padding: 30px;
  text-align: center;
  color: #999;
}

.spec-list {
  padding: 0 20px 20px;
}

.spec-item {
  background-color: #f8f8f8;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  position: relative;
}

/* 标签相关样式 */
.tag-input-section {
  display: flex;
  gap: 10px;
  padding: 20px;
  align-items: center;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 0 20px 20px;
}

.tag-item {
  background-color: #f0f8ff;
  color: #007aff;
  font-size: 12px;
  padding: 6px 12px;
  border-radius: 15px;
  display: flex;
  align-items: center;
  gap: 5px;
}

/* 底部操作栏 */
.bottom-actions {
  background-color: #fff;
  padding: 15px 20px;
  display: flex;
  gap: 15px;
  border-top: 1px solid #eee;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.06);
}

.save-draft-btn {
  flex: 1;
  background-color: #f5f5f5;
  color: #666;
  border: none;
}

.submit-btn {
  flex: 2;
}

/* 分类选择弹窗 */
.category-picker {
  padding: 20px;
  max-height: 60vh;
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.picker-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.category-list {
  max-height: 400px;
  overflow-y: auto;
}

.category-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
  font-size: 16px;
  color: #333;

  &.active {
    color: #007aff;
  }

  &:last-child {
    border-bottom: none;
  }
}
</style>
