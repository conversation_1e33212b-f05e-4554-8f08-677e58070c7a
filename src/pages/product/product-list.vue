<template>
  <view class="product-list-page" :style="{ height: contentHeight  + 'px', paddingTop: topHeight + 'px' }">
    <navigation-bar :showIcon="true" :showText="true" :text="pageTitle" />

    <!-- 搜索栏 -->
    <view class="search-section-fixed">
      <view class="search-bar" @click="focusSearch" v-if="!showSearchInput">
        <van-icon name="search" size="16" color="#999" />
        <text class="search-placeholder">搜索商品</text>
      </view>

      <view class="search-bar-active" v-if="showSearchInput && !searchValue">
        <van-icon name="search" size="16" color="#999" />
        <text class="search-placeholder-active">搜索商品</text>
      </view>

      <view class="search-bar-with-text" v-if="showSearchInput && searchValue">
        <van-icon name="search" size="16" color="#999" />
        <text class="search-text">{{ searchValue }}</text>
      </view>

      <!-- 实际的搜索输入框（隐藏） -->
      <input
        v-if="showSearchInput"
        class="search-input-real"
        v-model="searchValue"
        placeholder="搜索商品"
        @confirm="onSearch"
        @focus="showSearchHistory = true"
        @blur="hideSearchHistory"
        :focus="searchInputFocus"
      />
    </view>

    <!-- 筛选栏 -->
    <view class="filter-section">
      <view class="filter-tabs">
        <view
          v-for="(tab, index) in filterTabs"
          :key="index"
          class="filter-tab"
          :class="{ active: activeFilterTab === index }"
          @click="selectFilterTab(index)"
        >
          <text class="tab-text">{{ tab.name }}</text>
          <text v-if="tab.hasDropdown" class="tab-arrow">{{ getArrowIcon(tab) }}</text>
        </view>
      </view>
    </view>

    <!-- 筛选弹窗 -->
    <view v-if="showFilterPopup" class="filter-mask" @click="closeFilterPopup">
      <view class="filter-drawer" @click.stop>
        <view class="filter-popup">
        <!-- 拖拽指示器 -->
        <view class="drag-indicator"></view>
        <view class="filter-header">
          <text class="filter-title">筛选</text>
          <view class="filter-actions">
            <text class="reset-btn" @click="resetFilter">重置</text>
            <text class="confirm-btn" @click="confirmFilter">确定</text>
          </view>
        </view>

        <scroll-view scroll-y class="filter-content">
          <!-- 价格区间 -->
          <view class="filter-group">
            <text class="group-title">价格区间</text>
            <view class="price-range">
              <input
                v-model="tempFilter.minPrice"
                type="number"
                placeholder="最低价"
                class="price-input"
              />
              <text class="price-separator">-</text>
              <input
                v-model="tempFilter.maxPrice"
                type="number"
                placeholder="最高价"
                class="price-input"
              />
            </view>
          </view>



          <!-- 其他属性筛选 -->
          <view class="filter-group">
            <text class="group-title">商品状态</text>
            <view class="status-list">
              <view
                v-for="status in statusList"
                :key="status.value"
                class="status-item"
                :class="{ selected: tempFilter.status === status.value }"
                @click="selectStatus(status.value)"
              >
                <text class="status-name">{{ status.name }}</text>
              </view>
            </view>
          </view>
        </scroll-view>
        </view>
      </view>
    </view>

    <!-- 商品列表 -->
    <scroll-view
      scroll-y
      class="product-content"
      enable-back-to-top
      @scrolltolower="onScrollToLower"
      :lower-threshold="100"
    >
      <view v-if="loading && products.length === 0" class="loading-container">
        <van-loading size="24px" color="#007aff">加载中...</van-loading>
      </view>

      <view v-else-if="products.length === 0" class="empty-container">
        <van-empty description="暂无商品数据" />
        <van-button type="primary" size="small" @click="loadProducts">重新加载</van-button>
      </view>

      <view v-else class="product-grid">
        <view
          v-for="(product, index) in products"
          :key="index"
          class="product-item"
          @click="goProductDetail(product)"
        >
          <image :src="product.image" class="product-image" mode="aspectFill" />
          <view class="product-info">
            <text class="product-name">{{ product.name }}</text>
            <text class="product-desc">{{ product.description }}</text>
            <view class="price-section">
              <text class="product-price">¥{{ product.price }}</text>
              <text v-if="product.originalPrice" class="original-price">¥{{ product.originalPrice }}</text>
            </view>
            <view class="product-actions">
              <van-button size="mini" type="primary" @click.stop="quickAddToCart(product)">
                加购物车
              </van-button>
            </view>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view v-if="hasMore && products.length > 0" class="load-more">
        <van-loading v-if="loadingMore" size="16px" color="#999">加载更多...</van-loading>
        <text v-else class="load-more-text" @click="loadMore">点击加载更多</text>
      </view>

      <view v-else-if="products.length > 0" class="no-more">
        <text class="no-more-text">没有更多商品了</text>
      </view>
    </scroll-view>
  </view>
</template>

<script lang="ts">
import Vue from "vue";
import { Component } from "vue-property-decorator";
import NavigationBar from "@/components/common/NavigationBar.vue";
import { Code } from "@/constants/enum/code.enum";
import { getProductList } from "@/api/mall.api";
import {getLayoutHeights} from "@/utils/get-page-height.util";

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  originalPrice?: number;
  image: string;
  categoryId?: string;
  categoryName?: string;
  salesCount?: number;
  stock?: number;
}

interface FilterTab {
  name: string;
  key: string;
  hasDropdown: boolean;
}

@Component({
  name: "product-list",
  components: { NavigationBar },
})
export default class ProductList extends Vue {
  searchValue = "";
  showSearchInput = false;
  searchInputFocus = false;
  showSearchHistory = false;
  pageTitle = "商品列表";
  loading = false;
  loadingMore = false;
  hasMore = true;
  showFilterPopup = false;

  topHeight = 88;
  contentHeight = 0;

  // 分页参数
  currentPage = 1;
  pageSize = 20;

  // 筛选参数
  categoryId = "";
  categoryName = "";
  sortType = "default"; // default, price_asc, price_desc, sales
  priceSort = ""; // asc, desc

  activeFilterTab = 0;
  filterTabs: FilterTab[] = [
    { name: "综合", key: "default", hasDropdown: false },
    { name: "价格", key: "price", hasDropdown: true },
    { name: "销量", key: "sales", hasDropdown: false },
    { name: "筛选", key: "filter", hasDropdown: true },
  ];

  // 筛选条件
  filter = {
    minPrice: "",
    maxPrice: "",
    status: ""
  };

  // 临时筛选条件（弹窗中使用）
  tempFilter = {
    minPrice: "",
    maxPrice: "",
    status: ""
  };

  // 状态列表
  statusList = [
    { name: "全部", value: "" },
    { name: "有库存", value: "in_stock" },
    { name: "促销中", value: "on_sale" },
    { name: "新品", value: "new" }
  ];

  products: Product[] = [];

  async onLoad(options: any) {
    console.log("商品列表页面 - onLoad options:", options);

    // 解析参数
    if (options.keyword) {
      this.searchValue = decodeURIComponent(options.keyword);
      this.pageTitle = `搜索"${this.searchValue}"`;
    }

    if (options.categoryId) {
      this.categoryId = options.categoryId;
    }

    if (options.categoryName) {
      this.categoryName = decodeURIComponent(options.categoryName);
      this.pageTitle = this.categoryName;
    }

    // 加载商品列表
    await this.loadProducts();
  }

  onShow() {
    const {topHeight, contentHeight} = getLayoutHeights();
    this.topHeight = topHeight;
    this.contentHeight = contentHeight;
  }

  async loadProducts(isLoadMore = false) {
    if (!isLoadMore) {
      this.loading = true;
      this.currentPage = 1;
      this.products = [];
      this.hasMore = true;
    } else {
      if (this.loadingMore || !this.hasMore) return;
      this.loadingMore = true;
      this.currentPage++;
    }

    try {
      // 构建查询参数
      const params = this.buildQueryParams();
      console.log("查询参数:", params);

      // 调用API
      await this.callProductAPI(params, isLoadMore);
    } catch (error) {
      console.error("加载商品失败:", error);
      uni.showToast({ title: "加载失败", icon: "none" });

      // 如果是加载更多失败，回退页码
      if (isLoadMore) {
        this.currentPage--;
      }
    } finally {
      this.loading = false;
      this.loadingMore = false;
    }
  }

  // 构建查询参数
  buildQueryParams() {
    const params: any = {
      pageNum: this.currentPage,
      pageSize: this.pageSize
    };

    // 搜索关键词（商品名称）
    if (this.searchValue) {
      params.name = this.searchValue;
    }

    // 分类ID
    if (this.categoryId) {
      params.categoryId = parseInt(this.categoryId);
    }

    // 价格区间
    if (this.filter.minPrice) {
      params.minPrice = parseFloat(this.filter.minPrice);
    }
    if (this.filter.maxPrice) {
      params.maxPrice = parseFloat(this.filter.maxPrice);
    }

    // 排序处理
    if (this.sortType === "price_asc") {
      params.sortField = "price";
      params.sortOrder = "asc";
    } else if (this.sortType === "price_desc") {
      params.sortField = "price";
      params.sortOrder = "desc";
    } else if (this.sortType === "sales") {
      params.sortField = "sales_count";
      params.sortOrder = "desc";
    }

    // 商品状态（上架状态）
    if (this.filter.status === "in_stock") {
      params.isActive = 1;
    } else if (this.filter.status === "on_sale") {
      // 促销中的商品可以通过其他字段判断，这里暂时不处理
    } else if (this.filter.status === "new") {
      // 新品可以通过创建时间判断，这里暂时不处理
    }

    return params;
  }

  // 调用商品API
  async callProductAPI(params: any, isLoadMore: boolean) {
    try {
      // 调用真实的API
      const result = await getProductList(params);

      if (result.code === Code.OK.code) {
        const responseData = result.data;
        const newProducts = (responseData.records || []).map((item: any) => ({
          id: String(item.id),
          name: item.name,
          description: item.subtitle || item.description || "",
          price: item.price,
          originalPrice: item.originalPrice,
          image: item.image,
          categoryId: item.categoryId,
          categoryName: item.categoryName,
          salesCount: item.salesCount || 0,
          stock: item.stock || 0
        }));

        if (isLoadMore) {
          this.products.push(...newProducts);
        } else {
          this.products = newProducts;
        }

        // 判断是否还有更多数据
        this.hasMore = responseData.current < responseData.pages;

        console.log(`加载商品成功: ${newProducts.length}个, 总计: ${this.products.length}个`);
      } else {
        // API调用失败，使用模拟数据
        console.warn("API调用失败，使用模拟数据:", result.msg);
        const mockResult = await this.mockAPICall(params);

        const newProducts = mockResult.data.list || [];

        if (isLoadMore) {
          this.products.push(...newProducts);
        } else {
          this.products = newProducts;
        }

        this.hasMore = newProducts.length === this.pageSize;
      }
    } catch (error) {
      console.error("API调用失败，使用模拟数据:", error);

      // API调用失败，使用模拟数据
      const mockResult = await this.mockAPICall(params);
      const newProducts = mockResult.data.list || [];

      if (isLoadMore) {
        this.products.push(...newProducts);
      } else {
        this.products = newProducts;
      }

      this.hasMore = newProducts.length === this.pageSize;
    }
  }

  // 模拟API调用
  async mockAPICall(params: any) {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 800));

    console.log("模拟API调用，参数:", params);

    // 模拟分页数据
    const mockProducts = this.generateMockProducts(params.page);

    return {
      code: 200,
      msg: "success",
      data: {
        list: mockProducts,
        total: 100,
        page: params.page,
        pageSize: params.pageSize
      }
    };
  }

  // 生成模拟商品数据
  generateMockProducts(page: number): Product[] {
    const products: Product[] = [];
    const baseIndex = (page - 1) * this.pageSize;

    for (let i = 0; i < this.pageSize; i++) {
      const index = baseIndex + i;
      products.push({
        id: `product_${index}`,
        name: `商品${index + 1} - ${this.searchValue || this.categoryName || "默认"}`,
        description: `这是第${index + 1}个商品的描述信息`,
        price: Math.floor(Math.random() * 1000) + 10,
        originalPrice: Math.floor(Math.random() * 1500) + 100,
        image: "/static/images/test.jpeg",
        categoryId: this.categoryId,
        categoryName: this.categoryName,
        salesCount: Math.floor(Math.random() * 1000),
        stock: Math.floor(Math.random() * 100) + 1
      });
    }

    // 模拟最后一页数据较少的情况
    if (page >= 5) {
      return products.slice(0, Math.floor(Math.random() * this.pageSize));
    }

    return products;
  }

  // 滚动到底部事件
  onScrollToLower() {
    console.log("滚动到底部，触发加载更多");
    this.loadMore();
  }

  onSearchInput(e: any) {
    this.searchValue = e.detail;
  }

  async onSearch() {
    if (!this.searchValue.trim()) {
      uni.showToast({ title: "请输入搜索关键词", icon: "none" });
      return;
    }

    this.pageTitle = `搜索"${this.searchValue}"`;
    await this.loadProducts();
  }

  selectFilterTab(index: number) {
    const tab = this.filterTabs[index];

    switch (tab.key) {
      case "default":
        this.activeFilterTab = index;
        this.sortType = "default";
        this.priceSort = "";
        this.resetPriceTabName();
        break;
      case "price":
        this.activeFilterTab = index;
        // 切换价格排序
        if (this.priceSort === "asc") {
          this.priceSort = "desc";
          this.sortType = "price_desc";
          tab.name = "价格↓";
        } else {
          this.priceSort = "asc";
          this.sortType = "price_asc";
          tab.name = "价格↑";
        }
        break;
      case "sales":
        this.activeFilterTab = index;
        this.sortType = "sales";
        this.priceSort = "";
        this.resetPriceTabName();
        break;
      case "filter":
        this.showFilterModal();
        return;
    }

    this.loadProducts();
  }

  resetPriceTabName() {
    const priceTab = this.filterTabs.find(tab => tab.key === "price");
    if (priceTab) {
      priceTab.name = "价格";
    }
  }

  getArrowIcon(tab: FilterTab) {
    if (!tab.hasDropdown) return "";
    if (tab.key === "price" && this.priceSort) {
      return this.priceSort === "asc" ? "↑" : "↓";
    }
    return "↓";
  }

  showFilterModal() {
    // 复制当前筛选条件到临时变量
    this.tempFilter = {
      minPrice: this.filter.minPrice,
      maxPrice: this.filter.maxPrice,
      status: this.filter.status
    };
    this.showFilterPopup = true;
  }

  selectStatus(status: string) {
    this.tempFilter.status = status;
  }

  resetFilter() {
    this.tempFilter = {
      minPrice: "",
      maxPrice: "",
      status: ""
    };
  }

  confirmFilter() {
    // 应用筛选条件
    this.filter = {
      minPrice: this.tempFilter.minPrice,
      maxPrice: this.tempFilter.maxPrice,
      status: this.tempFilter.status
    };

    this.showFilterPopup = false;
    this.loadProducts();
  }

  closeFilterPopup() {
    this.showFilterPopup = false;
  }

  focusSearch() {
    this.showSearchInput = true;
    this.searchInputFocus = true;
    this.showSearchHistory = true;
  }

  hideSearchHistory() {
    setTimeout(() => {
      this.showSearchHistory = false;
      if (!this.searchValue) {
        this.showSearchInput = false;
        this.searchInputFocus = false;
      }
    }, 200);
  }

  async loadMore() {
    if (this.loadingMore || !this.hasMore) return;
    await this.loadProducts(true);
  }

  goProductDetail(product: Product) {
    uni.navigateTo({
      url: `/pages/product/product-detail?id=${product.id}`,
    });
  }

  async quickAddToCart(product: Product) {
    try {
      // TODO: 实现加入购物车逻辑
      uni.showToast({
        title: "已加入购物车",
        icon: "success",
      });
    } catch (error) {
      console.error("加入购物车失败:", error);
      uni.showToast({
        title: "加入失败",
        icon: "none",
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.product-list-page {
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.search-section-fixed {
  background-color: #fff;
  padding: 10px 15px;
  position: fixed;
  top: 88px;
  left: 0;
  right: 0;
  z-index: 100;
  border-bottom: 1px solid #eee;
  height: 60px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

.search-bar,
.search-bar-active,
.search-bar-with-text {
  flex: 1;
  background-color: #f5f5f5;
  border-radius: 20px;
  padding: 8px 15px;
  display: flex;
  align-items: center;
  gap: 8px;
  height: 40px;
  box-sizing: border-box;
}

.search-bar-active {
  border: 1px solid #007aff;
}

/* 搜索图标样式已通过van-icon组件设置 */

.search-placeholder,
.search-placeholder-active {
  font-size: 14px;
  color: #999;
}

.search-text {
  font-size: 14px;
  color: #333;
}

.search-input-real {
  position: absolute;
  top: 50%;
  left: 15px;
  right: 15px;
  transform: translateY(-50%);
  height: 40px;
  background: transparent;
  border: none;
  border-radius: 20px;
  padding: 0 45px 0 45px;
  font-size: 14px;
  color: #333;
  outline: none;
  z-index: 10;
}

.search-input-real::placeholder {
  color: transparent;
}

.filter-section {
  background-color: #fff;
  position: fixed;
  top: 148px;
  left: 0;
  right: 0;
  z-index: 99;
  border-bottom: 1px solid #eee;
  height: 44px;
}

.filter-tabs {
  display: flex;
  height: 44px;
}

.filter-tab {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;

  &.active {
    color: #007aff;

    .tab-text {
      color: #007aff;
    }
  }
}

.tab-text {
  font-size: 14px;
  color: #333;
}

.tab-arrow {
  font-size: 12px;
  color: #666;
  margin-left: 4px;
}

.product-content {
  flex: 1;
  margin-top: 104px; /* 搜索栏60px + 筛选栏44px */
  height: calc(100vh - 88px - 104px - 50px);
  padding-top: 0;
}

.loading-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 20px;
  gap: 20px;
}

.product-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  padding: 15px;
}

.product-item {
  width: calc(50% - 5px);
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.product-image {
  width: 100%;
  height: 120px;
}

.product-info {
  padding: 8px;
}

.product-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 5px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.3;
  height: 38px;
}

.product-desc {
  font-size: 12px;
  color: #999;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
}

.price-section {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.product-price {
  font-size: 16px;
  color: #ff4444;
  font-weight: bold;
}

.original-price {
  font-size: 12px;
  color: #999;
  text-decoration: line-through;
}

.product-actions {
  display: flex;
  justify-content: flex-end;
}

.load-more,
.no-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.load-more-text,
.no-more-text {
  font-size: 14px;
  color: #999;
}

.load-more-text {
  color: #007aff;
}

/* 筛选弹窗样式 */
.filter-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
}

.filter-drawer {
  width: 100%;
  max-height: 75%;
  background-color: #f8f9fa;
  animation: slideInUp 0.3s ease;
  border-radius: 20px 20px 0 0;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.filter-popup {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
  border-radius: 20px 20px 0 0;
  overflow: hidden;
}

.drag-indicator {
  width: 40px;
  height: 4px;
  background-color: #d0d0d0;
  border-radius: 2px;
  margin: 12px auto 8px;
}

.filter-header {
  background-color: #fff;
  padding: 20px 25px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 10;
  border-radius: 20px 20px 0 0;
}

.filter-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.filter-actions {
  display: flex;
  gap: 15px;
}

.reset-btn {
  color: #666;
  font-size: 15px;
  padding: 8px 16px;
  border-radius: 6px;
  background-color: #f5f5f5;
  transition: background-color 0.2s ease;
}

.reset-btn:active {
  background-color: #e8e8e8;
}

.confirm-btn {
  color: #fff;
  font-size: 15px;
  font-weight: 500;
  padding: 8px 20px;
  border-radius: 6px;
  background-color: #007aff;
  transition: background-color 0.2s ease;
}

.confirm-btn:active {
  background-color: #0056cc;
}

.filter-content {
  flex: 1;
  padding: 0 5px 80px;
  overflow-y: auto;
  width: 100%;
  box-sizing: border-box;
}

.filter-group {
  background-color: #fff;
  margin: 15px 10px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  width: calc(100% - 20px);
  box-sizing: border-box;
}

.group-title {
  display: block;
  padding: 20px 20px 15px;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #f0f0f0;
  text-align: center;
}

.price-range {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 15px;
  gap: 12px;
  width: 100%;
  box-sizing: border-box;
}

.price-input {
  flex: 1;
  max-width: 100px;
  height: 44px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 0 12px;
  font-size: 15px;
  background-color: #fafafa;
  transition: border-color 0.2s ease;
  text-align: center;
  box-sizing: border-box;
}

.price-input:focus {
  border-color: #007aff;
  background-color: #fff;
}

.price-separator {
  color: #666;
  font-size: 18px;
  font-weight: 500;
}

.status-list {
  padding: 15px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 10px;
  width: 100%;
  box-sizing: border-box;
}

.status-item {
  padding: 12px 8px;
  border: 1px solid #e0e0e0;
  border-radius: 20px;
  font-size: 13px;
  color: #666;
  background-color: #fafafa;
  transition: all 0.2s ease;
  cursor: pointer;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.status-item:active {
  transform: scale(0.95);
}

.status-item.selected {
  border-color: #007aff;
  color: #fff;
  background-color: #007aff;
  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
}

.status-name {
  font-size: 14px;
  font-weight: 500;
}
</style>
