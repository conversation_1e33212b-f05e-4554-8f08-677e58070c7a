<template>
  <view class="refund-apply-page" :style="{ paddingTop: topHeight + 'px' }">
    <!-- 导航栏 -->
    <view class="fixed-header">
      <navigation-bar style="width: 100%; z-index: 99" :showIcon="true" :showText="true" text="申请退款"></navigation-bar>
    </view>

    <view class="refund-content">
      <!-- 订单信息 -->
      <view class="order-info-card">
        <view class="card-title">订单信息</view>
        <view class="order-details">
          <view class="detail-row">
            <text class="label">订单号：</text>
            <text class="value">{{ orderData.orderNumber }}</text>
          </view>
          <view class="detail-row">
            <text class="label">订单金额：</text>
            <text class="value amount">¥{{ orderData.totalAmount }}</text>
          </view>
          <view class="detail-row">
            <text class="label">订单状态：</text>
            <text class="value">{{ orderData.statusText }}</text>
          </view>
        </view>
      </view>

      <!-- 商品列表 -->
      <view class="product-list-card">
        <view class="card-title">退款商品</view>
        <view class="product-list">
          <view v-for="(item, index) in orderData.items" :key="index" class="product-item">
            <image :src="item.image" class="product-image" mode="aspectFill" />
            <view class="product-info">
              <text class="product-name">{{ item.name }}</text>
              <text class="product-spec">{{ item.spec }}</text>
              <view class="product-bottom">
                <text class="product-price">¥{{ item.price }}</text>
                <text class="product-quantity">x{{ item.quantity }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 退款信息 -->
      <view class="refund-info-card">
        <view class="card-title">退款信息</view>

        <!-- 退款类型 -->
        <view class="form-item">
          <text class="form-label">退款类型</text>
          <text class="form-value">仅退款（全额退款）</text>
        </view>

        <!-- 退款金额 -->
        <view class="form-item">
          <text class="form-label">退款金额</text>
          <text class="form-value amount">¥{{ refundForm.refundAmount }}</text>
        </view>

        <!-- 退款原因 -->
        <view class="form-item">
          <text class="form-label required">退款原因</text>
          <picker :range="refundReasons" :value="selectedReasonIndex" @change="onReasonChange">
            <view class="picker-item">
              <text class="picker-text">{{ selectedReason || "请选择退款原因" }}</text>
              <van-icon name="arrow" class="picker-arrow" />
            </view>
          </picker>
        </view>

        <!-- 退款说明 -->
        <view class="form-item">
          <text class="form-label">退款说明</text>
          <textarea v-model="refundForm.refundDescription" class="description-input" placeholder="请详细描述退款原因（选填）" maxlength="500" :show-confirm-bar="false" />
          <view class="char-count">{{ refundForm.refundDescription.length }}/500</view>
        </view>

        <!-- 凭证图片 -->
        <view class="form-item">
          <text class="form-label">退款凭证</text>
          <text class="form-tip">最多可上传5张图片</text>
          <view class="image-upload-area">
            <!-- 已上传的图片 -->
            <view v-for="(image, index) in refundForm.refundImages" :key="index" class="image-item">
              <image :src="image" class="uploaded-image" mode="aspectFill" @click="previewImage(index)" />
              <view class="image-remove" @click="removeImage(index)">
                <van-icon name="cross" size="12" color="#fff" />
              </view>
            </view>

            <!-- 上传按钮 -->
            <view v-if="refundForm.refundImages.length < 5" class="upload-btn" @click="chooseImage">
              <van-icon name="photo" size="24" color="#999" />
              <text class="upload-text">上传图片</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 提交按钮 -->
      <view class="submit-area">
        <van-button type="primary" :loading="submitting" :disabled="!canSubmit" @click="submitRefund" block round>
          {{ submitting ? "提交中..." : "提交退款申请" }}
        </van-button>
      </view>
    </view>
  </view>
</template>

<script lang="ts">
import Vue from "vue";
import { Component } from "vue-property-decorator";
import NavigationBar from "@/components/common/NavigationBar.vue";
import { Order } from "@/types/mall.types";
import { REFUND_REASONS } from "@/types/refund.types";
import { applyRefund, RefundApplyParams } from "@/api/refund.api";
import { getLayoutHeights } from "@/utils/get-page-height.util";
// import { uploadFile } from '@/api/common.api' // 需要根据项目实际的文件上传接口进行配置

interface RefundForm {
  refundAmount: number;
  refundType: string;
  refundReason: string;
  refundDescription: string;
  refundImages: string[];
}

@Component({
  name: "RefundApply",
  components: {
    NavigationBar,
  },
})
export default class RefundApply extends Vue {
  topHeight = 88;
  orderData: Order = {} as Order;
  customerId = "";
  submitting = false;

  refundReasons = REFUND_REASONS;
  selectedReasonIndex = -1;

  refundForm: RefundForm = {
    refundAmount: 0,
    refundType: "FULL",
    refundReason: "",
    refundDescription: "",
    refundImages: [],
  };

  get selectedReason(): string {
    return this.selectedReasonIndex >= 0 ? this.refundReasons[this.selectedReasonIndex] : "";
  }

  get canSubmit(): boolean {
    return this.refundForm.refundReason !== "" && !this.submitting;
  }

  onLoad(options: any) {
    const { topHeight } = getLayoutHeights();
    this.topHeight = topHeight;

    // 获取订单数据和用户ID
    if (options.orderData) {
      try {
        this.orderData = JSON.parse(decodeURIComponent(options.orderData));
        this.refundForm.refundAmount = this.orderData.totalAmount || 0;
      } catch (error) {
        console.error("解析订单数据失败:", error);
        uni.showToast({
          title: "订单数据错误",
          icon: "none",
        });
        uni.navigateBack({});
        return;
      }
    }

    if (options.customerId) {
      this.customerId = options.customerId;
    }

    if (!this.orderData.id || !this.customerId) {
      uni.showToast({
        title: "缺少必要参数",
        icon: "none",
      });
      uni.navigateBack({});
    }
  }

  onReasonChange(e: any) {
    this.selectedReasonIndex = e.detail.value;
    this.refundForm.refundReason = this.refundReasons[this.selectedReasonIndex];
  }

  // 选择图片
  chooseImage() {
    const remainingCount = 5 - this.refundForm.refundImages.length;

    uni.chooseImage({
      count: remainingCount,
      sizeType: ["compressed"],
      sourceType: ["album", "camera"],
      success: (res) => {
        this.uploadImages(res.tempFilePaths as string[]);
      },
    });
  }

  // 上传图片
  async uploadImages(filePaths: string[]) {
    uni.showLoading({ title: "上传中..." });

    try {
      const uploadPromises = filePaths.map(async (filePath) => {
        // 使用实际的文件上传接口
        const uploadResult = await this.uploadSingleFile(filePath);
        return uploadResult;
      });

      const results = await Promise.all(uploadPromises);

      // 添加成功上传的图片URL
      const successUrls = results.filter((url) => url).map((url) => url as string);
      this.refundForm.refundImages.push(...successUrls);

      if (successUrls.length > 0) {
        uni.showToast({
          title: `成功上传${successUrls.length}张图片`,
          icon: "success",
        });
      }
    } catch (error) {
      console.error("图片上传失败:", error);
      uni.showToast({
        title: "图片上传失败",
        icon: "none",
      });
    } finally {
      uni.hideLoading();
    }
  }

  // 上传单个文件（适配项目的文件上传接口）
  async uploadSingleFile(filePath: string): Promise<string | null> {
    try {
      // 获取token
      const tokenObj = uni.getStorageSync("token");
      const token = tokenObj?.value || "";
      // 构建上传URL
      const businessBaseURL = (process.env.VUE_APP_ENABLE_GATEWAY === "on" ? process.env.VUE_APP_MS_GATEWAY_API + process.env.VUE_APP_BUSINESS_API : process.env.VUE_APP_BUSINESS_API) + process.env.VUE_APP_API_PREFIX;
      const uploadUrl = `${businessBaseURL}/video_upload/upload`;
      // 这里需要根据项目实际的文件上传接口进行调整
      const result = await new Promise<string>((resolve, reject) => {
        uni.uploadFile({
          url: uploadUrl, // 替换为实际的上传接口
          filePath: filePath,
          name: "file",
          formData: {
            path: "refund",
          },
          header: {
            Token: token,
          },
          success: (uploadRes) => {
            try {
              const data = JSON.parse(uploadRes.data);
              if (data.code === 0 && data.data) {
                resolve(data.data.url || data.data);
              } else {
                reject(new Error(data.msg || "上传失败"));
              }
            } catch (error) {
              reject(new Error("解析上传结果失败"));
            }
          },
          fail: (error) => {
            reject(error);
          },
        });
      });

      return result;
    } catch (error) {
      console.error("文件上传失败:", error);
      return null;
    }
  }

  // 预览图片
  previewImage(index: number) {
    uni.previewImage({
      urls: this.refundForm.refundImages,
      current: index,
    });
  }

  // 移除图片
  removeImage(index: number) {
    this.refundForm.refundImages.splice(index, 1);
  }

  // 提交退款申请
  async submitRefund() {
    if (!this.canSubmit) {
      return;
    }

    // 验证必填字段
    if (!this.refundForm.refundReason) {
      uni.showToast({
        title: "请选择退款原因",
        icon: "none",
      });
      return;
    }

    this.submitting = true;

    try {
      const params: RefundApplyParams = {
        orderId: this.orderData.id,
        customerId: this.customerId,
        refundType: "FULL" as const,
        refundReason: this.refundForm.refundReason,
        refundDescription: this.refundForm.refundDescription,
        refundImages: this.refundForm.refundImages,
      };

      const response = await applyRefund(params);

      if (response.code === 0) {
        uni.showModal({
          title: "申请成功",
          content: "退款申请已提交，请等待审核。您可以在订单列表中查看退款进度。",
          showCancel: false,
          confirmText: "知道了",
          success: () => {
            uni.navigateBack({});
          },
        });
      } else {
        uni.showToast({
          title: response.msg || "申请失败",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("提交退款申请失败:", error);
      uni.showToast({
        title: "网络错误，请重试",
        icon: "none",
      });
    } finally {
      this.submitting = false;
    }
  }
}
</script>

<style lang="scss" scoped>
.refund-apply-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #fff;
}

.refund-content {
  padding: 15px;
}

.order-info-card,
.product-list-card,
.refund-info-card {
  background-color: #fff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.card-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 15px;
}

.order-details {
  .detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .label {
    font-size: 14px;
    color: #666;
  }

  .value {
    font-size: 14px;
    color: #333;

    &.amount {
      color: #ff4444;
      font-weight: bold;
    }
  }
}

.product-list {
  .product-item {
    display: flex;
    padding: 15px 0;
    border-bottom: 1px solid #f5f5f5;

    &:last-child {
      border-bottom: none;
      padding-bottom: 0;
    }
  }
}

.product-image {
  width: 60px;
  height: 60px;
  border-radius: 6px;
  margin-right: 15px;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 5px;
}

.product-spec {
  font-size: 12px;
  color: #999;
  margin-bottom: 8px;
}

.product-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-price {
  font-size: 14px;
  color: #ff4444;
  font-weight: bold;
}

.product-quantity {
  font-size: 12px;
  color: #666;
}

.form-item {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.form-label {
  display: block;
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;

  &.required::after {
    content: " *";
    color: #ff4444;
  }
}

.form-value {
  font-size: 14px;
  color: #666;

  &.amount {
    color: #ff4444;
    font-weight: bold;
    font-size: 16px;
  }
}

.form-tip {
  display: block;
  font-size: 12px;
  color: #999;
  margin-bottom: 10px;
}

.picker-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.picker-text {
  font-size: 14px;
  color: #333;
}

.picker-arrow {
  color: #999;
}

.description-input {
  width: 100%;
  min-height: 80px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  font-size: 14px;
  color: #333;
  line-height: 1.5;
  resize: none;
}

.char-count {
  text-align: right;
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

.image-upload-area {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.image-item {
  position: relative;
  width: 80px;
  height: 80px;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  border-radius: 6px;
}

.image-remove {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 20px;
  height: 20px;
  background-color: #ff4444;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-btn {
  width: 80px;
  height: 80px;
  border: 2px dashed #ddd;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
}

.upload-text {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

.submit-area {
  padding: 20px 0;
}
</style>
