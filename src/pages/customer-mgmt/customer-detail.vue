 <template>
  <div class="customer-detail-page" :style="{ height: contentHeight  + 'px', paddingTop: topHeight + 'px' }">
    <!-- 导航栏 -->
    <navigation-bar showText :text="`客户详情 - ${customerInfo.nickname}`" showIcon backLevel />

    <!-- 客户信息卡片 -->
    <div class="customer-card">
      <div class="customer-header">
        <van-image round width="80rpx" height="80rpx" :src="customerInfo.avatarUrl || require('@/static/images/defaultAvatar.png')" />
        <div class="customer-info">
          <div class="customer-name">{{ customerInfo.nickname }}</div>
        </div>
        <!-- 按钮区域移动到右侧并改为垂直排列 -->
        <div class="vertical-card-actions">
          <van-button size="small" type="primary" @click="openAssignDialog">分配销售</van-button>
          <van-button size="small" type="primary" @click="openTagDialog">添加标签</van-button>
        </div>
      </div>
      <div class="customer-meta">
        <div class="meta-item">
          <span class="meta-label">手机号</span>
          <span class="meta-value">{{ customerInfo.mobile }}</span>
        </div>
      </div>
    </div>

    <!-- 栏目选择 -->
    <div class="column-selector">
      <van-field readonly clickable is-link :value="selectedColumnName" placeholder="请选择栏目" @click-input="openColumnPicker" />
    </div>

    <!-- 标签页 -->
    <van-tabs sticky animated swipeable @change="onChange">
      <van-tab title="行为轨迹">
        <div class="behavior-container">
          <!-- 行为类型筛选 -->
          <div class="camp-selector">
            <van-field readonly clickable is-link :value="selectedBehaviorTypesText" placeholder="请选择行为轨迹类型" @click-input="openBehaviorTypePicker" />
          </div>

          <!-- 行为轨迹列表 - 添加可滚动容器 -->
          <scroll-view scroll-y :style="{ height: behaviorScrollHeight }" @scrolltolower="onBehaviorScrollToLower" class="behavior-scroll-container">
            <div v-if="behaviorSteps.length > 0">
              <van-steps direction="vertical" :steps="behaviorSteps" />
            </div>
            <!-- 空状态 -->
            <van-empty v-else description="暂无行为轨迹数据" />

            <!-- 加载提示 -->
            <div class="loading-tips">
              <van-loading v-if="behaviorLoading" size="24px">加载中...</van-loading>
              <div v-if="behaviorFinished" class="finished">没有更多了</div>
            </div>
          </scroll-view>

          <!-- 行为类型选择器 - 修改为多选框 -->
          <van-popup :show="showBehaviorTypePicker" position="bottom" round>
            <div class="picker-header-container">
              <div class="picker-header">
                <div class="picker-actions left">
                  <van-button size="small" type="default" @click="showBehaviorTypePicker = false">取消</van-button>
                </div>
                <div class="picker-title">选择行为轨迹类型</div>
                <div class="picker-actions right">
                  <van-button size="small" type="primary" @click="onBehaviorTypeConfirm">确认</van-button>
                </div>
              </div>
            </div>
            <scroll-view scroll-y class="behavior-picker-content">
              <van-checkbox-group :value="tempSelectedBehaviorTypes" @change="onBehaviorTypeChange">
                <van-cell-group>
                  <van-cell v-for="(opt, index) in behaviorTypeOptions" :key="index" :title="opt.label" clickable @click="toggleBehaviorType(opt.value)">
                    <van-checkbox :name="opt.value" />
                  </van-cell>
                </van-cell-group>
              </van-checkbox-group>
            </scroll-view>
          </van-popup>
        </div>
      </van-tab>
      <van-tab title="训练营">
        <div class="camp-container">
          <!-- 新增营期选择器区域 -->
          <div class="camp-selector">
            <van-field readonly clickable is-link :value="selectedCampName" placeholder="请选择营期" @click-input="openCampPicker" />
          </div>

          <!-- 课程数据 -->
          <div v-if="courseData.length > 0" class="course-card-list">
            <div v-for="(course, index) in courseData" :key="index" class="course-card">
              <div class="course-info">
                <div class="course-name">{{ course.courseName }}</div>
                <div class="course-meta">
                  <span class="meta-label">开始时间：</span>
                  <span>{{ course.startTime }}</span>
                </div>

                <div class="course-status">
                  <span class="meta-label">状态：</span>
                  <text v-if="course.arrivalStatus === 0" class="status-tag danger"> 未到课 </text>
                  <text v-else-if="course.arrivalStatus === 1" class="status-tag warning"> 已到课 </text>
                  <text v-else-if="course.arrivalStatus === 2" class="status-tag success"> 已完课 </text>
                </div>

                <div class="course-meta">
                  <span class="meta-label">到课时间：</span>
                  <span>{{ course.arrivalTime || "--" }}</span>
                </div>

                <div class="course-meta">
                  <span class="meta-label">完播时间：</span>
                  <span>{{ course.completeTime || "--" }}</span>
                </div>

                <div class="course-meta">
                  <span class="meta-label">播放时长：</span>
                  <span>{{ secondsToTime(course.playProgress) }}</span>
                </div>
              </div>
            </div>

            <!-- 修改：加载提示调整 -->
            <div class="loading-tips">
              <van-loading v-if="courseLoading" size="24px">加载中...</van-loading>
              <div v-if="courseFinished && courseData.length > 0" class="finished">没有更多了</div>
            </div>
          </div>
          <!-- 新增：空状态提示 -->
          <van-empty v-else description="暂无课程数据" />
        </div>

        <!-- 新增营期选择器弹窗 -->
        <van-popup :show="showCampPicker" position="bottom" round>
          <div class="picker-header-container">
            <div class="picker-header">
              <div class="picker-title">选择营期</div>
            </div>
          </div>
          <scroll-view scroll-y class="camp-picker-content">
            <van-picker show-toolbar :columns="campColumns" @confirm="onCampConfirm" @cancel="showCampPicker = false" />
          </scroll-view>
        </van-popup>
      </van-tab>
      <van-tab title="操作记录">
        <div class="operation-container">
          <scroll-view scroll-y :style="{ height: operationScrollHeight }" @scrolltolower="onOperationScrollToLower" class="operation-scroll-container">
            <div v-if="operationData.length > 0" class="operation-card-list">
              <div v-for="(item, index) in operationData" :key="index" class="operation-card">
                <div class="operation-header">
                  <div class="operation-title">客户分配</div>
                  <div class="operation-time">{{ item.changeTime }} · 操作人：{{ item.operatorName }}</div>
                </div>
                <div class="operation-content">
                  <div class="operation-meta">
                    <span class="meta-label">原栏目:</span>
                    <span>{{ item.originalColumnName || "无" }}</span>
                  </div>
                  <div class="operation-meta">
                    <span class="meta-label">原训练营:</span>
                    <span>{{ item.originalCompanyName || "无" }}</span>
                  </div>
                  <div class="operation-meta">
                    <span class="meta-label">员工:</span>
                    <span>{{ item.originalEmployeeName || "无" }} → {{ item.newEmployeeName }}</span>
                  </div>
                </div>
              </div>
            </div>
            <!-- 优化空状态显示条件 -->
            <van-empty v-else description="暂无操作记录数据" />

            <!-- 修改：加载提示调整 -->
            <div class="loading-tips">
              <van-loading v-if="operationLoading" size="24px">加载中...</van-loading>
              <div v-if="operationFinished && operationData.length > 0" class="finished">没有更多了</div>
            </div>
          </scroll-view>
        </div>
      </van-tab>
    </van-tabs>

    <!-- 新增栏目选择器弹窗 -->
    <van-popup :show="showColumnPicker" position="bottom" round>
      <div class="picker-header-container">
        <div class="picker-header">
          <div class="picker-title">选择栏目</div>
        </div>
      </div>
      <scroll-view scroll-y class="behavior-picker-content">
        <van-picker show-toolbar :columns="columnColumns" @cancel="showColumnPicker = false" @confirm="onColumnConfirm" />
      </scroll-view>
    </van-popup>

    <!-- +++ 新增：分配销售弹窗 +++ -->
    <van-popup :show="showAssignDialog" position="bottom" custom-style="height:80%;width:100%;">
      <div class="picker-header">
        <div class="picker-actions left">
          <van-button size="small" @click="showAssignDialog = false">取消</van-button>
        </div>
        <div class="picker-title">分配销售</div>
        <div class="picker-actions right">
          <van-button size="small" type="primary" @click="confirmAssign">确认</van-button>
        </div>
      </div>
      <div class="assign-content">
        <van-tree-select height="100%" :items="companyTreeData" :active-id="selectedGroupId" :main-active-index="activeColumnIndex" @click-nav="onClickNav" @click-item="onClickItem" />
        <div class="user-list">
          <div class="search-bar">
            <van-search v-model="userSearch" placeholder="输入用户名搜索" @search="searchUsers" />
          </div>
          <scroll-view scroll-y style="height: calc(100% - 100rpx)">
            <van-checkbox-group v-model="selectedUser">
              <van-cell-group>
                <van-cell v-for="user in userList" :key="user.id" clickable @click="toggleUserSelect(user)">
                  <van-checkbox :name="user.id" />
                  <div class="user-info">
                    <div class="username">{{ user.username }}</div>
                    <div class="phone">{{ user.phone }}</div>
                  </div>
                </van-cell>
              </van-cell-group>
            </van-checkbox-group>
            <van-empty v-if="userList.length === 0" description="暂无用户" />
          </scroll-view>
        </div>
      </div>
    </van-popup>

    <!-- +++ 新增：添加标签弹窗 +++ -->
    <van-dialog :show="showTagDialog" title="添加手动标签" show-cancel-button @confirm="saveTag" use-slot @cancel="showTagDialog = false">
      <!-- 单选：选择营期 -->
      <van-radio-group @change="tagCampPeriodChange" class="padding: 20rpx;">
        <van-cell v-for="camp in campPeriodInfos" clickable :key="camp.id">
          <van-radio :name="camp.id" slot="right-icon">{{ camp.campperiodName }}</van-radio>
        </van-cell>
      </van-radio-group>
      <van-cell-group>
        <van-field
          @change="
            (e) => {
              tagName = e.detail;
            }
          "
          label="标签"
          placeholder="请输入标签内容"
        />
      </van-cell-group>
    </van-dialog>
  </div>
</template>

<script>
import NavigationBar from "@/components/common/NavigationBar.vue";
import { BehaviorTypeEnum, BEHAVIOR_TYPE_OPTIONS } from "@/constants/enum/behavior-type.enum";
import { customerBehaviorPage } from "@/api/customer-behavior.api";
import { getCampPeriodInfoByCustomerId, getCourseRelationByCustomerId } from "@/api/customer-course-relation.api";
import { getCustomerAssignmentList } from "@/api/customer-assignment.api";
import { assignCustomer, listByCustomerId } from "@/api/customer-sales-relation.api";
import { saveManualTag } from "@/api/customer-tags.api";
import { queryHeadquartersCompanyList } from "@/api/company.api";
import { queryUserByPage } from "@/api/system-user.api";
import {getLayoutHeights} from "@/utils/get-page-height.util";

export default {
  components: {
    NavigationBar,
  },
  data() {
    return {
      customerInfo: {},
      // 行为轨迹相关数据
      behaviorData: [],
      behaviorSteps: [],
      behaviorPagination: {
        current: 1,
        pageSize: 5,
        total: 0,
      },
      behaviorTypes: [],
      selectedBehaviorTypes: [],
      showBehaviorTypePicker: false,
      behaviorTypeEnum: BehaviorTypeEnum,

      // 行为类型颜色映射
      COLOR_MAP: {
        [BehaviorTypeEnum.CAMP_ENROLLMENT.code]: "#FF7A00",
        [BehaviorTypeEnum.CAMP_VIDEO_COURSE_LEARNING.code]: "#00C979",
        [BehaviorTypeEnum.POST_COURSE_ANSWERING.code]: "#FF4D4F",
        [BehaviorTypeEnum.RECEIVE_RED_PACKET.code]: "#FF1F44",
        [BehaviorTypeEnum.ADD_WECHAT_ENTERPRISE.code]: "#00C979",
        [BehaviorTypeEnum.DELETE_WECHAT_ENTERPRISE.code]: "#FF4D4F",
        [BehaviorTypeEnum.JOIN_GROUP_CHAT.code]: "#E6E600",
        [BehaviorTypeEnum.EXIT_GROUP_CHAT.code]: "#666666",
      },
      topHeight:  88,
      contentHeight: 0,

      // 新增训练营相关数据
      campPeriodInfos: [],
      selectedCampId: "",
      courseData: [],
      courseDataLoading: false,
      courseColumns: [
        { title: "课程名称", key: "courseName", width: 150 },
        { title: "课程开始时间", key: "startTime", width: 150 },
        { title: "课程状态", key: "arrivalStatus", width: 100 },
        { title: "到课时间", key: "arrivalTime", width: 150 },
        { title: "完播时间", key: "completeTime", width: 150 },
        { title: "播放时长", key: "playProgress", width: 100 },
      ],
      coursePagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },

      // 新增操作记录相关数据
      operationData: [],
      operationPagination: {
        current: 1,
        pageSize: 5,
        total: 0,
      },

      // 栏目信息相关数据
      columnInfos: [],
      selectedColumnId: "",
      customerCourseRelations: [],

      // 新增栏目选择器状态
      showColumnPicker: false,

      // 新增营期选择器状态
      showCampPicker: false,

      // 新增滚动加载状态变量
      behaviorLoading: false,
      behaviorFinished: false,
      courseLoading: false,
      courseFinished: false,
      operationLoading: false,
      operationFinished: false,

      // 新增当前激活标签索引变量
      activeTabIndex: 0, // 初始化为第一个标签页

      behaviorScrollHeight: "0px", // 滚动区域高度

      // 新增滚动高度变量
      operationScrollHeight: "0px", // 操作记录滚动区域高度

      // 新增临时变量存储行为类型选择
      tempSelectedBehaviorTypes: [],
      allSelected: false, // 是否全选

      // +++ 新增分配销售相关数据 +++
      showAssignDialog: false,
      companyTreeData: [],
      selectedGroupId: "",
      activeColumnIndex: "",
      userList: [],
      userSearch: "",
      selectedUser: [],
      activeIcon: "arrow",
      inactiveIcon: "arrow-down",
      treeScrollHeight: "0px",
      selectedNode: null,

      // +++ 新增标签相关数据 +++
      showTagDialog: false,
      selectedCampPeriod: {},
      tagName: "",
      salesList: [], // 新增：销售员工列表
      selectedSales: null, // 新增：选中的销售员工
    };
  },
  mounted() {
    this.calculateScrollHeight();
    // 监听窗口变化重新计算高度
    uni.onWindowResize(() => {
      this.calculateScrollHeight();
    });
  },
  onShow() {
    const { topHeight, contentHeight } = getLayoutHeights();
    this.topHeight = topHeight;
    this.contentHeight = contentHeight;
  },
  computed: {
    // 行为类型选项
    behaviorTypeOptions() {
      return BEHAVIOR_TYPE_OPTIONS;
    },

    // 行为类型选择器列
    behaviorTypeColumns() {
      return this.behaviorTypeOptions.map((opt) => opt.label);
    },

    // 已选择的行为类型文本
    selectedBehaviorTypesText() {
      const totalCount = this.behaviorTypeOptions.length;
      // 当选择所有类型或未选择时显示"全部行为类型"
      if (this.selectedBehaviorTypes.length === 0 || this.selectedBehaviorTypes.length === totalCount) {
        return "全部行为类型";
      }
      // 过滤无效标签避免多余逗号
      const validLabels = this.selectedBehaviorTypes.map((type) => this.behaviorTypeOptions.find((opt) => opt.value === type)?.label).filter((label) => label != null);
      return validLabels.join(", ");
    },

    // 新增：获取选中栏目名称
    selectedColumnName() {
      if (!this.selectedColumnId || this.columnInfos.length === 0) return "";
      const column = this.columnInfos.find((item) => item.id === this.selectedColumnId);
      return column ? column.columnName : "";
    },

    // 新增：栏目选择器选项
    columnColumns() {
      return this.columnInfos.map((item) => item.columnName);
    },

    // 新增：获取选中营期名称
    selectedCampName() {
      if (!this.selectedCampId || this.campPeriodInfos.length === 0) return "";
      const camp = this.campPeriodInfos.find((item) => item.id === this.selectedCampId);
      return camp ? camp.campperiodName : "";
    },

    // 新增：营期选择器选项
    campColumns() {
      return this.campPeriodInfos.map((item) => item.campperiodName);
    },
  },
  methods: {
    // 新增：切换行为类型选择状态
    toggleBehaviorType(type) {
      const index = this.tempSelectedBehaviorTypes.indexOf(type);
      if (index > -1) {
        // 如果已选中则移除
        this.tempSelectedBehaviorTypes.splice(index, 1);
      } else {
        // 如果未选中则添加
        this.tempSelectedBehaviorTypes.push(type);
      }
      // 更新全选状态
      this.allSelected = this.tempSelectedBehaviorTypes.length === this.behaviorTypeOptions.length;
    },

    // 标签页切换
    onChange(event) {
      this.activeTabIndex = event.detail.index;
    },
    openBehaviorTypePicker() {
      // 新增：检查是否已选择栏目
      if (!this.selectedColumnId || this.columnInfos.length === 0) {
        uni.showToast({
          title: "请先选择栏目",
          icon: "none",
          duration: 2000,
        });
        return;
      }

      this.showBehaviorTypePicker = true;
      // 初始化临时变量
      this.tempSelectedBehaviorTypes = [...this.selectedBehaviorTypes];
      this.allSelected = this.tempSelectedBehaviorTypes.length === this.behaviorTypeOptions.length;
    },

    // 行为类型多选框变化
    onBehaviorTypeChange(event) {
      this.tempSelectedBehaviorTypes = event.detail;
      this.allSelected = this.tempSelectedBehaviorTypes.length === this.behaviorTypeOptions.length;
    },

    // 切换全选
    toggleSelectAll() {
      if (this.allSelected) {
        this.tempSelectedBehaviorTypes = [];
      } else {
        this.tempSelectedBehaviorTypes = this.behaviorTypeOptions.map((opt) => opt.value);
      }
      this.allSelected = !this.allSelected;
    },

    // 行为类型选择确认
    onBehaviorTypeConfirm() {
      this.selectedBehaviorTypes = [...this.tempSelectedBehaviorTypes];
      this.showBehaviorTypePicker = false;
      this.queryCustomerBehavior();
    },

    // 获取行为类型名称
    getBehaviorTypeName(type) {
      const behaviorType = Object.values(BehaviorTypeEnum).find((item) => item.code === type);
      return behaviorType?.name || "";
    },

    // 获取行为类型颜色
    getDotColor(type) {
      return this.COLOR_MAP[type] || "default";
    },

    // 修改：行为轨迹查询方法（支持滚动加载）
    async queryCustomerBehavior(loadMore = false) {
      if (!this.customerId || this.behaviorLoading) return;

      // 新增：检查是否已加载完成
      if (loadMore && this.behaviorFinished) return;

      if (!loadMore) {
        this.behaviorPagination.current = 1;
        this.behaviorFinished = false;
        this.behaviorSteps = [];
      }

      this.behaviorLoading = true;
      try {
        const query = {
          pageNum: this.behaviorPagination.current,
          pageSize: this.behaviorPagination.pageSize,
        };

        const formData = {
          customerId: this.customerId,
          behaviorTypes: this.selectedBehaviorTypes,
          columnId: this.selectedColumnId,
        };
        const res = await customerBehaviorPage(formData, query);
        if (res.code === 0) {
          const newSteps = (res.data.records || []).map((item) => ({
            text: this.getBehaviorTypeName(item.behaviorType),
            desc: this.getBehaviorDesc(item),
            inactiveIcon: "checked",
            activeIcon: "success",
          }));

          if (loadMore) {
            this.behaviorSteps = [...this.behaviorSteps, ...newSteps];
          } else {
            this.behaviorSteps = newSteps;
          }

          this.behaviorPagination.total = res.data.total || 0;

          // 更新加载完成状态
          this.behaviorFinished = res.data.records.length < this.behaviorPagination.pageSize;

          // 新增：成功加载后递增页码
          if (!loadMore || res.data.records.length > 0) {
            this.behaviorPagination.current += 1;
          }
        } else {
          // 修改错误提示为微信小程序兼容方式
          uni.showToast({
            title: res.msg || "请求失败",
            icon: "none",
            duration: 2000,
          });
        }
      } catch (error) {
        console.error("查询行为轨迹失败", error);
        // 修改错误提示为微信小程序兼容方式
        uni.showToast({
          title: "查询行为轨迹失败",
          icon: "none",
          duration: 2000,
        });
      } finally {
        this.behaviorLoading = false;
      }
    },

    // 格式化日期
    formatDate(timestamp) {
      if (!timestamp) return "";
      const date = new Date(timestamp);
      return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, "0")}-${date.getDate().toString().padStart(2, "0")}`;
    },

    // 新增：将秒转为时分秒
    secondsToTime(seconds) {
      if (!seconds) return "";
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const remainingSeconds = seconds % 60;
      let result = "";
      if (hours > 0) result += `${hours}小时`;
      if (minutes > 0) result += `${minutes}分`;
      if (remainingSeconds > 0 || result === "") result += `${remainingSeconds}秒`;
      return result;
    },

    // 新增：选择营期
    selectCamp(item) {
      this.selectedCampId = item.id;
      this.queryCourseRelation();
    },

    // 新增：查询客户参加的营期
    async queryEnrolledCamps() {
      if (!this.customerId) return;

      try {
        const params = {
          customerId: this.customerId,
          columnId: this.selectedColumnId,
        };

        const res = await getCampPeriodInfoByCustomerId(params);

        console.log("查询客户营期结果:", res);
        if (res.code === 0) {
          this.campPeriodInfos = res.data || [];
          if (this.campPeriodInfos.length > 0) {
            this.selectedCampId = this.campPeriodInfos[0].id;
            await this.queryCourseRelation();
          }
        } else {
          uni.showToast({
            title: res.msg || "查询客户营期失败",
            icon: "none",
            duration: 2000,
          });
        }
      } catch (error) {
        console.error("查询客户营期异常:", error);
        uni.showToast({
          title: "查询客户营期失败",
          icon: "none",
          duration: 2000,
        });
      }
    },

    // 优化：课程查询方法（支持滚动加载）
    async queryCourseRelation(loadMore = false) {
      if (!this.customerId || !this.selectedCampId || this.courseLoading) return;

      // 新增：检查是否已加载完成
      if (loadMore && this.courseFinished) return;

      if (!loadMore) {
        this.coursePagination.current = 1;
        this.courseFinished = false;
        this.courseData = [];
      }

      this.courseLoading = true;
      try {
        const params = {
          customerId: this.customerId,
          campPeriodId: this.selectedCampId,
          pageNum: this.coursePagination.current,
          pageSize: this.coursePagination.pageSize,
        };

        const res = await getCourseRelationByCustomerId(params);

        if (res.code === 0) {
          if (loadMore) {
            this.courseData = [...this.courseData, ...(res.data.records || [])];
          } else {
            this.courseData = res.data.records || [];
          }

          this.coursePagination.total = res.data.total || 0;

          // 更新加载完成状态
          this.courseFinished = res.data.records.length < this.coursePagination.pageSize;

          // 新增：成功加载后递增页码
          if (!loadMore || res.data.records.length > 0) {
            this.coursePagination.current += 1;
          }
        } else {
          uni.showToast({
            title: res.msg || "查询课程信息失败",
            icon: "none",
            duration: 2000,
          });
        }
      } catch (error) {
        console.error("查询课程信息异常:", error);
        uni.showToast({
          title: "查询课程信息失败",
          icon: "none",
          duration: 2000,
        });
      } finally {
        this.courseLoading = false;
      }
    },

    // 优化：操作记录查询方法（支持滚动加载）
    async queryOperation(loadMore = false) {
      if (!this.customerId || this.operationLoading) return;

      // 新增：检查是否已加载完成
      if (loadMore && this.operationFinished) return;

      if (!loadMore) {
        this.operationPagination.current = 1;
        this.operationFinished = false;
        this.operationData = [];
      }

      this.operationLoading = true;
      try {
        const params = {
          customerId: this.customerId,
          pageNum: this.operationPagination.current,
          pageSize: this.operationPagination.pageSize,
        };

        const res = await getCustomerAssignmentList(params);
        console.log("查询操作记录成功:", res);
        if (res.code === 0) {
          if (loadMore) {
            this.operationData = [...this.operationData, ...(res.data.records || [])];
          } else {
            this.operationData = res.data.records || [];
          }

          this.operationPagination.total = res.data.total || 0;

          // 更新加载完成状态
          this.operationFinished = res.data.records.length < this.operationPagination.pageSize;

          // 新增：成功加载后递增页码
          if (!loadMore || res.data.records.length > 0) {
            this.operationPagination.current += 1;
          }
        } else {
          uni.showToast({
            title: res.msg || "查询操作记录失败",
            icon: "none",
            duration: 2000,
          });
        }
      } catch (error) {
        console.error("查询操作记录异常:", error);
        uni.showToast({
          title: "查询操作记录失败",
          icon: "none",
          duration: 2000,
        });
      } finally {
        this.operationLoading = false;
      }
    },

    // 修改：栏目变更处理（完善状态重置）
    columnChange() {
      console.log("栏目变更处理");
      // 重置操作记录分页到第一页
      this.operationPagination.current = 1;
      // 重置课程分页到第一页
      this.coursePagination.current = 1;
      // 重置行为轨迹分页到第一页
      this.behaviorPagination.current = 1;

      // 重置选中的训练营ID
      this.selectedCampId = "";
      // 清空当前数据
      this.courseData = [];
      this.operationData = [];
      this.behaviorSteps = [];

      // 重置加载完成状态
      this.behaviorFinished = false;
      this.courseFinished = false;
      this.operationFinished = false; // 新增：确保重置操作记录加载状态

      // 重新查询第一页数据
      this.queryCustomerBehavior();
      this.queryEnrolledCamps(); // 只调用营期查询
      this.queryOperation();
    },

    // 新增：获取栏目信息
    async getColumnInfo() {
      if (!this.customerId) return;

      console.log("获取栏目信息请求参数:", this.customerId);
      try {
        const res = await listByCustomerId({ customerId: this.customerId });
        console.log("获取栏目信息成功:", res);
        if (res.code === 0) {
          this.columnInfos = res.data || [];
          if (this.columnInfos.length > 0) {
            this.selectedColumnId = this.columnInfos[0].id;
            this.columnChange();
          }
        } else {
          console.warn("获取栏目信息失败:", res.msg);
          uni.showToast({
            title: res.msg || "获取栏目信息失败",
            icon: "none",
            duration: 2000,
          });
        }
      } catch (error) {
        console.error("获取栏目信息失败:", error);
        uni.showToast({
          title: "获取栏目信息失败",
          icon: "none",
          duration: 2000,
        });
      }
    },

    // 新增：打开栏目选择器
    openColumnPicker() {
      if (this.columnInfos.length === 0) {
        uni.showToast({
          title: "暂无栏目数据",
          icon: "none",
          duration: 2000,
        });
        return;
      }
      this.showColumnPicker = true;
    },

    // 新增：栏目选择确认 - 直接从事件参数获取值
    onColumnConfirm(value) {
      // 从事件参数获取选中的栏目名称
      const columnName = value?.detail?.value;
      if (columnName) {
        const column = this.columnInfos.find((item) => item.columnName === columnName);
        if (column) {
          this.selectedColumnId = column.id;
          this.columnChange(); // 调用已有的columnChange方法
        }
      }
      this.showColumnPicker = false;
    },

    // 获取行为描述 - 样式优化后的拼接逻辑
    getBehaviorDesc(item) {
      const timeStr = this.formatBehaviorTime(item.createdAt);
      // 根据不同类型拼接详细信息
      switch (item.behaviorType) {
        case this.behaviorTypeEnum.CAMP_ENROLLMENT.code:
          return `${timeStr} · 报名训练营`;
        case this.behaviorTypeEnum.CAMP_VIDEO_COURSE_LEARNING.code:
          return `${timeStr} · 学习视频课`;
        case this.behaviorTypeEnum.POST_COURSE_ANSWERING.code:
          return `${timeStr} · 答题`;
        case this.behaviorTypeEnum.RECEIVE_RED_PACKET.code:
          return `${timeStr} · 领取红包`;
        case this.behaviorTypeEnum.ADD_WECHAT_ENTERPRISE.code:
          return `${timeStr} · 添加企业微信`;
        case this.behaviorTypeEnum.DELETE_WECHAT_ENTERPRISE.code:
          return `${timeStr} · 删除企业微信`;
        case this.behaviorTypeEnum.JOIN_GROUP_CHAT.code:
          return `${timeStr} · 加入群聊`;
        case this.behaviorTypeEnum.EXIT_GROUP_CHAT.code:
          return `${timeStr} · 退出群聊`;
        default:
          return timeStr;
      }
    },

    // 新增：获取行为图标
    getBehaviorIcon(type) {
      const iconMap = {
        [BehaviorTypeEnum.CAMP_ENROLLMENT.code]: "flag-o",
        [BehaviorTypeEnum.CAMP_VIDEO_COURSE_LEARNING.code]: "video-o",
        [BehaviorTypeEnum.POST_COURSE_ANSWERING.code]: "comment-o",
        [BehaviorTypeEnum.RECEIVE_RED_PACKET.code]: "gift-o",
        [BehaviorTypeEnum.ADD_WECHAT_ENTERPRISE.code]: "friends-o",
        [BehaviorTypeEnum.DELETE_WECHAT_ENTERPRISE.code]: "clear",
        [BehaviorTypeEnum.JOIN_GROUP_CHAT.code]: "cluster-o",
        [BehaviorTypeEnum.EXIT_GROUP_CHAT.code]: "close",
      };
      return iconMap[type] || "circle";
    },

    // 新增：格式化行为时间
    formatBehaviorTime(timestamp) {
      if (!timestamp) return "";
      const date = new Date(timestamp);
      return `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours().toString().padStart(2, "0")}:${date.getMinutes().toString().padStart(2, "0")}`;
    },

    // 新增：打开营期选择器
    openCampPicker() {
      if (this.campPeriodInfos.length === 0) {
        uni.showToast({
          title: "暂无营期数据",
          icon: "none",
          duration: 2000,
        });
        return;
      }
      this.showCampPicker = true;
    },

    // 新增：营期选择确认
    onCampConfirm(value) {
      const camp = this.campPeriodInfos.find((item) => item.campperiodName === value.detail.value);
      if (camp) {
        this.selectedCampId = camp.id;
        this.queryCourseRelation();
      }
      this.showCampPicker = false;
    },

    // 计算滚动区域高度（扩展支持操作记录）
    calculateScrollHeight() {
      // 行为轨迹高度计算
      const behaviorQuery = uni.createSelectorQuery().in(this);
      behaviorQuery
        .select(".behavior-container")
        .boundingClientRect((data) => {
          if (data) {
            const safeArea = uni.getSystemInfoSync().safeAreaInsets?.bottom || 0;
            this.behaviorScrollHeight = `calc(100vh - ${data.top}px - 100rpx - ${safeArea}px)`;
          }
        })
        .exec();

      // 新增：操作记录高度计算
      const operationQuery = uni.createSelectorQuery().in(this);
      operationQuery
        .select(".operation-container")
        .boundingClientRect((data) => {
          if (data) {
            const safeArea = uni.getSystemInfoSync().safeAreaInsets?.bottom || 0;
            this.operationScrollHeight = `calc(100vh - ${data.top}px - 100rpx - ${safeArea}px)`;
          }
        })
        .exec();
    },

    // 行为轨迹滚动到底部事件
    onBehaviorScrollToLower() {
      if (!this.behaviorLoading && !this.behaviorFinished) {
        this.queryCustomerBehavior(true);
      }
    },

    // 新增：操作记录滚动到底部事件
    onOperationScrollToLower() {
      if (!this.operationLoading && !this.operationFinished) {
        this.queryOperation(true);
      }
    },

    // +++ 新增：打开分配销售弹窗 +++
    openAssignDialog() {
      if (!this.selectedColumnId) {
        uni.showToast({ title: "请先选择栏目", icon: "none" });
        return;
      }
      this.userList = [];
      this.showAssignDialog = true;
      this.$nextTick(() => {
        this.fetchOrganizationTree();
      });
    },

    // +++ 新增：获取组织架构树 +++
    async fetchOrganizationTree() {
      try {
        const res = await queryHeadquartersCompanyList({ id: 31000, level: 4 });
        if (res.code === 0) {
          this.companyTreeData = this.transformTreeData(res.data);
        }
        console.log("组织架构树数据", res);
      } catch (e) {
        console.error("获取组织架构失败", e);
        uni.showToast({ title: "获取组织架构失败", icon: "none" });
      }
    },

    // +++ 修改：转换树形数据结构 +++
    transformTreeData(data) {
      // 过滤出当前所选栏目
      const currentColumn = data.columns.find((column) => column.id == this.selectedColumnId);

      // 如果找到当前栏目，则直接返回其子节点(companies)
      if (currentColumn) {
        return currentColumn.companies.map((company) => ({
          id: company.id,
          text: company.name,
          children: company.salesGroups.map((group) => ({
            id: group.id,
            text: group.name,
          })),
        }));
      }
      // 未找到栏目时返回空数组
      return [];
    },

    // +++ 修改：树节点点击事件 +++
    onClickNav({ detail = {} }) {
      this.activeColumnIndex = detail.index || 0;
      const companyTreeDatum = this.companyTreeData[detail.index];
      this.fetchUsers("", companyTreeDatum.id);
    },

    // +++ 修改：子项点击事件 +++
    onClickItem({ detail = {} }) {
      console.log("点击的子项", detail);
      this.selectedGroupId = detail.id;
      this.fetchUsers(detail.id);
    },

    // +++ 新增：获取用户列表 +++
    async fetchUsers(groupId, companyId) {
      try {
        this.userList = [];
        const params = {
          current: 1,
          size: 100,
          companyId: companyId || "",
          salesGroupId: groupId,
          username: this.userSearch,
          status: 1,
          auditStatus: 2,
        };
        const res = await queryUserByPage(params);

        console.log("用户列表数据", res);
        if (res.code === 0) {
          this.userList = res.data.records || [];
        }
      } catch (e) {
        console.error("获取用户列表失败", e);
        uni.showToast({ title: "获取用户列表失败", icon: "none" });
      }
    },

    // +++ 新增：搜索用户 +++
    searchUsers() {
      if (this.selectedNode) {
        this.fetchUsers(this.selectedNode.id);
      }
    },

    // +++ 新增：切换用户选择 +++
    toggleUserSelect(user) {
      this.selectedUser = [user.id];
    },

    // +++ 新增：确认分配 +++
    async confirmAssign() {
      if (this.selectedUser.length === 0) {
        uni.showToast({ title: "请选择销售", icon: "none" });
        return;
      }

      const selectedUser = this.userList.find((u) => u.id === this.selectedUser[0]);
      const params = {
        oldColumnId: this.selectedColumnId,
        customerIds: [this.customerId],
        columnId: this.selectedColumnId,
        companyId: selectedUser.companyId,
        salesGroupId: selectedUser.salesGroupId,
        salesId: selectedUser.id,
        salesName: selectedUser.username,
      };

      try {
        const res = await assignCustomer(params);
        if (res.code === 0) {
          uni.showToast({ title: "分配成功" });
          this.showAssignDialog = false;
          // 刷新客户信息
          await this.getColumnInfo();
        } else {
          uni.showToast({
            title: res.msg || "分配失败",
            icon: "none",
            duration: 2000,
          });
        }
      } catch (error) {
        console.error("分配销售失败:", error);
        uni.showToast({
          title: "分配失败",
          icon: "none",
          duration: 2000,
        });
      }
    },

    // 新增：打开添加标签弹窗
    openTagDialog() {
      console.log("打开标签弹窗");
      if (!this.selectedColumnId) {
        uni.showToast({
          title: "请先选择栏目",
          icon: "none",
          duration: 2000,
        });
        return;
      }

      if (this.campPeriodInfos.length === 0) {
        uni.showToast({
          title: "该栏目下没有营期",
          icon: "none",
          duration: 2000,
        });
        return;
      }
      console.log("this.tagName", this.tagName);
      this.showTagDialog = true;
    },

    tagCampPeriodChange(event) {
      this.selectedCampPeriod.id = event.detail;
      this.selectedCampPeriod.campperiodName = this.campPeriodInfos.find((item) => item.id === event.detail).campperiodName;
    },
    // +++ 新增：保存标签 +++
    async saveTag() {
      if (!this.selectedCampPeriod.id) {
        uni.showToast({ title: "请选择营期", icon: "none" });
        this.showTagDialog = false;
        return;
      }
      console.log("保存标签", this.tagName);
      if (!this.tagName.trim()) {
        uni.showToast({ title: "请输入标签内容", icon: "none" });
        this.showTagDialog = false;
        return;
      }

      const params = {
        customerIds: [this.customerId],
        manualTagsName: this.tagName.trim(),
        campPeriodId: this.selectedCampPeriod.id,
        campPeriodName: this.selectedCampPeriod.campperiodName,
      };

      try {
        const res = await saveManualTag(params);
        if (res.code === 0) {
          uni.showToast({ title: "标签添加成功" });
          this.showTagDialog = false;
          this.tagName = "";
        } else {
          uni.showToast({
            title: res.msg || "添加标签失败",
            icon: "none",
            duration: 2000,
          });
        }
      } catch (error) {
        console.error("添加标签失败:", error);
        uni.showToast({
          title: "添加标签失败",
          icon: "none",
          duration: 2000,
        });
      }
    },
  },
  onLoad(query) {
    console.log("query===", query);
    this.customerInfo = query;
    this.customerId = this.customerInfo.customerId;
    // 新增：获取栏目信息
    this.getColumnInfo();
  },
  // 触底加载更多
  onReachBottom() {
    if (this.loading || this.finished) return;

    console.log("onReachBottom===========", this.activeTabIndex);

    // 根据当前激活的标签页调用对应加载方法
    switch (this.activeTabIndex) {
      case 0: // 行为轨迹
        this.queryCustomerBehavior(true);
        break;
      case 1: // 训练营
        this.queryCourseRelation(true);
        break;
      case 2: // 操作记录
        this.queryOperation(true);
        break;
    }
  },
};
</script>

<style lang="scss" scoped>
.customer-detail-page {
  padding-bottom: 20rpx;
  /* 新增：与首页一致的渐变背景 */
  background: linear-gradient(180deg, #f0f7ff 0%, #ffffff 100%);
  ::v-deep .van-cell {
    border-radius: 0 !important;
    background-color: rgba(255, 255, 255, 0.95) !important;
  }
}

.customer-card {
  background: #fff;
  border-radius: 24rpx; /* 增大圆角与首页一致 */
  margin: 20rpx;
  padding: 30rpx;
  /* 新增：与首页一致的阴影效果 */
  box-shadow: 0 6rpx 24rpx rgba(64, 158, 255, 0.15);

  .customer-header {
    display: flex;
    align-items: flex-start; // 顶部对齐
    margin-bottom: 30rpx;
    position: relative; // 为按钮定位提供基准

    .customer-info {
      margin-left: 24rpx;
      flex: 1; // 占据剩余空间
      min-width: 0; // 防止昵称过长溢出

      .customer-name {
        font-size: 36rpx;
        font-weight: bold;
        color: #1a1a1a;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 100%; // 防止昵称过长溢出
      }
    }

    /* 修改：垂直排列的按钮容器 - 固定在右侧 */
    .vertical-card-actions {
      display: flex;
      flex-direction: column; // 垂直排列
      gap: 20rpx; // 按钮间距
      margin-left: 20rpx; // 与客户信息保持间距
      flex-shrink: 0; // 防止被挤压

      ::v-deep .van-button {
        height: 60rpx;
        line-height: 60rpx;
        font-size: 26rpx;
        padding: 0 20rpx;
        border-radius: 12rpx;
        min-width: 120rpx; // 确保按钮最小宽度
      }
      ::v-deep .van-button--primary {
        background: #409eff;
        border-color: #409eff;
      }
    }
  }

  .customer-meta {
    .meta-item {
      display: flex;
      justify-content: space-between;
      padding: 18rpx 0;
      border-bottom: 1rpx solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .meta-label {
        font-size: 28rpx;
        color: #666;
      }

      .meta-value {
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
      }
    }
  }
}

/* 新增：统一标题样式 */
.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #1a1a1a;
  margin-bottom: 32rpx;
  position: relative;
  padding-left: 20rpx;

  &::before {
    content: "";
    position: absolute;
    left: 0;
    top: 8rpx;
    bottom: 8rpx;
    width: 6rpx;
    background: #409eff;
    border-radius: 4rpx;
  }
}

/* 新增：统一状态标签样式 */
.status-tag {
  display: inline-block;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  font-size: 24rpx;

  &.danger {
    background-color: #fef0f0;
    color: #f56c6c;
  }

  &.warning {
    background-color: #fdf6ec;
    color: #e6a23c;
  }

  &.success {
    background-color: #f0f9eb;
    color: #67c23a;
  }
}

/* 新增：统一弹窗标题颜色 */
.picker-title {
  text-align: center;
  flex: 1;
  color: #409eff; /* 统一标题颜色 */
  font-weight: bold;
}

/* 调整按钮间距 */
.picker-actions {
  gap: 20rpx; /* 增加按钮间距 */
}

/* 新增：统一选择器样式 */
.column-selector,
.camp-selector {
  margin: 20rpx;
  background: #fff;
  border-radius: 24rpx; /* 增大圆角 */
  padding: 20rpx;
  /* 新增：与首页一致的阴影效果 */
  box-shadow: 0 6rpx 24rpx rgba(64, 158, 255, 0.15);
}

/* 新增：统一卡片样式 */
.course-card,
.operation-card {
  background: #fff;
  border-radius: 24rpx; /* 增大圆角 */
  padding: 30rpx;
  /* 新增：与首页一致的阴影效果 */
  box-shadow: 0 6rpx 24rpx rgba(64, 158, 255, 0.15);
  margin-bottom: 20rpx;
}

.course-data {
  flex: 1;
  padding: 20rpx;
  background: #fff;
  border-top: 1rpx solid #f0f0f0;
}

/* 新增卡片样式 */
.course-card-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  padding: 20rpx;
}

.course-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
  }
}

.course-info {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.course-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #1a1a1a;
  margin-bottom: 10rpx;
}

.course-meta {
  display: flex;
  font-size: 28rpx;
  color: #333;

  .meta-label {
    color: #666;
    width: 140rpx;
  }
}

.course-status {
  display: flex;
  align-items: center;

  .meta-label {
    color: #666;
    width: 140rpx;
  }
}

.loading-container {
  display: flex;
  justify-content: center;
  padding: 60rpx 0;
}

.empty-data {
  padding: 40rpx 0;
}

/* 新增操作记录样式 */
.operation-container {
  padding: 20rpx;
}

/* 新增操作记录卡片样式 - 与课程卡片保持一致 */
.operation-card-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  padding: 20rpx;
}

/* 优化操作记录卡片样式 */
.operation-card {
  background: #fff;
  border-radius: 20rpx;
  padding: 20rpx; // 减少内边距使更紧凑
  margin-bottom: 20rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  }

  .operation-header {
    padding-bottom: 10rpx;
    margin-bottom: 10rpx;

    .operation-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #1a1a1a;
      margin-bottom: 5rpx; // 减少间距
    }

    .operation-time {
      color: #999;
      font-size: 24rpx; // 减小字体大小
    }
  }

  .operation-content {
    .operation-meta {
      display: flex;
      font-size: 28rpx;
      margin-bottom: 10rpx; // 减少间距

      .meta-label {
        color: #666;
        width: auto; // 移除固定宽度
        flex-shrink: 0;
        margin-right: 10rpx;
      }

      span:last-child {
        color: #333;
        font-weight: 500; // 加粗值文本
      }
    }
  }
}

// 添加滚动容器样式
.operation-scroll-container {
  flex: 1;
  min-height: 300rpx; // 确保最小高度
  padding: 0 20rpx;
  -webkit-overflow-scrolling: touch; // iOS滚动优化
}

/* 新增栏目选择器样式 */
.column-selector {
  margin: 20rpx;
  background: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

// 新增营期选择器样式
.camp-selector {
  margin: 20rpx;
  background: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

// 新增营期选择器弹窗样式
.camp-selector-popup {
  .van-picker__toolbar {
    background-color: #fff;
    border-bottom: 1px solid #ebedf0;

    .van-picker__cancel,
    .van-picker__confirm {
      color: #333;
      font-size: 32rpx;
      padding: 0 40rpx;
    }

    .van-picker__title {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
    }
  }

  .van-picker__columns {
    padding: 20rpx 0;

    .van-picker-column__item {
      font-size: 32rpx;
      color: #333;
      line-height: 80rpx;
      text-align: center;
    }

    .van-picker-column__item--selected {
      color: #07c160;
    }
  }
}

// 新增步骤样式
::v-deep .van-step {
  &--vertical {
    padding-left: 40rpx;

    &::after {
      left: 18rpx;
      border-color: #ebedf0;
    }
  }

  &__icon {
    width: 24rpx;
    height: 24rpx;
    background: white;

    .van-icon {
      display: block;
    }
  }

  &__title {
    font-size: 28rpx;
    font-weight: bold;
  }
}

.step-header {
  display: flex;
  flex-direction: column;
  margin-bottom: 16rpx;
}

.step-title {
  font-weight: bold;
  font-size: 28rpx;
  margin-bottom: 8rpx;
}

.step-time {
  color: #969799;
  font-size: 24rpx;
}

// 新增行为轨迹描述样式
.behavior-desc {
  padding: 10rpx 0;
  font-size: 26rpx;
  line-height: 1.6;

  .behavior-time {
    color: #969799;
    font-size: 24rpx;
    margin-bottom: 8rpx;
  }

  .behavior-title {
    font-weight: bold;
    color: #1a1a1a;
    margin-bottom: 8rpx;
  }

  .behavior-divider {
    height: 1rpx;
    background: #f0f0f0;
    margin: 10rpx 0;
  }

  .behavior-detail-item {
    margin-bottom: 6rpx;

    &.amount {
      color: #ff4d4f;
      font-weight: 500;
    }
  }
}

// 添加加载提示样式
.loading-tips {
  text-align: center;
  padding: 20rpx 0;

  .finished {
    color: #999;
    font-size: 24rpx;
    padding: 20rpx 0;
  }
}

.behavior-scroll-container {
  flex: 1;
  min-height: 300rpx; // 确保最小高度
  -webkit-overflow-scrolling: touch; // iOS滚动优化
}

/* 行为类型选择器样式 */
.picker-header-container {
  position: sticky;
  top: 0;
  z-index: 100;
  background: white;
  border-bottom: 1rpx solid #eee;
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: #fff;

  .picker-title {
    font-size: 32rpx;
    font-weight: bold;
  }

  .picker-actions {
    display: flex;
    gap: 10rpx;

    &.left {
      justify-content: flex-start;
    }

    &.right {
      justify-content: flex-end;
    }
    ::v-deep .van-button {
      height: 60rpx;
      font-size: 28rpx;
    }
    ::v-deep .van-button--primary {
      background: #409eff;
      border-color: #409eff;
    }
  }
}

.behavior-picker-content {
  max-height: 60vh;
  overflow-y: auto;

  ::v-deep .van-cell {
    padding: 20rpx 30rpx;
  }
}

/* 新增标签弹窗样式 */
.tag-dialog {
  padding: 30rpx;
  background: #fff;
  border-radius: 24rpx;

  &-header {
    text-align: center;
    margin-bottom: 30rpx;
  }

  &-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #1a1a1a;
  }

  &-subtitle {
    font-size: 28rpx;
    color: #666;
    margin-top: 10rpx;
  }

  &-body {
    margin: 40rpx 0;
  }

  &-footer {
    display: flex;
    justify-content: space-between;
    gap: 30rpx;

    .van-button {
      flex: 1;
      border-radius: 12rpx;
    }
  }
}

/* 分配销售弹窗样式优化 */
.behavior-picker-content {
  max-height: 60vh;

  .van-cell {
    padding: 30rpx;
  }

  .van-radio {
    display: flex;
    justify-content: flex-end;
  }
}

/* +++ 新增：分配销售弹窗样式 +++*/
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1rpx solid #eee;

  .dialog-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #409eff;
  }

  .dialog-actions {
    display: flex;
    gap: 20rpx;

    .van-button {
      height: 60rpx;
      line-height: 60rpx;
      padding: 0 20rpx;

      &--primary {
        background: #409eff;
        border-color: #409eff;
      }
    }
  }
}

.assign-content {
  display: flex;
  height: calc(100% - 180rpx);

  .organization-tree {
    width: 40%;
    border-right: 1rpx solid #eee;

    ::v-deep .van-tree {
      padding: 20rpx;

      .van-tree__leaf-node {
        padding-left: 20rpx;
      }
    }
  }

  .user-list {
    width: 60%;

    .search-bar {
      height: 100rpx;
      background: #fff;
    }

    .user-info {
      margin-left: 20rpx;

      .username {
        font-size: 32rpx;
      }

      .phone {
        font-size: 26rpx;
        color: #999;
      }
    }
  }
}
::v-deep .van-sidebar {
  width: 180rpx !important;
}
::v-deep .van-tree-select__content {
  width: 240rpx !important;
}
</style>
