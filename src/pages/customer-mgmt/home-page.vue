<template>
  <div :style="{ height: contentHeight  + 'px', paddingTop: topHeight + 'px' }">
    <navigation-bar show-icon backLevel showText text="客户管理首页" />
    <div class="home-page">
      <!-- 筛选弹窗 -->
      <van-popup :show="showFilterPopup" position="bottom" round :z-index="999" lock-scroll root-portal :overlay="true" teleport="body" closeable @close="showFilterPopup = false">
        <div class="filter-header">
          <div class="filter-title">客户筛选</div>
        </div>
        <div class="filter-content">
          <van-cell-group>
            <van-field :value="filterParams.nickname" label="微信昵称" placeholder="请输入昵称" />
            <van-field :value="filterParams.wechatRemark" label="企微备注" placeholder="请输入企微备注" />
            <van-field :value="filterParams.mobile" label="客户手机号" placeholder="请输入手机号" />
            <van-field :value="filterParams.customerId" label="客户ID" placeholder="请输入客户ID" />
            <van-field :value="filterParams.unionId" label="UnionId" placeholder="请输入UnionId" />
            <van-field readonly clickable is-link label="创建开始时间" :value="formatDate(filterParams.createStartTime)" placeholder="请选择开始时间" @click-input="openTimePicker('createTime', 'start')" />
            <van-field readonly clickable is-link label="创建结束时间" :value="formatDate(filterParams.createEndTime)" placeholder="请选择结束时间" @click-input="openTimePicker('createTime', 'end')" />
            <van-field readonly clickable is-link label="活跃开始时间" :value="formatDate(filterParams.activeStartTime)" placeholder="请选择开始时间" @click-input="openTimePicker('activeTime', 'start')" />
            <van-field readonly clickable is-link label="活跃结束时间" :value="formatDate(filterParams.activeEndTime)" placeholder="请选择结束时间" @click-input="openTimePicker('activeTime', 'end')" />
            <!-- 修改字段为可点击选择 -->
            <van-field readonly clickable is-link label="栏目" placeholder="请选择栏目" :value="filterParams.columnName" @click-input="openFieldPicker('column')" />
            <van-field readonly clickable is-link label="训练营" placeholder="请选择训练营" :value="filterParams.companyName" @click-input="openFieldPicker('company')" />
            <van-field readonly clickable is-link label="销售组" placeholder="请选择销售组" :value="filterParams.salesGroupName" @click-input="openFieldPicker('salesGroup')" />
            <van-field readonly clickable is-link label="销售" placeholder="请选择销售" :value="filterParams.salesName" @click-input="openFieldPicker('sales')" />
            <van-field readonly clickable is-link label="营期" placeholder="请选择营期" :value="filterParams.campPeriodName" @click-input="openFieldPicker('campPeriod')" />
            <van-field readonly clickable is-link label="企微添加状态" placeholder="请选择企微添加状态" :value="filterParams.weworkStatus" @click-input="openFieldPicker('weworkStatus')" />
            <van-field readonly clickable is-link label="账号状态" placeholder="请选择账号状态" :value="filterParams.forbiddenStatusName" @click-input="openFieldPicker('forbiddenStatus')" />
            <van-field readonly clickable is-link label="红包状态" placeholder="请选择红包状态" :value="filterParams.redPacketStatusName" @click-input="openFieldPicker('redPacketStatus')" />
          </van-cell-group>
        </div>
        <div class="filter-actions">
          <van-button type="default" round size="normal" @click="resetFilter()">重置</van-button>
          <van-button type="primary" round size="normal" @click="applyFilter(true)">搜索</van-button>
        </div>
      </van-popup>

      <!-- 时间范围选择弹窗 -->
      <van-popup :show="showTimePicker" position="bottom" round :z-index="1000" lock-scroll closeable @close="showTimePicker = false">
        <div class="time-picker-header">
          <div class="time-picker-title">选择时间</div>
        </div>
        <div class="time-range-picker">
          <van-datetime-picker type="datetime" :value="currentTimeRange" :min-date="minDate" :max-date="maxDate" @confirm="confirmTimeRange" @cancel="showTimePicker = false" />
        </div>
      </van-popup>

      <!-- 新增：字段选择器弹窗 -->
      <van-popup :show="showPickerPopup" position="bottom" round :z-index="1001" lock-scroll closeable @close="showPickerPopup = false">
        <div class="picker-header">
          <div class="picker-title">选择{{ currentPickerField === "column" ? "栏目" : currentPickerField === "company" ? "训练营" : currentPickerField === "salesGroup" ? "销售组" : currentPickerField === "sales" ? "销售" : currentPickerField === "forbiddenStatus" ? "账号状态" : currentPickerField === "redPacketStatus" ? "红包状态" : "营期" }}</div>
        </div>
        <div class="picker-content">
          <van-picker show-toolbar :columns="pickerOptions" @confirm="onPickerConfirm" @cancel="showPickerPopup = false" />
        </div>
      </van-popup>

      <!-- 功能入口 -->
      <div class="feature-section">
        <h2 class="section-title">快捷功能</h2>
        <div class="feature-grid">
          <!-- 修改：添加点击事件 -->
          <div class="feature-item" v-for="(feature, index) in features" :key="index" @click="feature.label === '客户筛选' ? openFilter() : null">
            <van-icon :name="feature.icon" size="48rpx" color="#05497a" />
            <div class="feature-label">{{ feature.label }}</div>
          </div>
        </div>
      </div>
      <!-- 客户信息卡片 -->
      <div class="customer-card">
        <div class="customer-header">
          <van-icon name="user-circle-o" size="48rpx" color="#05497a" />
          <h2 class="customer-title">客户信息概览</h2>
        </div>
        <div class="info-grid">
          <div class="info-item">
            <div class="info-label">客户数</div>
            <div class="info-value highlight">{{ total }}</div>
          </div>
          <!--          <div class="info-item">-->
          <!--            <div class="info-label">本月新增</div>-->
          <!--            <div class="info-value highlight">{{ customerStats.monthNew }}</div>-->
          <!--          </div>-->
          <!--          <div class="info-item">-->
          <!--            <div class="info-label">活跃客户</div>-->
          <!--            <div class="info-value">{{ customerStats.activeCount }}</div>-->
          <!--          </div>-->
        </div>
      </div>
      <!-- 最新客户列表 -->
      <div class="customer-list">
        <h2 class="section-title">最近新增客户</h2>

        <!-- 替换下拉刷新组件 -->
        <div>
          <div class="customer-item" v-for="(customer, index) in recentCustomers" :key="index">
            <div class="customer-avatar">
              <img :src="customer.avatarUrl || require('@/static/images/defaultAvatar.png')" />
            </div>
            <div class="customer-info">
              <div class="customer-row">
                <span class="customer-name">{{ customer.nickname }}</span>
              </div>
              <div class="customer-row" @click="copyText(customer.customerId)">
                <span class="customer-id">ID: {{ customer.customerId }}</span>
              </div>
              <div class="customer-row">
                <span class="customer-mobile">手机号: {{ customer.mobile || "--" }}</span>
              </div>
              <div class="customer-row">
                <span class="customer-time">注册时间：{{ customer.customerCreatedAt }}</span>
              </div>
              <div class="customer-row">
                <span class="customer-time">活跃时间：{{ customer.lastActiveTime }}</span>
              </div>
              <div class="customer-row">
                <div class="customer-tags" @click="openTagPopup(customer)">
                  <!-- 自动标签（蓝色） - 移除数量限制 -->
                  <div class="tag-row">
                    <text v-for="(tag, idx) in getAllTags(customer.tags, 'tagsName')" :key="idx" class="tag-auto">
                      {{ tag }}
                    </text>
                  </div>

                  <!-- 手动标签（绿色） - 移除数量限制 -->
                  <div class="tag-row">
                    <text v-for="(tag, idx) in getAllTags(customer.tags, 'manualTagsName')" :key="idx" class="tag-manual">
                      {{ tag }}
                    </text>
                  </div>
                </div>
              </div>
              <div class="customer-row" @click="copyText(customer.unionId)">
                <span class="customer-unionId">UnionID: {{ customer.unionId }}</span>
              </div>
            </div>
            <!-- 新增详情按钮 -->
            <div class="action-buttons">
              <van-button size="small" custom-style="padding:0 24rpx; height: 60rpx; line-height: 60rpx; font-size: 26rpx; background: #67c23a; color: white; margin-bottom: 30rpx;" @click="handleOperation(customer)"> 操作 </van-button>
              <van-button size="small" custom-style="padding:0 24rpx; height: 60rpx; line-height: 60rpx; font-size: 26rpx; background: #05497a; color: white;" @click="openCustomerDetail(customer)"> 详情 </van-button>
            </div>
          </div>

          <!-- 添加加载状态提示 -->
          <div class="loading-tips">
            <van-loading v-if="loading" size="24px">加载中...</van-loading>
            <div v-if="finished" class="finished">没有更多了</div>
          </div>
        </div>
      </div>

      <!-- 新增标签弹窗 -->
      <van-popup :show="showTagPopup" position="bottom" round closeable @close="showTagPopup = false">
        <div class="picker-header">
          <div class="picker-title">客户标签</div>
        </div>
        <div class="tag-popup-content">
          <!-- 自动标签 -->
          <div v-if="getAllTags(currentCustomerTags, 'tagsName').length > 0" class="tag-section">
            <div class="tag-type">自动标签</div>
            <div class="tag-list">
              <span class="tag tag-auto" v-for="(tag, index) in getAllTags(currentCustomerTags, 'tagsName')" :key="index">
                {{ tag }}
              </span>
            </div>
          </div>
          <!-- 手动标签 -->
          <div v-if="getAllTags(currentCustomerTags, 'manualTagsName').length > 0" class="tag-section">
            <div class="tag-type">手动标签</div>
            <div class="tag-list">
              <span class="tag tag-manual" v-for="(tag, index) in getAllTags(currentCustomerTags, 'manualTagsName')" :key="index">
                {{ tag }}
              </span>
            </div>
          </div>
        </div>
      </van-popup>

      <!-- 新增：操作动作面板 -->
      <van-action-sheet :show="showActionSheet" :actions="actions" cancel-text="取消" close-on-click-action @select="onActionSelect" @cancel="showActionSheet = false" />

      <!-- 新增：回到顶部按钮 -->
      <div class="back-top" v-if="showBackTop" @click="goTop">
        <van-icon name="back-top" size="48rpx" color="#05497a" />
      </div>
    </div>
  </div>
</template>

<script>
import NavigationBar from "@/components/common/NavigationBar.vue";
import { getCampPeriodsByCompanyId } from "@/api/sys-camp-group.api";
import { queryUserByPage } from "@/api/system-user.api";
import { queryHeadquartersCompanyList } from "@/api/company.api";
import { delCustomer, queryCustomerPage, updCustomerStatus } from "@/api/customer.api";
import { listByCompanyId } from "@/api/sales-group.api";
import {getLayoutHeights} from "@/utils/get-page-height.util";

export default {
  components: {
    NavigationBar,
  },
  data() {
    return {
      customerStats: {
        totalCount: 128,
        monthNew: 24,
        activeCount: 87,
      },
      tabKey: new Date().getTime(),
      features: [{ icon: "filter-o", label: "客户筛选" }],
      weworkStatusOptions: [
          {
            text: '已添加',
            value: 1
          },
          {
            text: '未添加',
            value: 0
          },
          {
            text: '已删除',
            value: 9
          },
        ],
      statusOptions: [
        {
          text: '全部',
          value: ''
        },
        {
          text: '禁用',
          value: '1'
        },
      ],
      showFilterPopup: false,
      topHeight:  88,
      contentHeight: 0,
      filterParams: {
        nickname: "",
        nicknameQueryType: "1",
        createStartTime: this.getStartOfDay(),
        createEndTime: this.getEndOfDay(),
        activeStartTime: null,
        activeEndTime: null,
        mobile: "",
        customerId: "", // 客户Id
        unionId: "", // 微信unionId
        columnId: "",
        columnName: "", // 新增：栏目名称
        campPeriodId: "",
        campPeriodName: "", // 新增：营期名称
        companyId: "",
        companyName: "", // 新增：训练营名称
        salesGroupId: "",
        salesGroupName: "", // 新增：销售组名称
        salesId: "",
        salesName: "", // 新增：销售名称
        forbiddenStatus: "", // 新增：账号状态
        redPacketStatus: "", // 新增：红包状态
        forbiddenStatusName: "", // 新增：账号状态名称
        redPacketStatusName: "", // 新增：红包状态名称
        wechatRemark: '', // 企微备注
        weworkStatus: '', // 企微添加状态（0未添加/1已添加/9已删除）
      },
      recentCustomers: [],
      showTimePicker: false, // 控制时间选择器显示
      currentTimeField: "", // 当前编辑的时间字段
      currentTimeType: "",
      currentTimeRange: new Date().getTime(), // 当前选择的时间范围
      minDate: new Date(2025, 4, 1).getTime(), // 最小日期
      maxDate: new Date().getTime(), // 最大日期
      showPickerPopup: false, // 控制选择器弹窗显示
      currentPickerField: "", // 当前选择的字段类型
      pickerOptions: [], // 选择器选项数据
      // pickerValue: "", // 当前选择的值
      // 添加分页相关数据
      currentPage: 1, // 当前页码
      pageSize: 12, // 每页条数
      total: 0, // 总记录数

      // 添加下拉刷新相关状态
      refreshing: false, // 是否正在下拉刷新
      loading: false, // 是否正在加载更多
      finished: false, // 是否已加载所有数据
      showTagPopup: false, // 控制标签弹窗显示
      currentCustomerTags: [], // 当前点击的客户标签

      // 新增：操作面板相关状态
      showActionSheet: false,
      currentOperationCustomer: null,
      actions: [],
      isAdmin: false, // 新增：管理员状态标识
      showBackTop: false, // 新增：控制回到顶部按钮显示
    };
  },
  async mounted() {
    await this.initColumn();
  },
  onShow() {
    const { topHeight, contentHeight } = getLayoutHeights();
    this.topHeight = topHeight;
    this.contentHeight = contentHeight;
    this.tabKey = new Date().getTime();
    this.applyFilter(true);
    // 新增：初始化管理员状态
    const user = uni.getStorageSync("userInfo");
    if (user) {
      // 角色类型为'1'表示超级管理员
      this.isAdmin = user.roleType === 1;
    }
  },
  methods: {
    /**
     * 复制文本到剪贴板
     * @param text 要复制的文本
     */
    copyText(text) {
      if (!text) {
        uni.showToast({
          title: "无内容可复制",
          icon: "none",
        });
        return;
      }

      uni.setClipboardData({
        data: text,
        success: () => {
          uni.showToast({
            title: "复制成功",
            icon: "success",
            duration: 1500,
          });
        },
      });
    },
    openCustomerDetail(customer) {
      uni.navigateTo({
        url: `/pages/customer-mgmt/customer-detail?customerId=${customer.customerId}&nickname=${customer.nickname}&mobile=${customer.mobile || "--"}&avatarUrl=${customer.avatarUrl}`,
      });
    },
    openFilter() {
      this.showFilterPopup = true;
    },
    resetFilter() {
      this.filterParams.nickname = null;
      this.filterParams.nicknameQueryType = "2";
      this.filterParams.createStartTime = this.getStartOfDay();
      this.filterParams.createEndTime = this.getEndOfDay();
      this.filterParams.activeStartTime = null;
      this.filterParams.activeEndTime = null;
      this.filterParams.mobile = null;
      this.filterParams.customerId = null; // 客户Id
      this.filterParams.unionId = null; // 微信unionId
      this.filterParams.campPeriodId = null;
      this.filterParams.campPeriodName = null; // 重置时清空名称
      this.filterParams.companyId = null;
      this.filterParams.companyName = null; // 重置时清空名称
      this.filterParams.columnId = null;
      this.filterParams.columnName = null; // 重置时清空名称
      this.filterParams.salesGroupId = null;
      this.filterParams.salesGroupName = null; // 重置时清空名称
      this.filterParams.salesId = null;
      this.filterParams.salesName = null; // 重置时清空名称
      this.filterParams.forbiddenStatus = null; // 新增：账号状态
      this.filterParams.redPacketStatus = null;
      this.filterParams.forbiddenStatusName = null; // 新增：账号状态名称
      this.filterParams.redPacketStatusName = null; // 新增：红包状态名称
      this.filterParams.wechatRemark = null; // 企微备注
      this.filterParams.weworkStatus = null; // 企微添加状态（0未添加/1已添加/9已删除）
      console.log("重置过滤条件:resetFilter===============", this.filterParams);
      this.applyFilter(true);
    },
    async applyFilter(reset) {
      console.log("应用过滤条件:applyFilter===============");
      if (reset) {
        this.currentPage = 1;
        this.finished = false;
      }
      // 检查加载完成状态
      if (this.loading || this.finished) {
        return;
      }
      // 显示加载状态
      this.loading = true;

      const params = {
        nickname: this.filterParams.nickname,
        nicknameQueryType: this.filterParams.nicknameQueryType,
        // 拆分客户创建时间为开始和结束时间
        createStartTime: this.formatDate(this.filterParams.createStartTime), // 创建开始时间
        createEndTime: this.formatDate(this.filterParams.createEndTime), // 创建结束时间
        // 拆分客户活跃时间为开始和结束时间
        activeStartTime: this.formatDate(this.filterParams.activeStartTime), // 活跃开始时间
        activeEndTime: this.formatDate(this.filterParams.activeEndTime), // 活跃结束时间
        mobile: this.filterParams.mobile,
        columnId: this.filterParams.columnId,
        campPeriodId: this.filterParams.campPeriodId,
        companyId: this.filterParams.companyId,
        salesGroupId: this.filterParams.salesGroupId,
        salesId: this.filterParams.salesId,
        forbiddenStatus: this.filterParams.forbiddenStatus, // 新增：账号状态
        redPacketStatus: this.filterParams.redPacketStatus, // 新增：红包状态
      };

      const query = {
        pageNum: this.currentPage,
        pageSize: this.pageSize / 4,
      };

      try {
        const res = await queryCustomerPage(params, query);
        if (res.code === 0) {
          // 如果是第一页，替换数据；否则追加数据
          if (this.currentPage === 1) {
            this.recentCustomers = res.data.records;
          } else {
            this.recentCustomers = [...this.recentCustomers, ...res.data.records];
          }
          this.total = res.data.total;
          // 检查是否已加载所有数据
          this.finished = res.data.total <= this.recentCustomers.length;
          // 仅在请求成功时递增页码
          if (res.data.records.length > 0) {
            this.currentPage++;
          }
          this.showFilterPopup = false;
        } else {
          this.showFilterPopup = false;
          console.warn("加载客户列表失败:", res.msg);
          uni.showToast({
            title: "加载客户列表失败",
            icon: "none",
            duration: 2000,
          });
        }
      } catch (error) {
        console.error("加载客户列表异常:", error);
        uni.showToast({
          title: "加载客户列表失败，请稍后重试",
          icon: "none",
          duration: 2000,
        });
      } finally {
        this.loading = false;
      }
    },
    // 新增：获取当天开始时间
    getStartOfDay() {
      const date = new Date();
      date.setHours(0, 0, 0, 0);
      return date.getTime();
    },

    // 新增：获取当天结束时间
    getEndOfDay() {
      const date = new Date();
      date.setHours(23, 59, 59, 999);
      return date.getTime();
    },

    // 修改时间格式化方法，增加时分秒显示
    formatDate(timestamp) {
      if (!timestamp) return "";
      const date = new Date(timestamp);
      // 增加时分秒格式化
      const hours = date.getHours().toString().padStart(2, "0");
      const minutes = date.getMinutes().toString().padStart(2, "0");
      const seconds = date.getSeconds().toString().padStart(2, "0");
      return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, "0")}-${date.getDate().toString().padStart(2, "0")} ${hours}:${minutes}:${seconds}`;
    },
    openTimePicker(field, type) {
      console.log("打开时间选择器:", field, type);
      this.currentTimeField = field;
      this.currentTimeType = type;
      // 初始化时间范围数组
      let start, end;
      if (field === "createTime") {
        start = this.filterParams.createStartTime;
        end = this.filterParams.createEndTime;
      } else if (field === "activeTime") {
        start = this.filterParams.activeStartTime;
        end = this.filterParams.activeEndTime;
      }
      if (type === "start") {
        this.currentTimeRange = start ? start : new Date().getTime();
      } else if (type === "end") {
        this.currentTimeRange = end ? end : new Date().getTime();
      }
      this.showTimePicker = true;
    },

    confirmTimeRange(value) {
      this.currentTimeRange = value.detail;
      console.log("确认时间value:", value);
      console.log("确认时间:", this.currentTimeRange);
      if (this.currentTimeField === "createTime") {
        if (this.currentTimeType === "start") {
          this.filterParams.createStartTime = this.currentTimeRange;
        } else if (this.currentTimeType === "end") {
          this.filterParams.createEndTime = this.currentTimeRange;
        }
      } else if (this.currentTimeField === "activeTime") {
        if (this.currentTimeType === "start") {
          this.filterParams.activeStartTime = this.currentTimeRange;
        } else if (this.currentTimeType === "end") {
          this.filterParams.activeEndTime = this.currentTimeRange;
        }
      }
      console.log("this.filterParams:", this.filterParams);
      this.showTimePicker = false;
    },

    // 打开字段选择器
    openFieldPicker(field) {
      this.currentPickerField = field;
      // 根据字段类型加载选项数据
      switch (field) {
        case "column":
          this.loadColumnOptions();
          break;
        case "company":
          this.loadCompanyOptions();
          break;
        case "salesGroup":
          this.loadSalesGroupOptions();
          break;
        case "sales":
          this.loadSalesOptions();
          break;
        case "campPeriod":
          this.loadCampPeriodOptions();
          break;
        case "forbiddenStatus":
          this.loadForbiddenStatusOptions();
          break;
        case "redPacketStatus":
          this.loadRedPacketStatusOptions();
          break;
        case "weworkStatus":
          this.loadWeworkStatusOptions();
          break;
      }
    },

    async initColumn() {
      try {
        const res = await queryHeadquartersCompanyList({ id: 31000, level: 2 });
        if (res.code === 0 && res.data) {
          this.pickerOptions = res.data.columns.map((item) => ({
            text: item.name,
            value: item.id,
          }));
          if (this.pickerOptions && this.pickerOptions.length > 0) {
            this.filterParams.columnId = this.pickerOptions[0].value;
            this.filterParams.columnName = this.pickerOptions[0].text;
          }
        } else {
          console.warn("加载栏目选项失败:", res.msg);
          uni.showToast({
            title: "加载栏目失败",
            icon: "none",
            duration: 2000,
          });
        }
      } catch (error) {
        console.error("加载栏目选项异常:", error);
        uni.showToast({
          title: "加载栏目失败，请稍后重试",
          icon: "none",
          duration: 2000,
        });
      }
    },
    // 新增：加载禁用状态选项
    async loadForbiddenStatusOptions() {
      this.pickerOptions = this.statusOptions;
      this.showPickerPopup = true;
    },
    // 新增：加载红包状态选项
    async loadRedPacketStatusOptions() {
      this.pickerOptions = this.statusOptions;
      this.showPickerPopup = true;
    },
    // 新增：加载企微添加状态选项
    async loadWeworkStatusOptions() {
      this.pickerOptions = this.weworkStatusOptions;
      this.showPickerPopup = true;
    },
    // 加载栏目选项 (优化后)
    async loadColumnOptions() {
      try {
        const res = await queryHeadquartersCompanyList({ id: 31000, level: 2 });
        if (res.code === 0 && res.data) {
          this.pickerOptions = res.data.columns.map((item) => ({
            text: item.name,
            value: item.id,
          }));
          this.showPickerPopup = true;
        } else {
          console.warn("加载栏目选项失败:", res.msg);
          uni.showToast({
            title: "加载栏目失败",
            icon: "none",
            duration: 2000,
          });
        }
      } catch (error) {
        console.error("加载栏目选项异常:", error);
        uni.showToast({
          title: "加载栏目失败，请稍后重试",
          icon: "none",
          duration: 2000,
        });
      }
    },

    async loadCompanyOptions() {
      if (!this.filterParams.columnId) {
        uni.showToast({
          title: "请先选择栏目",
          icon: "none",
          duration: 2000,
        });
        return;
      }

      try {
        const res = await queryHeadquartersCompanyList({ id: 31000, level: 2 });
        if (res.code === 0 && res.data) {
          const column = res.data.columns.find((item) => item.id === this.filterParams.columnId);
          if (column && column.companies) {
            this.pickerOptions = column.companies.map((item) => ({
              text: item.name,
              value: item.id,
            }));
          }
          console.log("this.pickerOptions:", this.pickerOptions);
          this.showPickerPopup = true;
        } else {
          console.warn("加载训练营选项失败:", res.msg);
          uni.showToast({ title: "加载训练营失败", icon: "none" });
        }
      } catch (error) {
        console.error("加载训练营选项异常:", error);
        uni.showToast({ title: "加载训练营失败，请稍后重试", icon: "none" });
      }
    },

    async loadSalesGroupOptions() {
      if (!this.filterParams.companyId) {
        uni.showToast({
          title: "请先选择训练营",
          icon: "none",
          duration: 2000,
        });
        return;
      }

      try {
        const res = await listByCompanyId({
          companyId: this.filterParams.companyId,
        });
        if (res.code === 0 && res.data) {
          this.pickerOptions = res.data.map((item) => ({
            text: item.salesGroupName,
            value: item.id,
          }));
          this.showPickerPopup = true;
        } else {
          console.warn("加载销售组选项失败:", res.msg);
          uni.showToast({ title: "加载销售组失败", icon: "none" });
        }
      } catch (error) {
        console.error("加载销售组选项异常:", error);
        uni.showToast({ title: "加载销售组失败，请稍后重试", icon: "none" });
      }
    },

    async loadSalesOptions() {
      if (!this.filterParams.salesGroupId) {
        uni.showToast({
          title: "请先选择销售组",
          icon: "none",
          duration: 2000,
        });
        return;
      }

      try {
        const res = await queryUserByPage({
          current: 1,
          size: 100,
          salesGroupId: this.filterParams.salesGroupId,
          status: 1,
          auditStatus: 2,
        });
        if (res.code === 0 && res.data) {
          this.pickerOptions = res.data.records.map((item) => ({
            text: item.username,
            value: item.id,
          }));
          this.showPickerPopup = true;
        } else {
          console.warn("加载销售选项失败:", res.msg);
          uni.showToast({ title: "加载销售失败", icon: "none" });
        }
      } catch (error) {
        console.error("加载销售选项异常:", error);
        uni.showToast({ title: "加载销售失败，请稍后重试", icon: "none" });
      }
    },

    async loadCampPeriodOptions() {
      if (!this.filterParams.companyId) {
        uni.showToast({
          title: "请先选择训练营",
          icon: "none",
          duration: 2000,
        });
        return;
      }

      try {
        const res = await getCampPeriodsByCompanyId({
          companyId: this.filterParams.companyId,
        });
        if (res.code === 0 && res.data) {
          this.pickerOptions = res.data.map((item) => ({
            text: item.campperiodName,
            value: item.id,
          }));
          this.showPickerPopup = true;
        } else {
          console.warn("加载营期选项失败:", res.msg);
          uni.showToast({ title: "加载营期失败", icon: "none" });
        }
      } catch (error) {
        console.error("加载营期选项异常:", error);
        uni.showToast({ title: "加载营期失败，请稍后重试", icon: "none" });
      }
    },

    // 确认选择器选择
    onPickerConfirm(value) {
      this.showPickerPopup = false;

      // 获取选中项的文本和值
      const selectedValue = value.detail.value.value;
      const selectedText = value.detail.value.text;

      // 根据字段类型更新筛选参数
      switch (this.currentPickerField) {
        case "column":
          this.filterParams.columnId = selectedValue;
          this.filterParams.columnName = selectedText; // 设置显示文本
          // 清空依赖字段
          this.filterParams.companyId = "";
          this.filterParams.companyName = ""; // 同时清空名称
          this.filterParams.salesGroupId = "";
          this.filterParams.salesGroupName = ""; // 同时清空名称
          this.filterParams.salesId = "";
          this.filterParams.salesName = ""; // 同时清空名称
          this.filterParams.campPeriodId = "";
          this.filterParams.campPeriodName = ""; // 同时清空名称
          break;
        case "company":
          this.filterParams.companyId = selectedValue;
          this.filterParams.companyName = selectedText; // 设置显示文本
          // 清空依赖字段
          this.filterParams.salesGroupId = "";
          this.filterParams.salesGroupName = ""; // 同时清空名称
          this.filterParams.salesId = "";
          this.filterParams.salesName = ""; // 同时清空名称
          this.filterParams.campPeriodId = "";
          this.filterParams.campPeriodName = ""; // 同时清空名称
          break;
        case "salesGroup":
          this.filterParams.salesGroupId = selectedValue;
          this.filterParams.salesGroupName = selectedText; // 设置显示文本
          this.filterParams.salesId = "";
          this.filterParams.salesName = ""; // 同时清空名称
          break;
        case "sales":
          this.filterParams.salesId = selectedValue;
          this.filterParams.salesName = selectedText; // 设置显示文本
          break;
        case "campPeriod":
          this.filterParams.campPeriodId = selectedValue;
          this.filterParams.campPeriodName = selectedText; // 设置显示文本
          break;
        case "forbiddenStatus":
          this.filterParams.forbiddenStatus = selectedValue;
          this.filterParams.forbiddenStatusName = selectedText; // 设置显示文本
          break;
        case "redPacketStatus":
          this.filterParams.redPacketStatus = selectedValue;
          this.filterParams.redPacketStatusName = selectedText; // 设置显示文本
          break;
      }
    },
    // 新增：获取所有标签（无数量限制）
    getAllTags(tags, name) {
      if (!tags || !Array.isArray(tags)) {
        return [];
      }
      const allTags = [];

      tags.forEach((item) => {
        if (item[name]) {
          const tagsArr = item[name].split(",");
          tagsArr.forEach((tag) => {
            if (tag && tag.trim()) {
              allTags.push(tag.trim());
            }
          });
        }
      });

      return allTags;
    },

    // 打开标签弹窗
    openTagPopup(customer) {
      this.currentCustomerTags = customer.tags || [];
      this.showTagPopup = true;
    },

    // 修改：操作按钮点击事件（添加管理员权限判断）
    handleOperation(customer) {
      this.currentOperationCustomer = customer;

      // 动态生成操作选项
      this.actions = [
        {
          name: +customer.forbiddenStatus === 1 ? "启用账号" : "禁用账号",
          type: "toggleAccountStatus",
        },
        {
          name: +customer.redPacketStatus === 1 ? "启用红包" : "禁用红包",
          type: "toggleRedPacketStatus",
        },
      ];

      // 新增：只有管理员才显示删除选项
      if (this.isAdmin) {
        this.actions.push({
          name: "删除账户",
          type: "deleteAccount",
          color: "#ee0a24", // 红色强调
        });
      }

      this.showActionSheet = true;
    },

    // 新增：操作面板选择处理
    async onActionSelect(event) {
      const { type } = event.detail;
      switch (type) {
        case "toggleAccountStatus":
          await this.toggleAccountStatus();
          break;
        case "toggleRedPacketStatus":
          await this.toggleRedPacketStatus();
          break;
        case "deleteAccount":
          await this.deleteAccount();
          break;
      }
      this.showActionSheet = false;
    },

    // 新增：切换账号状态
    async toggleAccountStatus() {
      const customer = this.currentOperationCustomer;
      if (!customer) return;

      const params = {
        customerIds: [customer.customerId],
        forbiddenStatus: customer.forbiddenStatus === 1 ? 0 : 1,
      };

      try {
        uni.showLoading({ title: "处理中...", mask: true });
        const res = await updCustomerStatus(params);

        if (res.code === 0) {
          uni.hideLoading();
          uni.showToast({ title: "操作成功", icon: "success" });
          // 更新本地数据状态
          customer.forbiddenStatus = params.forbiddenStatus;
          // 刷新列表
          await this.applyFilter(true);
        } else {
          uni.hideLoading();
          uni.showToast({ title: res.msg || "操作失败", icon: "none" });
        }
      } catch (error) {
        console.error("切换账号状态失败:", error);
        uni.showToast({ title: "操作失败，请重试", icon: "none" });
      }
    },

    // 新增：切换红包状态
    async toggleRedPacketStatus() {
      const customer = this.currentOperationCustomer;
      if (!customer) return;

      const params = {
        customerIds: [customer.customerId],
        redPacketStatus: customer.redPacketStatus === 1 ? 0 : 1,
      };

      try {
        uni.showLoading({ title: "处理中...", mask: true });
        const res = await updCustomerStatus(params);

        if (res.code === 0) {
          uni.showToast({ title: "操作成功", icon: "success" });
          // 更新本地数据状态
          customer.redPacketStatus = params.redPacketStatus;
        } else {
          uni.showToast({ title: res.msg || "操作失败", icon: "none" });
        }
      } catch (error) {
        console.error("切换红包状态失败:", error);
        uni.showToast({ title: "操作失败，请重试", icon: "none" });
      } finally {
        uni.hideLoading();
      }
    },

    // 新增：删除账户
    async deleteAccount() {
      const customer = this.currentOperationCustomer;
      if (!customer || !customer.customerId || !customer.unionId) {
        uni.showToast({ title: "客户信息不完整", icon: "none" });
        return;
      }

      try {
        // 确认弹窗
        uni.showModal({
          title: "删除确认",
          content: "确认删除此账户吗？此操作不可恢复，将永久删除该用户所有数据。",
          confirmText: "确认删除",
          confirmColor: "#ee0a24",
          success: async (res) => {
            if (res.confirm) {
              uni.showLoading({ title: "删除中...", mask: true });

              const params = {
                customerId: customer.customerId,
                unionId: customer.unionId,
              };

              const res = await delCustomer(params);

              if (res.code === 0) {
                uni.showToast({ title: "账户删除成功", icon: "success" });
                // 刷新列表
                await this.applyFilter(true);
              } else {
                uni.showToast({ title: res.msg || "删除失败", icon: "none" });
              }
            }
          },
        });
      } catch (error) {
        console.error("删除账户失败:", error);
        uni.showToast({ title: "删除失败，请重试", icon: "none" });
      } finally {
        uni.hideLoading();
      }
    },

    // 新增：回到顶部方法
    goTop() {
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 600,
      });
    },
  },
  onPageScroll(e) {
    // 新增：滚动监听，超过300rpx显示按钮
    this.showBackTop = e.scrollTop > 1300;
  },
  onReachBottom() {
    if (this.loading || this.finished) return;
    this.applyFilter();
  },
};
</script>

<style scoped lang="scss">
.home-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #f0f7ff 0%, #ffffff 100%);
  padding: 20rpx;
  ::v-deep .van-popup--bottom.van-popup--safe {
    padding-bottom: 0;
  }
  ::v-deep .van-cell {
    border-radius: 0 !important;
    background-color: rgba(255, 255, 255, 0.95) !important;
  }
}

.customer-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  box-shadow: 0 6rpx 24rpx rgba(64, 158, 255, 0.15);
  padding: 32rpx;
  margin-bottom: 32rpx;

  .customer-header {
    display: flex;
    align-items: center;
    margin-bottom: 32rpx;

    .customer-title {
      font-size: 38rpx;
      color: #1a1a1a;
      font-weight: 650;
      margin-left: 16rpx;
      position: relative;
      padding-left: 20rpx;

      &::before {
        content: "";
        position: absolute;
        left: 0;
        top: 8rpx;
        bottom: 8rpx;
        width: 6rpx;
        background: #05497a;
        border-radius: 4rpx;
      }
    }
  }

  .info-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 24rpx;

    .info-item {
      text-align: center;
      padding: 24rpx 0;
      background: #f5f8ff;
      border-radius: 16rpx;

      .info-label {
        font-size: 28rpx;
        color: #666;
        margin-bottom: 12rpx;
      }

      .info-value {
        font-size: 36rpx;
        font-weight: bold;
        color: #1a1a1a;

        &.highlight {
          color: #05497a;
        }
      }
    }
  }
}

.feature-section {
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 6rpx 24rpx rgba(0, 0, 0, 0.08);
  padding: 32rpx;
  margin-bottom: 32rpx;

  .section-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #1a1a1a;
    margin-bottom: 32rpx;
    position: relative;
    padding-left: 20rpx;

    &::before {
      content: "";
      position: absolute;
      left: 0;
      top: 8rpx;
      bottom: 8rpx;
      width: 6rpx;
      background: #05497a;
      border-radius: 4rpx;
    }
  }

  .feature-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 24rpx;

    .feature-item {
      text-align: center;

      .feature-label {
        margin-top: 16rpx;
        font-size: 26rpx;
        color: #333;
      }
    }
  }
}

.customer-list {
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 6rpx 24rpx rgba(0, 0, 0, 0.08);
  padding: 32rpx;

  .section-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #1a1a1a;
    margin-bottom: 32rpx;
    position: relative;
    padding-left: 20rpx;

    &::before {
      content: "";
      position: absolute;
      left: 0;
      top: 8rpx;
      bottom: 8rpx;
      width: 6rpx;
      background: #05497a;
      border-radius: 4rpx;
    }
  }

  .customer-item {
    display: flex;
    align-items: center;
    padding: 24rpx 0;
    border-bottom: 1rpx solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .customer-avatar {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      overflow: hidden;
      background: #f0f0f0;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .customer-info {
      flex: 1;
      margin-left: 24rpx;

      .customer-row {
        display: flex;
        margin-bottom: 8rpx;
        align-items: center;

        .customer-id {
          font-size: 24rpx;
          color: #333;
          margin-right: 20rpx;
          min-width: 190rpx;
        }

        .customer-name {
          font-size: 32rpx;
          font-weight: 500;
          color: #1a1a1a;
        }

        .customer-mobile {
          font-size: 28rpx;
          color: #333;
          min-width: 240rpx;
        }

        .customer-time {
          font-size: 24rpx;
          color: #999;
          margin-right: 20rpx;
        }

        .customer-tags {
          display: flex;
          flex-direction: column;
          gap: 10rpx;
          margin-top: 10rpx;
          max-height: 200rpx; /* 设置最大高度 */
          overflow-y: auto; /* 允许滚动 */
        }

        .tag-row {
          display: flex;
          flex-wrap: wrap; /* 允许换行 */
          gap: 10rpx;
        }

        .customer-unionId {
          font-size: 24rpx;
          color: #666;
          min-width: 360rpx;
        }
      }
    }

    .action-buttons {
      display: flex;
      flex-direction: column;
      margin-left: 20rpx;
      flex-shrink: 0;
    }
  }
}

/* 新增筛选弹窗样式 */
.filter-popup {
  background: #fff;
  display: flex;
  flex-direction: column;
  //padding-top: 30rpx;
}
.filter-header {
  height: 100rpx;
  .filter-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #05497a;
    text-align: center;
    line-height: 100rpx;
    width: 100%;
  }
}

.filter-content {
  flex: 1;
  height: 800rpx;
  overflow-y: auto;
  box-sizing: border-box; /* 新增，确保内边距不增加总高度 */
}

.filter-actions {
  margin-bottom: 40rpx;
  padding: 15rpx 20rpx;
  display: flex;
  justify-content: flex-end;
  gap: 30rpx; /* 增大按钮间距至30rpx */

  ::v-deep .van-button {
    height: 60rpx;
    font-size: 28rpx;
  }
  ::v-deep .van-button--primary {
    background: #05497a;
    border-color: #05497a;
  }
}

/* 时间范围选择弹窗样式 */
.time-picker-header {
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;

  .time-picker-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #05497a;
  }
}

.time-range-picker {
  padding: 30rpx;
  height: 500rpx;

  /* 新增：适配范围选择器样式 */
  ::v-deep .van-picker {
    width: 100%;
  }
}

/* 新增选择器弹窗样式 */
.picker-header {
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;

  .picker-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #05497a;
  }
}

.picker-content {
  padding: 30rpx;
  height: 500rpx;
}

/* 分页组件样式 */
.van-pagination {
  margin-top: 30rpx;
  padding: 20rpx 0;
  justify-content: center;
}

/* 添加加载提示样式 */
.loading-tips {
  text-align: center;
  padding: 20rpx 0;

  .finished {
    color: #999;
    font-size: 24rpx;
    padding: 20rpx 0;
  }
}

.tag-auto {
  display: inline-block;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  font-size: 24rpx;
  background: #ecf5ff; /* 蓝色背景 */
  color: #05497a; /* 蓝色文字 */
  margin-right: 10rpx;
  margin-bottom: 10rpx;
}

.tag-manual {
  display: inline-block;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  font-size: 24rpx;
  background: #f0f9eb; /* 绿色背景 */
  color: #67c23a; /* 绿色文字 */
  margin-right: 10rpx;
  margin-bottom: 10rpx;
}

.tag-popup-content {
  padding: 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.tag-section {
  margin-bottom: 30rpx;
}

.tag-type {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.tag {
  display: inline-block;
  padding: 8rpx 20rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
}

.tag-auto {
  background: #ecf5ff;
  color: #05497a;
}

.tag-manual {
  background: #f0f9eb;
  color: #67c23a;
}

.tag-popup-actions {
  padding: 20rpx;
  text-align: center;
  border-top: 1rpx solid #f0f0f0;
}

// 新增：回到顶部按钮样式
.back-top {
  position: fixed;
  right: 40rpx;
  bottom: 180rpx; /* 位于tabbar上方 */
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}
</style>
