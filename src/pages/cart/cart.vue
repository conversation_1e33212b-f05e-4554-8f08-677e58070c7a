<template>
  <view class="cart-page" :style="{ height: contentHeight + 'px', paddingTop: topHeight + 'px' }">
    <navigation-bar style="width: 100%; z-index: 99" :showIcon="false" :showText="true" text="购物车"></navigation-bar>

    <view class="cart-content" v-if="cartItems.length > 0">
      <!-- 购物车商品列表 -->
      <view class="cart-list">
        <view v-for="(item, index) in cartItems" :key="index" class="cart-item-wrapper">
          <view class="cart-item" :style="{ transform: `translateX(${item.translateX || 0}px)` }" @touchstart="onTouchStart($event, index)" @touchmove="onTouchMove($event, index)" @touchend="onTouchEnd($event, index)">
            <view class="item-checkbox">
              <van-checkbox :value="item.selected" @change="toggleItemSelect(index)" />
            </view>

            <image :src="item.image" class="item-image" mode="aspectFill" @click="goToProductDetail(item)" />

            <view class="item-info">
              <view class="item-details" @click="goToProductDetail(item)">
                <text class="item-name">{{ item.name }}</text>
                <text class="item-spec">{{ item.spec }}</text>
              </view>
              <view class="item-bottom">
                <text class="item-price" @click="goToProductDetail(item)">¥{{ item.price }}</text>
                <view class="quantity-control" @click.stop>
                  <van-icon name="minus" size="16" @click.stop="decreaseQuantity(index)" :class="{ disabled: item.quantity <= 1 }" />
                  <text class="quantity">{{ item.quantity }}</text>
                  <van-icon name="plus" size="16" @click.stop="increaseQuantity(index)" />
                </view>
              </view>
            </view>
          </view>

          <!-- 左滑显示的操作按钮 -->
          <view class="item-actions">
            <view class="action-btn favorite-btn" @click="toggleFavorite(index)">
              <van-icon :name="item.isFavorite ? 'star' : 'star-o'" size="20" color="#fff" />
              <text class="action-text">{{ item.isFavorite ? "已收藏" : "收藏" }}</text>
            </view>
            <view class="action-btn delete-btn" @click="deleteItem(index)">
              <van-icon name="delete-o" size="20" color="#fff" />
              <text class="action-text">删除</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 空购物车 -->
    <view v-else class="empty-cart">
      <van-empty description="购物车是空的" />
      <van-button type="primary" @click="goShopping">去逛逛</van-button>

      <!-- 推荐商品 -->
      <view class="recommend-section">
        <view class="recommend-title">为你推荐</view>
        <view class="recommend-grid">
          <view v-for="(product, index) in recommendProducts" :key="index" class="recommend-item" @click="addToCart(product)">
            <image :src="product.image" class="recommend-image" mode="aspectFill" />
            <view class="recommend-info">
              <text class="recommend-name">{{ product.name }}</text>
              <text class="recommend-price">¥{{ product.price }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部结算栏 -->
    <view class="cart-footer" v-if="cartItems.length > 0">
      <view class="footer-left">
        <van-checkbox v-model="allSelectedModel" @change="toggleAllSelect" class="select-all-checkbox"> 全选 </van-checkbox>
        <view class="total-section">
          <view class="total-line">
            <text class="total-label">合计:</text>
            <text class="total-price">¥{{ totalPrice }}</text>
          </view>
          <text class="discount-info" v-if="actualDiscountAmount > 0">优惠: ¥{{ actualDiscountAmount.toFixed(2) }}</text>
        </view>
      </view>
      <view class="checkout-btn" :class="{ disabled: selectedItems.length === 0 }" @click="checkout">
        <text class="checkout-text">去结算({{ selectedItems.length }})</text>
      </view>
    </view>
  </view>
</template>

<script lang="ts">
import Vue from "vue";
import { Component } from "vue-property-decorator";
import NavigationBar from "../../components/common/NavigationBar.vue";
import { getDefaultAddress, payOrder } from "../../api/mall.api";
import { checkWechatPayEnvironment } from "../../utils/wechat-pay";
import { authManager } from "../../utils/auth";
import { Code } from "../../constants/enum/code.enum";
import { getLayoutHeights } from "@/utils/get-page-height.util";
import { getAppId } from "@/api/auth.api";

interface CartItem {
  id: string;
  name: string;
  spec: string;
  price: number;
  quantity: number;
  image: string;
  selected: boolean;
  isFavorite: boolean;
  translateX: number;
}

interface Address {
  id: string;
  name: string;
  phone: string;
  province: string;
  city: string;
  district: string;
  detail: string;
  fullAddress: string;
  tag: string;
  isDefault: number;
}

@Component({
  name: "cart",
  components: { NavigationBar },
})
export default class Cart extends Vue {
  cartItems: CartItem[] = [];
  loading = true;
  customerId = ""; // 用户ID，从存储中获取
  defaultAddress: Address | null = null; // 默认地址

  recommendProducts = [
    { name: "新鲜葡萄", price: "18.80", image: "/static/images/test.jpeg" },
    { name: "优质牛奶", price: "25.50", image: "/static/images/test.jpeg" },
    { name: "精选面包", price: "12.00", image: "/static/images/test.jpeg" },
    { name: "有机蔬菜", price: "8.80", image: "/static/images/test.jpeg" },
  ];

  discountAmount = 0.0; // 优惠金额，需要根据实际优惠券计算
  allSelectedModel = false;

  // 防抖定时器映射，key为商品ID
  quantityUpdateTimers: { [key: string]: any } = {};

  topHeight = 0;
  contentHeight = 0;

  // 触摸相关数据
  touchStartX = 0;
  touchStartY = 0;
  currentIndex = -1;

  get selectedItems() {
    return this.cartItems.filter((item) => item.selected);
  }

  get allSelected() {
    return this.cartItems.length > 0 && this.cartItems.every((item) => item.selected);
  }

  get totalPrice() {
    return this.selectedItems
      .reduce((total, item) => {
        return total + item.price * item.quantity;
      }, 0)
      .toFixed(2);
  }

  // 计算实际优惠金额
  get actualDiscountAmount() {
    // TODO: 这里应该根据用户选择的优惠券来计算实际优惠金额
    // 目前暂时返回0，等优惠券功能完善后再实现
    return 0;
  }

  // 获取用户ID（静默获取，不弹窗）
  async getUserId(): Promise<string | null> {
    return await authManager.getUserId(false);
  }

  // 页面显示时加载购物车数据
  async onShow() {
    const { topHeight, contentHeight } = getLayoutHeights();
    this.topHeight = topHeight;
    this.contentHeight = contentHeight;
    const userId = await this.getUserId();
    if (userId) {
      this.customerId = userId;
      await this.loadCartList();
    } else {
      // 未登录，显示空购物车状态，引导用户登录
      this.cartItems = [];
      this.loading = false;
      console.log("用户未登录，显示空购物车状态");
    }
  }

  // 加载购物车列表
  async loadCartList() {
    try {
      this.loading = true;
      const mallApi = await import("@/api/mall.api");
      const result = await mallApi.getCartList(this.customerId);

      // 添加数据类型检查和调试信息
      console.log("购物车API返回结果:", result);
      console.log("购物车数据类型:", typeof result.data);
      console.log("购物车数据内容:", result.data);

      // 检查API响应状态码
      if (result.code === Code.OK.code) {
        // 检查返回的数据结构
        if (result.data) {
          // 后端返回的是CartSummary对象，包含cartItems数组
          const cartData = result.data.cartItems || result.data;

          if (Array.isArray(cartData)) {
            this.cartItems = cartData.map((item: any) => ({
              ...item,
              selected: Boolean(item.selected),
              translateX: 0,
            }));

            // 如果有统计信息，也可以使用
            if (result.data.totalQuantity !== undefined) {
              console.log("购物车统计信息:", {
                totalQuantity: result.data.totalQuantity,
                selectedQuantity: result.data.selectedQuantity,
                totalAmount: result.data.totalAmount,
                selectedAmount: result.data.selectedAmount,
              });
            }
          } else {
            console.warn("购物车数据格式不正确:", result.data);
            this.cartItems = [];
          }
        } else {
          // 如果数据为空，设置为空数组
          console.log("购物车为空");
          this.cartItems = [];
        }
      } else {
        // API调用失败
        console.error("加载购物车失败:", result);
        this.cartItems = [];
        uni.showToast({
          title: result.msg || "加载购物车失败",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("加载购物车失败:", error);
      this.cartItems = []; // 确保设置为空数组
      uni.showToast({
        title: "加载失败",
        icon: "none",
      });
    } finally {
      this.loading = false;
    }
  }

  async toggleItemSelect(index: number) {
    const item = this.cartItems[index];
    const newSelected = !item.selected;

    try {
      const mallApi = await import("@/api/mall.api");
      await mallApi.batchSelectCartItems({
        customerId: this.customerId,
        cartItemIds: [item.id],
        selected: newSelected ? 1 : 0,
      });

      this.cartItems[index].selected = newSelected;
      this.updateAllSelectedModel();
    } catch (error) {
      console.error("更新选中状态失败:", error);
      uni.showToast({
        title: "操作失败",
        icon: "none",
      });
    }
  }

  async toggleAllSelect(value: boolean) {
    try {
      const mallApi = await import("@/api/mall.api");
      await mallApi.selectAllCartItems(this.customerId, value ? 1 : 0);

      this.cartItems.forEach((item) => {
        item.selected = value;
      });
      this.allSelectedModel = value;
    } catch (error) {
      console.error("全选操作失败:", error);
      uni.showToast({
        title: "操作失败",
        icon: "none",
      });
    }
  }

  updateAllSelectedModel() {
    // 更新全选checkbox的状态
    this.allSelectedModel = this.allSelected;
  }

  // 获取默认地址
  async loadDefaultAddress() {
    if (!this.customerId) {
      console.warn("购物车页面 - 用户ID为空，无法获取默认地址");
      return;
    }

    try {
      console.log("购物车页面 - 开始获取默认地址，用户ID:", this.customerId);
      const response = await getDefaultAddress(this.customerId);

      if (response && response.code === 0 && response.data) {
        this.defaultAddress = response.data;
        console.log("购物车页面 - 成功获取默认地址:", this.defaultAddress);
      } else {
        console.warn("购物车页面 - 未找到默认地址");
        this.defaultAddress = null;
      }
    } catch (error) {
      console.error("购物车页面 - 获取默认地址失败:", error);
      this.defaultAddress = null;
    }
  }

  async mounted() {
    // 获取用户ID
    const userId = await this.getUserId();
    if (userId) {
      this.customerId = userId;

      // 获取默认地址
      await this.loadDefaultAddress();
    }

    // 初始化全选状态
    this.updateAllSelectedModel();
  }

  onTouchStart(event: TouchEvent, index: number) {
    this.touchStartX = event.touches[0].clientX;
    this.touchStartY = event.touches[0].clientY;
    this.currentIndex = index;
  }

  onTouchMove(event: TouchEvent, index: number) {
    if (this.currentIndex !== index) return;

    const touchX = event.touches[0].clientX;
    const touchY = event.touches[0].clientY;
    const deltaX = touchX - this.touchStartX;
    const deltaY = touchY - this.touchStartY;

    // 判断是否为水平滑动
    if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 10) {
      event.preventDefault();

      // 限制滑动距离
      const maxSlide = -120; // 最大左滑距离
      let translateX = Math.max(deltaX, maxSlide);
      translateX = Math.min(translateX, 0); // 不允许右滑

      this.cartItems[index].translateX = translateX;
    }
  }

  onTouchEnd(event: TouchEvent, index: number) {
    if (this.currentIndex !== index) return;

    const item = this.cartItems[index];
    const threshold = -60; // 触发阈值

    if (item.translateX < threshold) {
      // 滑动距离超过阈值，显示操作按钮
      item.translateX = -120;
    } else {
      // 滑动距离不够，回弹
      item.translateX = 0;
    }

    this.currentIndex = -1;
  }

  toggleFavorite(index: number) {
    this.cartItems[index].isFavorite = !this.cartItems[index].isFavorite;
    const item = this.cartItems[index];
    uni.showToast({
      title: item.isFavorite ? "已添加到收藏" : "已取消收藏",
      icon: "success",
    });

    // 收藏后收起滑动
    item.translateX = 0;
  }

  increaseQuantity(index: number) {
    console.log("购物车 - 增加商品数量:", index);
    const item = this.cartItems[index];
    const newQuantity = item.quantity + 1;

    // 立即更新界面，提供即时反馈
    this.cartItems[index].quantity = newQuantity;
    this.updateAllSelectedModel();

    // 使用防抖机制调用API
    this.updateCartQuantityWithDebounce(item.id, newQuantity);
  }

  decreaseQuantity(index: number) {
    console.log("购物车 - 减少商品数量:", index);
    const item = this.cartItems[index];

    if (item.quantity <= 1) {
      console.log("商品数量已经是最小值，无法减少");
      return;
    }

    const newQuantity = item.quantity - 1;

    // 立即更新界面，提供即时反馈
    this.cartItems[index].quantity = newQuantity;
    this.updateAllSelectedModel();

    // 使用防抖机制调用API
    this.updateCartQuantityWithDebounce(item.id, newQuantity);
  }

  // 调用API更新购物车商品数量（带防抖）
  updateCartQuantityWithDebounce(cartItemId: string, quantity: number) {
    // 清除之前的定时器
    if (this.quantityUpdateTimers[cartItemId]) {
      clearTimeout(this.quantityUpdateTimers[cartItemId]);
    }

    // 设置新的定时器，500ms后执行API调用
    this.quantityUpdateTimers[cartItemId] = setTimeout(async () => {
      try {
        await this.updateCartQuantityAPI(cartItemId, quantity);
      } catch (error) {
        console.error("防抖更新数量失败:", error);
      }

      // 清除定时器
      delete this.quantityUpdateTimers[cartItemId];
    }, 500);
  }

  // 调用API更新购物车商品数量
  async updateCartQuantityAPI(cartItemId: string, quantity: number) {
    try {
      console.log("购物车 - 调用API更新数量:", { cartItemId, quantity, customerId: this.customerId });

      const mallApi = await import("@/api/mall.api");
      const result = await mallApi.updateCartQuantity({
        cartItemId: cartItemId,
        customerId: this.customerId,
        quantity: quantity,
      });

      console.log("购物车 - 数量更新API响应:", result);

      if (result.code === Code.OK.code) {
        console.log("购物车 - 数量更新成功");
      } else {
        console.error("购物车 - 数量更新失败:", result);
        throw new Error(result.msg || "更新数量失败");
      }
    } catch (error) {
      console.error("购物车 - 数量更新API调用失败:", error);
      throw error; // 重新抛出错误，让调用方处理
    }
  }

  async deleteItem(index: number) {
    const item = this.cartItems[index];

    uni.showModal({
      title: "确认删除",
      content: "确定要删除这个商品吗？",
      success: async (res) => {
        if (res.confirm) {
          try {
            // 调用后端API删除购物车商品
            const mallApi = await import("@/api/mall.api");
            const result = await mallApi.removeCartItem(item.id, this.customerId);

            console.log("购物车 - 删除商品API响应:", result);

            if (result.code === Code.OK.code) {
              // 删除成功后从前端列表中移除
              this.cartItems.splice(index, 1);
              this.updateAllSelectedModel();
              uni.showToast({ title: "删除成功", icon: "success" });
            } else {
              throw new Error(result.msg || "删除失败");
            }
          } catch (error) {
            console.error("删除购物车商品失败:", error);
            uni.showToast({
              title: error.message || "删除失败",
              icon: "none",
            });
            // 删除失败时收起滑动
            this.cartItems[index].translateX = 0;
          }
        } else {
          // 取消删除时收起滑动
          this.cartItems[index].translateX = 0;
        }
      },
    });
  }

  // 批量删除已选中的商品
  async deleteSelectedItems() {
    const selectedItems = this.cartItems.filter((item) => item.selected);

    if (selectedItems.length === 0) {
      uni.showToast({
        title: "请先选择要删除的商品",
        icon: "none",
      });
      return;
    }

    uni.showModal({
      title: "确认删除",
      content: `确定要删除这${selectedItems.length}个商品吗？`,
      success: async (res) => {
        if (res.confirm) {
          try {
            // 调用后端API批量删除购物车商品
            const mallApi = await import("@/api/mall.api");
            const cartItemIds = selectedItems.map((item) => item.id);

            // 使用批量删除API
            const result = await mallApi.batchRemoveCartItems({
              customerId: this.customerId,
              cartItemIds: cartItemIds,
            });

            console.log("购物车 - 批量删除API响应:", result);

            if (result.code === Code.OK.code) {
              // 删除成功后从前端列表中移除已选中的商品
              this.cartItems = this.cartItems.filter((item) => !item.selected);
              this.updateAllSelectedModel();

              uni.showToast({
                title: `成功删除${selectedItems.length}个商品`,
                icon: "success",
              });
            } else {
              throw new Error(result.msg || "批量删除失败");
            }
          } catch (error) {
            console.error("批量删除购物车商品失败:", error);
            uni.showToast({
              title: error.message || "删除失败",
              icon: "none",
            });
          }
        }
      },
    });
  }

  goShopping() {
    uni.switchTab({
      url: "/pages/index/index",
    });
  }

  // 跳转到商品详情页面
  goToProductDetail(item: any) {
    console.log("购物车 - 跳转到商品详情:", item.name);
    const productId = item.productId || item.id;
    if (productId) {
      uni.navigateTo({
        url: `/pages/product/product-detail?id=${productId}`,
        success: () => {
          console.log("购物车 - 成功跳转到商品详情页面");
        },
        fail: (error) => {
          console.error("购物车 - 跳转到商品详情页面失败:", error);
        },
      });
    } else {
      console.error("商品ID不存在:", item);
      uni.showToast({
        title: "商品信息异常",
        icon: "none",
      });
    }
  }

  async checkout() {
    if (this.selectedItems.length === 0) {
      uni.showToast({ title: "请选择要结算的商品", icon: "none" });
      return;
    }

    // 静默检查登录状态，未登录时直接跳转
    let userId = this.customerId;
    if (!userId) {
      userId = await authManager.getUserId(false); // 静默获取，不弹窗
      if (!userId) {
        // 未登录，跳转到登录页面或用户中心
        uni.showToast({
          title: "请先登录",
          icon: "none",
        });
        setTimeout(() => {
          uni.switchTab({
            url: "/pages/user-center/user-center",
          });
        }, 1500);
        return;
      }
      this.customerId = userId;
    }

    // 跳转到订单确认页面
    this.goToOrderConfirm();
  }

  // 去结算 - 跳转到订单确认页面
  goToOrderConfirm() {
    try {
      // 验证是否有选中的商品
      if (this.selectedItems.length === 0) {
        uni.showToast({
          title: "请选择要结算的商品",
          icon: "none",
        });
        return;
      }

      // 准备订单数据
      const orderData = {
        appId: getAppId(),
        customerId: this.customerId,
        addressId: this.defaultAddress.id, // 使用默认地址ID
        paymentMethod: "WECHAT", // 默认使用微信支付
        deliveryFee: 0.0, // 免配送费
        cartItemIds: this.selectedItems.map((item) => item.id),
        cartItems: this.selectedItems.map((item) => ({
          productId: item.productId || item.id,
          name: item.name,
          spec: item.spec || "",
          price: item.price,
          quantity: item.quantity,
          image: item.image,
        })),
      };

      console.log("购物车页面 - 跳转到订单确认页面，订单数据:", orderData);

      // 检查数据大小，避免URL过长
      const dataString = JSON.stringify(orderData);
      console.log("购物车页面 - 订单数据大小:", dataString.length, "字符");

      if (dataString.length > 2000) {
        console.warn("购物车页面 - 订单数据过大，可能导致跳转失败");
        uni.showToast({
          title: "订单数据过大，请重试",
          icon: "none",
        });
        return;
      }

      // 跳转到订单确认页面，传递订单数据
      uni.navigateTo({
        url: `/pages/orders/order-confirm?orderData=${encodeURIComponent(dataString)}`,
        success: () => {
          console.log("购物车页面 - 跳转成功");
        },
        fail: (err) => {
          console.error("购物车页面 - 跳转失败:", err);
          uni.showToast({
            title: "跳转失败，请重试",
            icon: "none",
          });
        },
      });
    } catch (error) {
      console.error("购物车页面 - 跳转订单确认页面失败:", error);
      uni.showToast({
        title: "操作失败，请重试",
        icon: "none",
      });
    }
  }

  addToCart(product: any) {
    const newItem: CartItem = {
      id: Date.now().toString(),
      name: product.name,
      spec: "默认规格",
      price: parseFloat(product.price),
      quantity: 1,
      image: product.image,
      selected: true,
    };
    this.cartItems.push(newItem);
    uni.showToast({ title: `${product.name} 已加入购物车`, icon: "success" });
  }

  // 页面销毁时清理定时器
  beforeDestroy() {
    // 清理所有防抖定时器
    Object.keys(this.quantityUpdateTimers).forEach((key) => {
      if (this.quantityUpdateTimers[key]) {
        clearTimeout(this.quantityUpdateTimers[key]);
      }
    });
    this.quantityUpdateTimers = {};
    console.log("购物车 - 已清理所有防抖定时器");
  }
}
</script>

<style lang="scss" scoped>
.cart-page {
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.cart-content {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 80px; /* 为底部固定栏留出空间 */
}

.cart-list {
  padding: 15px;
}

.cart-item-wrapper {
  position: relative;
  margin-bottom: 10px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.cart-item {
  background-color: #fff;
  padding: 15px;
  display: flex;
  align-items: center;
  transition: transform 0.3s ease;
  position: relative;
  z-index: 2;
}

.item-checkbox {
  display: flex;
  align-items: center;
}

.item-image {
  width: 85px;
  height: 85px;
  border-radius: 12px;
  margin-right: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 85px;
  min-width: 0;
}

.item-details {
  flex: 1;
  cursor: pointer;
}

.item-name {
  font-size: 16px;
  color: #333;
  font-weight: 500;
  line-height: 1.4;
  margin-bottom: 6px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.item-spec {
  font-size: 12px;
  color: #999;
  margin-bottom: 8px;
  background-color: #f5f5f5;
  padding: 2px 8px;
  border-radius: 4px;
  align-self: flex-start;
}

.item-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.item-price {
  font-size: 18px;
  color: #ff4444;
  font-weight: bold;
}

.quantity-control {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border-radius: 20px;
  padding: 4px;
  border: 1px solid #e9ecef;

  .van-icon {
    padding: 6px;
    border-radius: 50%;
    transition: all 0.2s ease;

    &:active {
      background-color: #e9ecef;
    }

    &.disabled {
      color: #ccc;
    }
  }

  .quantity {
    padding: 0 12px;
    font-size: 16px;
    color: #333;
    font-weight: 500;
    min-width: 24px;
    text-align: center;
  }
}

/* 左滑操作按钮 */
.item-actions {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 120px;
  display: flex;
  z-index: 1;
}

.action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
  color: #fff;
  font-size: 12px;
  transition: all 0.3s ease;
}

.favorite-btn {
  background: linear-gradient(135deg, #ff9500 0%, #ffb84d 100%);
}

.delete-btn {
  background: linear-gradient(135deg, #ff4444 0%, #ff6b6b 100%);
}

.action-text {
  font-size: 11px;
  color: #fff;
  font-weight: 500;
}

.empty-cart {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 50px 20px;

  .van-button {
    margin-top: 20px;
    width: 200px;
  }
}

.cart-footer {
  background-color: #fff;
  padding: 10px 15px;
  border-radius: 50px;
  margin: 8px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.06);
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
}

.footer-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.select-all-checkbox {
  margin-right: 15px;
}

.total-section {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.total-line {
  display: flex;
  align-items: baseline;
  margin-bottom: 2px;
}

.total-label {
  font-size: 14px;
  color: #666;
  margin-right: 8px;
}

.total-price {
  color: #ff4444;
  font-weight: bold;
  font-size: 18px;
}

.discount-info {
  font-size: 12px;
  color: #999;
}

.checkout-btn {
  background: linear-gradient(135deg, #007aff 0%, #5ac8fa 100%);
  border-radius: 25px;
  //padding: 12px 24px;
  min-width: 120px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
  transition: all 0.3s ease;
}

.checkout-btn.disabled {
  background: #f5f5f5;
  color: #ccc;
  box-shadow: none;
}

.checkout-text {
  color: #fff;
  font-size: 16px;
  font-weight: 600;
  text-align: center;
}

.checkout-btn.disabled .checkout-text {
  color: #ccc;
}

/* 推荐商品样式 */
.recommend-section {
  margin-top: 30px;
  width: 100%;
}

.recommend-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 20px;
}

.recommend-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  padding: 0 20px;
}

.recommend-item {
  width: calc(50% - 7.5px);
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.recommend-image {
  width: 100%;
  height: 100px;
}

.recommend-info {
  padding: 10px;
}

.recommend-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 5px;
  display: block;
}

.recommend-price {
  font-size: 16px;
  color: #ff4444;
  font-weight: bold;
}
</style>
