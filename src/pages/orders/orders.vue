<template>
  <view class="orders-page" :style="{ height: contentHeight + 'px', paddingTop: topHeight + 'px' }">
    <view class="fixed-header">
      <navigation-bar style="width: 100%; z-index: 99" :showIcon="true" :showText="true" text="我的订单"></navigation-bar>
    </view>

    <view class="orders-content">
      <!-- 订单状态筛选 -->
      <view class="order-tabs">
        <view v-for="(tab, index) in orderTabs" :key="index" class="tab-item" :class="activeTab === index ? 'active' : ''" @click="switchTab(index)">
          <text class="tab-text">{{ tab.name }}</text>
          <view v-if="tab.count > 0" class="tab-badge">{{ tab.count }}</view>
        </view>
      </view>

      <!-- 订单列表 -->
      <scroll-view scroll-y class="order-list" refresher-enabled :refresher-triggered="refreshing" @refresherrefresh="onRefresh">
        <!-- 加载状态 -->
        <view v-if="loading" class="loading-state">
          <van-loading type="spinner" color="#007aff" />
          <text class="loading-text">加载中...</text>
        </view>

        <view v-for="(order, index) in currentOrders" :key="index" class="order-item">
          <!-- 订单头部 - 可点击区域 -->
          <view class="order-header" @click="goOrderDetail(order)">
            <view class="order-info">
              <text class="order-number">订单号：{{ order.orderNumber }}</text>
              <text class="order-date">{{ order.createTime }}</text>
            </view>
            <text class="order-status" :class="'status-' + order.orderStatus">{{ order.statusText }}</text>
          </view>

          <!-- 商品列表 - 可点击区域 -->
          <view class="product-list" @click="goOrderDetail(order)">
            <view v-for="(item, pIndex) in order.items" :key="pIndex" class="product-item">
              <image :src="item.image" class="product-image" mode="aspectFill" />
              <view class="product-info">
                <text class="product-name">{{ item.name }}</text>
                <text class="product-spec">{{ item.spec }}</text>
                <view class="product-bottom">
                  <text class="product-price">¥{{ item.price }}</text>
                  <text class="product-quantity">x{{ item.quantity }}</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 订单底部 - 不可点击区域 -->
          <view class="order-footer" @click.stop>
            <view class="order-total" @click="goOrderDetail(order)">
              <text class="total-text">共{{ order.totalQuantity }}件商品 合计：</text>
              <text class="total-price">¥{{ order.totalAmount }}</text>
            </view>
            <view class="order-actions" @click.stop>
              <!-- 待支付状态 -->
              <van-button v-if="order.orderStatus === 'pending'" size="small" type="default" @click.stop="handleButtonClick($event, 'cancel', order)"> 取消订单 </van-button>
              <van-button v-if="order.orderStatus === 'pending'" size="small" type="primary" @click.stop="handleButtonClick($event, 'pay', order)"> 立即支付 </van-button>

              <!-- 支付失败状态 -->
              <van-button v-if="order.orderStatus === 'failed'" size="small" type="default" @click.stop="handleButtonClick($event, 'cancel', order)"> 取消订单 </van-button>
              <van-button v-if="order.orderStatus === 'failed'" size="small" type="primary" @click.stop="handleButtonClick($event, 'repay', order)"> 重新支付 </van-button>

              <!-- 其他状态 -->
              <van-button v-if="order.orderStatus === 'shipped'" size="small" type="primary" @click.stop="handleButtonClick($event, 'confirm', order)"> 确认收货 </van-button>
              <van-button v-if="order.orderStatus === 'completed'" size="small" type="default" @click.stop="handleButtonClick($event, 'evaluate', order)"> 评价 </van-button>
              <van-button v-if="order.orderStatus === 'completed'" size="small" type="primary" @click.stop="handleButtonClick($event, 'buyAgain', order)"> 再次购买 </van-button>
              <!-- 退款相关按钮使用新的RefundButton组件 -->
              <refund-button v-if="order.orderStatus === 'completed' || order.orderStatus === 'paid' || order.orderStatus === 'shipped'" :order="order" :customer-id="customerId" size="small" @click.stop />
              <van-button v-if="order.orderStatus === 'refunded'" size="small" type="primary" @click.stop="handleButtonClick($event, 'buyAgain', order)"> 再次购买 </van-button>
              <van-button v-if="order.orderStatus === 'after_sale_completed'" size="small" type="primary" @click.stop="handleButtonClick($event, 'buyAgain', order)"> 再次购买 </van-button>
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <view v-if="!loading && currentOrders.length === 0" class="empty-state">
          <van-empty description="暂无订单" />
          <van-button type="primary" @click="goShopping">去逛逛</van-button>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script lang="ts">
import Vue from "vue";
import { Component } from "vue-property-decorator";
import NavigationBar from "@/components/common/NavigationBar.vue";
import RefundButton from "@/components/RefundButton.vue";
import { getOrderList, cancelOrder as cancelOrderApi, confirmOrder, payOrder, repayOrder } from "@/api/mall.api";
import { Order, OrderQueryParams } from "@/types/mall.types";
import { Code } from "@/constants/enum/code.enum";
import { checkWechatPayEnvironment } from "../../utils/wechat-pay";
import { authManager } from "../../utils/auth";
import { getAppId, getWechatLoginCode } from "@/api/auth.api";
import { getLayoutHeights } from "@/utils/get-page-height.util";
import { refundManager } from "@/utils/refund-manager";

interface Product {
  id: number;
  name: string;
  spec: string;
  price: number;
  quantity: number;
  image: string;
}

@Component({
  name: "orders",
  components: {
    NavigationBar,
    RefundButton,
  },
})
export default class Orders extends Vue {
  activeTab = 0;
  loading = false;
  refreshing = false;
  customerId = "";
  allOrders: Order[] = [];

  topHeight = 88;
  contentHeight = 0;

  orderTabs = [
    { name: "全部", count: 0 },
    { name: "待付款", count: 0 },
    { name: "待发货", count: 0 },
    { name: "待收货", count: 0 },
    { name: "已完成", count: 0 },
    { name: "退款/售后", count: 0 },
  ];

  // 获取用户ID（静默获取，不弹窗）
  async getUserId(): Promise<string | null> {
    return await authManager.getUserId(false);
  }

  // 获取微信用户openid
  getOpenid(): string {
    // 从微信用户信息中获取openid
    try {
      const wxUserInfo = uni.getStorageSync("wxUserInfo");
      if (wxUserInfo && wxUserInfo.openid) {
        console.log("订单列表页面 - 成功获取用户openid:", wxUserInfo.openid.substring(0, 8) + "...");
        return wxUserInfo.openid;
      }
      // 如果没有找到openid，提示用户重新授权
      console.warn("订单列表页面 - 未找到用户openid，请重新授权");
      uni.showToast({
        title: "请重新授权登录",
        icon: "none",
        duration: 2000,
      });
      return "";
    } catch (error) {
      console.error("订单列表页面 - 获取openid失败:", error);
      uni.showToast({
        title: "获取用户信息失败",
        icon: "none",
        duration: 2000,
      });
      return "";
    }
  }

  // 加载订单列表
  async loadOrderList() {
    if (this.customerId === "") {
      return;
    }

    this.loading = true;
    try {
      const params: OrderQueryParams = {
        customerId: this.customerId,
        pageNum: 1,
        pageSize: 100,
        // 不添加状态筛选，始终加载所有订单
      };

      const response = await getOrderList(params);
      if (response.code === Code.OK.code) {
        this.allOrders = response.data.records || [];
        this.updateTabCounts();
      } else {
        uni.showToast({
          title: response.msg || "加载订单失败",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("加载订单列表失败:", error);
      uni.showToast({
        title: "网络错误，请重试",
        icon: "none",
      });
    } finally {
      this.loading = false;
    }
  }

  get currentOrders() {
    if (this.activeTab === 0) {
      return this.allOrders;
    }
    const statusMap = ["", "pending", "paid", "shipped", "completed", "refund"];

    if (this.activeTab === 5) {
      // 退款/售后tab，显示退款中和售后处理中的订单
      return this.allOrders.filter((order) => order.orderStatus === "refund" || order.orderStatus === "refund_processing" || order.orderStatus === "refund_success" || order.orderStatus === "refund_failed");
    }

    return this.allOrders.filter((order) => order.orderStatus === statusMap[this.activeTab]);
  }

  async onLoad(options: any) {
    // 获取用户ID
    const userId = await this.getUserId();
    if (userId) {
      this.customerId = userId;

      // 如果从个人中心跳转过来带有状态参数
      if (options.status) {
        const statusIndex = ["", "pending", "paid", "shipped", "completed", "refund"].indexOf(options.status);
        if (statusIndex > 0) {
          this.activeTab = statusIndex;
        }
      }

      // 只加载一次所有订单数据
      await this.loadOrderList();
    } else {
      // 未登录，显示登录提示并跳转到用户中心
      uni.showToast({
        title: "请先登录查看订单",
        icon: "none",
      });
      setTimeout(() => {
        uni.switchTab({
          url: "/pages/user-center/user-center",
        });
      }, 1500);
    }
  }

  onShow() {
    const { topHeight, contentHeight } = getLayoutHeights();
    this.topHeight = topHeight;
    this.contentHeight = contentHeight;
  }

  switchTab(index: number) {
    this.activeTab = index;
    // 不需要重新加载数据，只需要切换显示的订单列表
  }

  updateTabCounts() {
    this.orderTabs[0].count = this.allOrders.length;
    this.orderTabs[1].count = this.allOrders.filter((order) => order.orderStatus === "pending").length;
    this.orderTabs[2].count = this.allOrders.filter((order) => order.orderStatus === "paid").length;
    this.orderTabs[3].count = this.allOrders.filter((order) => order.orderStatus === "shipped").length;
    this.orderTabs[4].count = this.allOrders.filter((order) => order.orderStatus === "completed").length;
    this.orderTabs[5].count = this.allOrders.filter((order) => order.orderStatus === "refund" || order.orderStatus === "refund_processing" || order.orderStatus === "refund_success" || order.orderStatus === "refund_failed").length;
  }

  goOrderDetail(order: Order) {
    uni.navigateTo({
      url: `/pages/orders/order-detail?id=${order.id}`,
    });
  }

  // 统一的按钮点击处理方法，防止事件穿透
  handleButtonClick(event: any, action: string, order: Order) {
    // 阻止事件冒泡和默认行为
    if (event) {
      event.stopPropagation();
      event.preventDefault();
    }

    // 防止重复点击
    if (this.loading) {
      return;
    }

    // 根据动作类型调用相应的方法
    switch (action) {
      case "cancel":
        this.cancelOrder(order);
        break;
      case "pay":
        this.goOrderDetail(order);
        break;
      case "repay":
        this.goOrderDetail(order);
        break;
      case "confirm":
        this.confirmReceive(order);
        break;
      case "evaluate":
        this.evaluateOrder(order);
        break;
      case "buyAgain":
        this.buyAgain(order);
        break;
      case "refund":
        // 已废弃，使用RefundButton组件处理
        console.warn("refund action已废弃，请使用RefundButton组件");
        break;
      case "refundDetail":
        this.viewRefundDetail(order);
        break;
      case "afterSaleDetail":
        this.viewAfterSaleDetail(order);
        break;
      default:
        console.warn("未知的按钮动作:", action);
    }
  }

  async cancelOrder(order: Order) {
    // 防止重复点击
    if (this.loading) {
      return;
    }

    uni.showModal({
      title: "取消订单",
      content: "确定要取消这个订单吗？",
      success: async (res) => {
        if (res.confirm) {
          this.loading = true;
          try {
            const response = await cancelOrderApi(order.id, this.customerId);
            if (response.code === Code.OK.code) {
              uni.showToast({ title: "订单已取消", icon: "success" });
              await this.loadOrderList(); // 重新加载订单列表
            } else {
              uni.showToast({
                title: response.msg || "取消订单失败",
                icon: "none",
              });
            }
          } catch (error) {
            console.error("取消订单失败:", error);
            uni.showToast({
              title: "网络错误，请重试",
              icon: "none",
            });
          } finally {
            this.loading = false;
          }
        }
      },
    });
  }

  async confirmReceive(order: Order) {
    uni.showModal({
      title: "确认收货",
      content: "确认已收到商品吗？",
      success: async (res) => {
        if (res.confirm) {
          try {
            const response = await confirmOrder(order.id, this.customerId);
            if (response.code === Code.OK.code) {
              uni.showToast({ title: "确认收货成功", icon: "success" });
              await this.loadOrderList(); // 重新加载订单列表
            } else {
              uni.showToast({
                title: response.msg || "确认收货失败",
                icon: "none",
              });
            }
          } catch (error) {
            console.error("确认收货失败:", error);
            uni.showToast({
              title: "网络错误，请重试",
              icon: "none",
            });
          }
        }
      },
    });
  }

  async evaluateOrder(order: Order) {
    try {
      // 检查订单是否已经评价过
      const mallExtendedApi = await import("@/api/mall-extended.api");
      const result = await mallExtendedApi.checkOrderReviewed(parseInt(order.id), parseInt(this.customerId));

      if (result.code === 0) {
        const isReviewed = result.data?.reviewed || false;
        if (isReviewed) {
          uni.showModal({
            title: "提示",
            content: "该订单已经评价过了",
            showCancel: false,
            confirmText: "知道了",
          });
          return;
        }
      }

      // 跳转到评价页面
      uni.navigateTo({
        url: `/pages/orders/order-review?id=${order.id}`,
      });
    } catch (error) {
      console.error("检查评价状态失败:", error);
      // 即使检查失败，也允许跳转到评价页面
      uni.navigateTo({
        url: `/pages/orders/order-review?id=${order.id}`,
      });
    }
  }

  buyAgain(order: Order) {
    uni.showToast({ title: "已添加到购物车", icon: "success" });
  }

  viewRefundDetail(order: Order) {
    // 使用新的退款管理器跳转到详情页面
    if (order.refundId) {
      refundManager.navigateToRefundDetail(order.refundId, this.customerId);
    } else {
      refundManager.navigateToRefundList(this.customerId);
    }
  }

  viewAfterSaleDetail(order: Order) {
    // 同样使用退款详情页面处理售后
    this.viewRefundDetail(order);
  }

  // 下拉刷新
  async onRefresh() {
    this.refreshing = true;
    try {
      await this.loadOrderList();
    } finally {
      this.refreshing = false;
    }
  }

  goShopping() {
    uni.switchTab({
      url: "/pages/index/index",
    });
  }
}
</script>

<style lang="scss" scoped>
.orders-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #fff;
}

.orders-content {
  padding-left: 15px;
  padding-right: 15px;
}

.order-tabs {
  background-color: #fff;
  display: flex;
  padding: 0 10px;
  border-bottom: 1px solid #f0f0f0;
  overflow-x: auto;
  white-space: nowrap;
  position: sticky;
  top: 88px; /* 紧贴在固定的navigation-bar下方 */
  z-index: 10;
}

.tab-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15px 3px;
  position: relative;
  min-width: 0;

  &.active {
    .tab-text {
      color: #007aff;
      font-weight: bold;
    }

    &::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 24px;
      height: 2px;
      background-color: #007aff;
    }
  }
}

.tab-text {
  font-size: 14px;
  color: #666;
  text-align: center;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.tab-badge {
  position: absolute;
  top: 8px;
  right: 1px;
  background-color: #ff4444;
  color: #fff;
  font-size: 10px;
  padding: 1px 5px;
  border-radius: 8px;
  height: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  transform: scale(0.9);
}

.order-list {
  height: calc(100vh - 200px); /* 减去头部和tab的高度 */
  padding-top: 15px;
}

.order-item {
  background-color: #fff;
  border-radius: 12px;
  margin-bottom: 15px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  position: relative;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #f5f5f5;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f8f9fa;
  }

  &:active {
    background-color: #f0f1f2;
  }
}

.order-info {
  flex: 1;
}

.order-number {
  font-size: 14px;
  color: #333;
  margin-bottom: 5px;
  display: block;
}

.order-date {
  font-size: 12px;
  color: #999;
}

.order-status {
  font-size: 14px;
  font-weight: bold;

  &.status-pending {
    color: #ff4444;
  }

  &.status-paid {
    color: #1890ff;
  }

  &.status-shipped {
    color: #52c41a;
  }

  &.status-completed {
    color: #666;
  }

  &.status-refunding {
    color: #fa8c16;
  }

  &.status-after_sale {
    color: #722ed1;
  }

  &.status-refunded {
    color: #52c41a;
  }

  &.status-after_sale_completed {
    color: #52c41a;
  }
}

.product-list {
  padding: 0 20px;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f8f9fa;
  }

  &:active {
    background-color: #f0f1f2;
  }
}

.product-item {
  display: flex;
  padding: 15px 0;
  border-bottom: 1px solid #f8f8f8;

  &:last-child {
    border-bottom: none;
  }
}

.product-image {
  width: 60px;
  height: 60px;
  border-radius: 6px;
  margin-right: 15px;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 5px;
}

.product-spec {
  font-size: 12px;
  color: #999;
  margin-bottom: 8px;
}

.product-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-price {
  font-size: 14px;
  color: #ff4444;
  font-weight: bold;
}

.product-quantity {
  font-size: 12px;
  color: #666;
}

.order-footer {
  padding: 15px 20px;
  border-top: 1px solid #f5f5f5;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
}

.order-total {
  flex: 1;
  cursor: pointer;
  transition: background-color 0.2s ease;
  padding: 5px;
  margin: -5px;
  border-radius: 4px;

  &:hover {
    background-color: #f8f9fa;
  }

  &:active {
    background-color: #f0f1f2;
  }
}

.total-text {
  font-size: 12px;
  color: #666;
}

.total-price {
  font-size: 16px;
  color: #ff4444;
  font-weight: bold;
}

.order-actions {
  display: flex;
  gap: 10px;
}

.loading-state {
  padding: 50px 20px;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.loading-text {
  font-size: 14px;
  color: #666;
}

.empty-state {
  padding: 50px 20px;
  text-align: center;

  .van-button {
    margin-top: 20px;
    width: 200px;
  }
}
</style>
