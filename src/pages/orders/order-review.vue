<template>
  <view class="review-page" :style="{ height: contentHeight  + 'px', paddingTop: topHeight + 'px' }">
    <!-- 顶部导航栏（统一自定义） -->
    <navigation-bar style="width: 100%; z-index: 99" :showIcon="true" :showText="true" text="商品评价" />

    <scroll-view scroll-y class="review-content">
      <!-- 订单信息 -->
      <view class="order-info-section">
        <view class="order-info-card">
          <view class="order-header">
            <van-icon name="orders-o" size="16" color="#007aff" />
            <text class="order-title">订单号：{{ orderInfo.orderNumber }}</text>
          </view>
        </view>
      </view>

      <!-- 分割线 -->
      <view class="divider-line"></view>

      <!-- 商品列表 -->
      <view class="products-section">
        <view class="section-header">
          <text class="section-title">评价商品</text>
        </view>
        <view v-for="(item, index) in orderItems" :key="index" class="product-item">
          <image :src="item.productImage" class="product-image" mode="aspectFill" />
          <view class="product-info">
            <text class="product-name">{{ item.productName }}</text>
            <text class="product-spec" v-if="item.productSpec">{{ item.productSpec }}</text>
            <view class="product-bottom">
              <text class="product-price">¥{{ item.price }}</text>
              <text class="product-quantity">×{{ item.quantity }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 分割线 -->
      <view class="divider-line"></view>

      <!-- 评价表单 -->
      <view class="review-form-section">
        <!-- 整体评价 -->
        <view class="form-card">
          <view class="form-header">
            <text class="header-icon">⭐</text>
            <text class="form-title">整体评价</text>
          </view>
          <view class="rating-container">
            <view class="rating-section">
              <view class="custom-rating">
                <view v-for="star in 5" :key="star" class="star-item" @click="setRating(star)">
                  <text class="star-icon" :class="{ 'star-filled': star <= reviewData.rating }">★</text>
                </view>
              </view>
              <text class="rating-text">{{ getRatingText(reviewData.rating) }}</text>
            </view>
          </view>
        </view>

        <!-- 评价标签 -->
        <view class="form-card">
          <view class="form-header">
            <text class="header-icon">🏷️</text>
            <text class="form-title">评价标签</text>
            <text class="form-subtitle">选择符合您体验的标签</text>
          </view>
          <view class="tags-container">
            <view v-for="tag in availableTags" :key="tag.id" class="tag-item" :class="{ 'tag-selected': reviewData.selectedTags.includes(tag.id) }" @click="toggleTag(tag.id)">
              <text class="tag-text">{{ tag.name }}</text>
            </view>
          </view>
        </view>

        <!-- 评价内容 -->
        <view class="form-card">
          <view class="form-header">
            <text class="header-icon">✏️</text>
            <text class="form-title">评价内容</text>
          </view>
          <view class="textarea-container">
            <textarea v-model="reviewData.content" placeholder="请分享您的使用体验，帮助其他买家更好地了解商品" maxlength="1000" class="review-textarea" @input="onContentInput" />
            <view class="word-count">{{ reviewData.content.length }}/1000</view>
          </view>
        </view>

        <!-- 上传图片 -->
        <view class="form-card">
          <view class="form-header">
            <text class="header-icon">📷</text>
            <text class="form-title">上传图片</text>
            <text class="form-subtitle">最多可上传6张图片</text>
          </view>
          <view class="image-upload-container">
            <view class="image-upload-grid">
              <view v-for="(image, index) in reviewData.images" :key="index" class="image-item">
                <image :src="image" class="uploaded-image" mode="aspectFill" @click="previewImage(index)" />
                <view class="image-delete" @click="removeImage(index)">
                  <text class="delete-icon">×</text>
                </view>
              </view>
              <view v-if="reviewData.images.length < 6" class="upload-btn" @click="chooseImage">
                <text class="plus-icon">+</text>
                <text class="upload-text">添加图片</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 匿名评价选项 -->
        <view class="form-card">
          <view class="anonymous-option" @click="toggleAnonymous">
            <view class="anonymous-left">
              <text class="header-icon">👤</text>
              <view class="anonymous-info">
                <text class="anonymous-title">匿名评价</text>
                <text class="anonymous-desc">开启后其他用户将看不到您的用户名</text>
              </view>
            </view>
            <view class="custom-switch" :class="{ 'switch-on': reviewData.anonymous }" @click.stop="toggleAnonymous">
              <view class="switch-handle"></view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 底部提交按钮 -->
    <view class="submit-footer">
      <view class="submit-container">
        <button class="submit-btn" :class="{ 'btn-disabled': !canSubmit, 'btn-loading': submitting }" :disabled="!canSubmit || submitting" @click="submitReview">
          {{ submitButtonText }}
        </button>
      </view>
    </view>
  </view>
</template>

<script lang="ts">
import Vue from "vue";
import { Component } from "vue-property-decorator";
import NavigationBar from "@/components/common/NavigationBar.vue";
import { authManager } from "@/utils/auth";
import { Code } from "@/constants/enum/code.enum";
import {getLayoutHeights} from "@/utils/get-page-height.util";

interface OrderInfo {
  id: string;
  orderNumber: string;
  createdAt: string;
}

interface OrderItem {
  id: string;
  productId: string;
  productName: string;
  productImage: string;
  productSpec: string;
  price: number;
  quantity: number;
}

interface ReviewData {
  rating: number;
  content: string;
  images: string[];
  anonymous: boolean;
  selectedTags: string[];
}

interface ReviewTag {
  id: string;
  name: string;
  type: string;
}

@Component({
  components: {
    NavigationBar,
  },
})
export default class OrderReview extends Vue {
  orderId = "";
  customerId = "";
  loading = false;
  submitting = false;

  topHeight = 88;
  contentHeight = 0;

  orderInfo: OrderInfo = {
    id: "",
    orderNumber: "",
    createdAt: "",
  };

  orderItems: OrderItem[] = [];

  reviewData: ReviewData = {
    rating: 5,
    content: "",
    images: [],
    anonymous: false,
    selectedTags: [],
  };

  // 可选的评价标签
  availableTags: ReviewTag[] = [];

  async onLoad(options: any) {
    this.orderId = options.id || "";
    if (!this.orderId) {
      uni.showToast({
        title: "订单ID不能为空",
        icon: "none",
      });
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
      return;
    }

    // 获取用户ID
    const userId = await this.getUserId();
    if (userId) {
      this.customerId = userId;
      await this.loadOrderInfo();
      await this.loadReviewTags();
    } else {
      uni.showToast({
        title: "请先登录",
        icon: "none",
      });
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    }
  }

  onShow() {
    const { topHeight, contentHeight } = getLayoutHeights();
    this.topHeight = topHeight;
    this.contentHeight = contentHeight;
  }

  async getUserId(): Promise<string | null> {
    return await authManager.getUserId(true);
  }

  // 加载评价标签
  async loadReviewTags() {
    try {
      const mallExtendedApi = await import("@/api/mall-extended.api");
      const result = await mallExtendedApi.getReviewTags();

      if (result.code === 0) {
        this.availableTags = result.data || [];
        console.log("评价标签加载成功:", this.availableTags);
      } else {
        console.error("获取评价标签失败:", result.msg);
        // 使用默认标签
        this.availableTags = [
          { id: "1", name: "质量很好", type: "positive" },
          { id: "2", name: "物流快速", type: "positive" },
          { id: "3", name: "包装精美", type: "positive" },
          { id: "4", name: "性价比高", type: "positive" },
          { id: "5", name: "服务态度好", type: "positive" },
          { id: "6", name: "商品描述准确", type: "positive" },
        ];
      }
    } catch (error) {
      console.error("加载评价标签异常:", error);
      // 使用默认标签
      this.availableTags = [
        { id: "1", name: "质量很好", type: "positive" },
        { id: "2", name: "物流快速", type: "positive" },
        { id: "3", name: "包装精美", type: "positive" },
        { id: "4", name: "性价比高", type: "positive" },
        { id: "5", name: "服务态度好", type: "positive" },
        { id: "6", name: "商品描述准确", type: "positive" },
      ];
    }
  }

  // 加载订单信息
  async loadOrderInfo() {
    try {
      this.loading = true;
      const mallApi = await import("@/api/mall.api");
      const result = await mallApi.getOrderDetail(this.orderId, this.customerId);

      console.log("评价页面 - 订单详情API响应:", result);

      if (result.code === Code.OK.code) {
        const order = result.data;
        this.orderInfo = {
          id: order.id,
          orderNumber: order.orderNumber,
          createdAt: this.formatDate(order.createdAt),
        };

        this.orderItems = order.orderItems || [];
        console.log("评价页面 - 订单信息加载成功:", this.orderInfo);
      } else {
        throw new Error(result.msg || "获取订单信息失败");
      }
    } catch (error) {
      console.error("评价页面 - 加载订单信息失败:", error);
      uni.showToast({
        title: "加载订单信息失败",
        icon: "none",
      });
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    } finally {
      this.loading = false;
    }
  }

  // 设置评分
  setRating(rating: number) {
    this.reviewData.rating = rating;
    console.log("评分变化:", rating);
  }

  // 评分变化处理
  onRatingChange(value: number) {
    console.log("评分变化:", value);
  }

  // 内容输入处理
  onContentInput(e: any) {
    this.reviewData.content = e.detail.value;
  }

  // 获取评分文本
  getRatingText(rating: number): string {
    if (rating >= 4.5) return "非常满意";
    if (rating >= 3.5) return "满意";
    if (rating >= 2.5) return "一般";
    if (rating >= 1.5) return "不满意";
    return "非常不满意";
  }

  // 选择图片
  async chooseImage() {
    const remainingCount = 6 - this.reviewData.images.length;
    if (remainingCount <= 0) {
      uni.showToast({
        title: "最多只能上传6张图片",
        icon: "none",
      });
      return;
    }

    uni.chooseImage({
      count: remainingCount,
      sizeType: ["compressed"],
      sourceType: ["album", "camera"],
      success: async (res) => {
        // 显示上传进度
        uni.showLoading({
          title: "正在上传图片...",
          mask: true,
        });

        try {
          const validImages: string[] = [];
          let oversizedCount = 0;
          let uploadedCount = 0;

          for (const path of res.tempFilePaths) {
            try {
              // 获取图片信息
              const info = await this.getImageInfo(path);

              // 检查文件大小（限制为5MB）
              const maxSize = 5 * 1024 * 1024; // 5MB
              if (info.size && info.size > maxSize) {
                oversizedCount++;
                continue;
              }

              // 立即上传图片到服务器
              const uploadedUrl = await this.uploadSingleImage(path);
              if (uploadedUrl && uploadedUrl.startsWith("http")) {
                validImages.push(uploadedUrl);
                uploadedCount++;
              }
            } catch (error) {
              console.error("处理图片失败:", error);
            }
          }

          uni.hideLoading();

          // 添加成功上传的图片
          this.reviewData.images.push(...validImages);

          // 显示结果提示
          if (uploadedCount > 0) {
            uni.showToast({
              title: `成功上传${uploadedCount}张图片`,
              icon: "success",
            });
          }

          if (oversizedCount > 0) {
            uni.showToast({
              title: `${oversizedCount}张图片超过5MB限制`,
              icon: "none",
            });
          }
        } catch (error) {
          uni.hideLoading();
          console.error("上传图片失败:", error);
          uni.showToast({
            title: "上传图片失败",
            icon: "none",
          });
        }
      },
      fail: (error) => {
        console.error("选择图片失败:", error);
        uni.showToast({
          title: "选择图片失败",
          icon: "none",
        });
      },
    });
  }

  // 获取图片信息的Promise封装
  getImageInfo(src: string): Promise<any> {
    return new Promise((resolve, reject) => {
      uni.getImageInfo({
        src,
        success: resolve,
        fail: reject,
      });
    });
  }

  // 删除图片
  removeImage(index: number) {
    uni.showModal({
      title: "确认删除",
      content: "确定要删除这张图片吗？",
      success: (res) => {
        if (res.confirm) {
          this.reviewData.images.splice(index, 1);
        }
      },
    });
  }

  // 预览图片
  previewImage(index: number) {
    uni.previewImage({
      current: index,
      urls: this.reviewData.images,
    });
  }

  // 切换评价标签
  toggleTag(tagId: string) {
    const index = this.reviewData.selectedTags.indexOf(tagId);
    if (index > -1) {
      // 如果已选中，则取消选中
      this.reviewData.selectedTags.splice(index, 1);
    } else {
      // 如果未选中，则添加选中（最多选择5个标签）
      if (this.reviewData.selectedTags.length < 5) {
        this.reviewData.selectedTags.push(tagId);
      } else {
        uni.showToast({
          title: "最多只能选择5个标签",
          icon: "none",
        });
      }
    }
  }

  // 切换匿名评价
  toggleAnonymous() {
    this.reviewData.anonymous = !this.reviewData.anonymous;
  }

  // 匿名评价开关变化
  onAnonymousChange(value: boolean) {
    console.log("匿名评价状态变化:", value);
    this.reviewData.anonymous = value;
  }

  // 检查是否可以提交
  get canSubmit(): boolean {
    return this.reviewData.rating > 0 && this.reviewData.content.trim().length >= 10 && this.reviewData.content.trim().length <= 1000;
  }

  // 获取提交按钮文本
  get submitButtonText(): string {
    if (this.submitting) {
      return "提交中...";
    }
    if (this.reviewData.content.trim().length < 10) {
      return "评价内容至少10个字";
    }
    if (this.reviewData.content.trim().length > 1000) {
      return "评价内容不能超过1000字";
    }
    if (this.reviewData.rating === 0) {
      return "请选择评分";
    }
    return "提交评价";
  }

  // 提交评价
  async submitReview() {
    if (!this.canSubmit) {
      let message = "请完善评价信息";
      if (this.reviewData.rating === 0) {
        message = "请选择评分";
      } else if (this.reviewData.content.trim().length < 10) {
        message = "评价内容至少需要10个字";
      } else if (this.reviewData.content.trim().length > 1000) {
        message = "评价内容不能超过1000字";
      }

      uni.showToast({
        title: message,
        icon: "none",
      });
      return;
    }

    try {
      this.submitting = true;

      // 图片已经在选择时上传，直接使用
      const imageUrls = this.reviewData.images;

      // 提交评价
      uni.showLoading({
        title: "正在提交评价...",
        mask: true,
      });

      const mallExtendedApi = await import("@/api/mall-extended.api");
      const result = await mallExtendedApi.submitOrderReview({
        orderId: this.orderId,
        customerId: this.customerId,
        rating: this.reviewData.rating,
        content: this.reviewData.content.trim(),
        images: imageUrls,
        anonymous: this.reviewData.anonymous,
        tagIds: this.reviewData.selectedTags,
      });

      uni.hideLoading();
      console.log("评价页面 - 提交评价API响应:", result);

      if (result.code === 0) {
        uni.showToast({
          title: "评价提交成功",
          icon: "success",
        });
        // 立即返回，不等待toast显示完成
        uni.navigateBack();
      } else {
        throw new Error(result.msg || "提交评价失败");
      }
    } catch (error) {
      uni.hideLoading();
      console.error("评价页面 - 提交评价失败:", error);
      uni.showToast({
        title: error.message || "提交评价失败",
        icon: "none",
      });
    } finally {
      this.submitting = false;
    }
  }

  // 上传单张图片
  async uploadSingleImage(filePath: string): Promise<string> {
    return new Promise((resolve, reject) => {
      // 获取token
      const tokenObj = uni.getStorageSync("token");
      const token = tokenObj?.value || "";

      // 构建上传URL
      const businessBaseURL = (process.env.VUE_APP_ENABLE_GATEWAY === "on" ? process.env.VUE_APP_MS_GATEWAY_API + process.env.VUE_APP_BUSINESS_API : process.env.VUE_APP_BUSINESS_API) + process.env.VUE_APP_API_PREFIX;
      const uploadUrl = `${businessBaseURL}/video_upload/upload`;

      uni.uploadFile({
        url: uploadUrl,
        filePath: filePath,
        name: "file",
        formData: {
          path: "review-images",
        },
        header: {
          Token: token,
        },
        success: (res) => {
          console.log("图片上传响应:", res);
          try {
            const result = JSON.parse(res.data);
            if (result.code === 0 && result.data) {
              console.log("图片上传成功:", result.data);
              resolve(result.data);
            } else {
              console.error("图片上传失败:", result.msg);
              resolve(filePath); // 上传失败时返回原路径
            }
          } catch (error) {
            console.error("解析上传响应失败:", error);
            resolve(filePath); // 解析失败时返回原路径
          }
        },
        fail: (error) => {
          console.error("图片上传请求失败:", error);
          resolve(filePath); // 请求失败时返回原路径
        },
      });
    });
  }

  // 格式化日期
  formatDate(dateStr: string): string {
    if (!dateStr) return "";
    const date = new Date(dateStr);
    return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, "0")}-${date.getDate().toString().padStart(2, "0")} ${date.getHours().toString().padStart(2, "0")}:${date.getMinutes().toString().padStart(2, "0")}`;
  }

  // 页面卸载时的清理
  onUnload() {
    // 清理可能的loading状态
    uni.hideLoading();
  }

  // 页面隐藏时的处理
  onHide() {
    // 清理可能的loading状态
    uni.hideLoading();
  }
}
</script>

<style lang="scss" scoped>
.review-page {
  background-color: #f7f8fa;
  display: flex;
  flex-direction: column;
}

.review-content {
  flex: 1;
  padding-bottom: 120px; /* 为底部按钮留出更多空间 */
}

/* 订单信息区域 */
.order-info-section {
  padding: 12px 16px;
}

.order-info-card {
  background-color: #fff;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.order-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.order-title {
  font-size: 16px;
  color: #333;
  font-weight: 600;
  margin-left: 8px;
}

.order-date {
  font-size: 14px;
  color: #666;
  margin-left: 24px;
}

/* 分割线 */
.divider-line {
  height: 8px;
  background-color: #f7f8fa;
}

/* 商品区域 */
.products-section {
  padding: 0 16px;
}

.section-header {
  padding: 16px 0 12px;
}

.section-title {
  font-size: 16px;
  color: #333;
  font-weight: 600;
}

.product-item {
  background-color: #fff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  display: flex;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.product-image {
  width: 70px;
  height: 70px;
  border-radius: 8px;
  margin-right: 12px;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-name {
  font-size: 15px;
  color: #333;
  font-weight: 500;
  line-height: 1.4;
  margin-bottom: 4px;
}

.product-spec {
  font-size: 12px;
  color: #999;
  margin-bottom: 8px;
}

.product-bottom {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.product-price {
  font-size: 16px;
  color: #ff4444;
  font-weight: 600;
}

.product-quantity {
  font-size: 14px;
  color: #666;
}

/* 评价表单区域 */
.review-form-section {
  padding: 0 16px;
}

.form-card {
  background-color: #fff;
  border-radius: 12px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  overflow: hidden;
}

.form-header {
  padding: 16px 16px 12px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #f5f5f5;
}

.header-icon {
  font-size: 16px;
  margin-right: 8px;
}

.form-title {
  font-size: 16px;
  color: #333;
  font-weight: 600;
  flex: 1;
}

.form-subtitle {
  font-size: 12px;
  color: #999;
  margin-left: auto;
}

/* 评分区域 */
.rating-container {
  padding: 16px;
}

.rating-section {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 12px;
}

.rating-text {
  font-size: 16px;
  color: #007aff;
  font-weight: 500;
}

/* 自定义评分组件 */
.custom-rating {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-bottom: 16px;
}

.star-item {
  cursor: pointer;
  transition: transform 0.2s ease;
  padding: 4px;
}

.star-item:active {
  transform: scale(1.2);
}

.star-icon {
  font-size: 36px;
  color: #e5e5e5;
  transition: all 0.3s ease;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.star-filled {
  color: #ffd700;
  text-shadow: 0 1px 3px rgba(255, 215, 0, 0.3);
}

/* 评价标签 */
.tags-container {
  padding: 16px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-item {
  padding: 8px 16px;
  border-radius: 20px;
  border: 1px solid #e5e5e5;
  background-color: #fff;
  transition: all 0.3s ease;
}

.tag-selected {
  background-color: #007aff;
  border-color: #007aff;
}

.tag-text {
  font-size: 14px;
  color: #666;
}

.tag-selected .tag-text {
  color: #fff;
}

/* 文本输入区域 */
.textarea-container {
  padding: 0 16px 16px;
  position: relative;
}

.review-textarea {
  width: 100%;
  min-height: 100px;
  padding: 12px;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  font-size: 14px;
  line-height: 1.5;
  resize: none;
  background-color: #fafafa;
  box-sizing: border-box;
}

.review-textarea:focus {
  border-color: #007aff;
  background-color: #fff;
  outline: none;
}

.word-count {
  position: absolute;
  bottom: 24px;
  right: 28px;
  font-size: 12px;
  color: #999;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 2px 4px;
  border-radius: 4px;
}

/* 图片上传区域 */
.image-upload-container {
  padding: 16px;
}

.image-upload-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.image-item {
  position: relative;
  width: 80px;
  height: 80px;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  border-radius: 8px;
}

.image-delete {
  position: absolute;
  top: -6px;
  right: -6px;
  width: 22px;
  height: 22px;
  background-color: #ff4444;
  border-radius: 11px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid #fff;
}

.delete-icon {
  color: #fff;
  font-size: 14px;
  font-weight: bold;
}

.upload-btn {
  width: 80px;
  height: 80px;
  border: 1px dashed #ddd;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
  background-color: #fafafa;
}

.plus-icon {
  font-size: 24px;
  color: #999;
}

.upload-text {
  font-size: 12px;
  color: #999;
}

/* 匿名评价选项 */
.anonymous-option {
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.anonymous-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.anonymous-info {
  margin-left: 8px;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.anonymous-title {
  font-size: 15px;
  color: #333;
  font-weight: 500;
}

.anonymous-desc {
  font-size: 12px;
  color: #999;
}

/* 自定义开关 */
.custom-switch {
  width: 44px;
  height: 24px;
  background-color: #e5e5e5;
  border-radius: 12px;
  position: relative;
  transition: background-color 0.3s ease;
  cursor: pointer;
}

.switch-on {
  background-color: #007aff;
}

.switch-handle {
  width: 20px;
  height: 20px;
  background-color: #fff;
  border-radius: 10px;
  position: absolute;
  top: 2px;
  left: 2px;
  transition: transform 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.switch-on .switch-handle {
  transform: translateX(20px);
}

/* 底部提交按钮 */
.submit-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-top: 1px solid #eee;
  z-index: 100;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.06);
}

.submit-container {
  padding: 12px 16px calc(12px + env(safe-area-inset-bottom));
}

.submit-btn {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(135deg, #007aff 0%, #5ac8fa 100%);
  border: none;
  border-radius: 24px;
  color: #fff;
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
  transition: all 0.3s ease;
}

.submit-btn:active {
  transform: translateY(1px);
  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
}

.btn-disabled {
  background: #e5e5e5 !important;
  color: #999 !important;
  box-shadow: none !important;
}

.btn-loading {
  opacity: 0.7;
}

/* iOS <= 11 的兼容写法 */
@supports (padding-bottom: constant(safe-area-inset-bottom)) {
  .submit-container {
    padding-bottom: calc(12px + constant(safe-area-inset-bottom));
  }
}
</style>
