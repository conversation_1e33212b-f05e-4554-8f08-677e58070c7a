<template>
  <view class="order-confirm-page" :style="{ height: contentHeight + 'px', paddingTop: topHeight + 'px' }">
    <view class="fixed-header">
      <!-- 顶部导航栏（统一自定义） -->
      <navigation-bar style="width: 100%; z-index: 99" :showIcon="true" :showText="true" text="订单确认" />
    </view>
    <scroll-view scroll-y class="confirm-content">
      <!-- 收货地址选择 -->
      <view class="delivery-section">
        <view class="address-section">
          <view class="address-card" v-if="selectedAddress" @click="showAddressPicker = true">
            <view class="address-info">
              <view class="address-header">
                <text class="receiver-name">{{ selectedAddress.name }}</text>
                <text class="receiver-phone">{{ selectedAddress.phone }}</text>
                <view v-if="selectedAddress.isDefault" class="default-tag">默认</view>
              </view>
              <text class="address-detail">{{ selectedAddress.fullAddress || selectedAddress.address }}</text>
            </view>
            <van-icon name="arrow" size="16" color="#999" />
          </view>
          <view v-else class="address-prompt" @click="showAddressPicker = true">
            <van-icon name="add-o" size="20" color="#007aff" />
            <text class="prompt-text">请选择收货地址</text>
            <van-icon name="arrow" size="16" color="#999" />
          </view>
        </view>
      </view>

      <!-- 分割线 -->
      <view class="divider-line"></view>

      <!-- 商品列表 -->
      <view class="products-section">
        <view v-for="(product, index) in orderProducts" :key="index" class="product-item">
          <image :src="product.image" class="product-image" mode="aspectFill" />
          <view class="product-info">
            <text class="product-name">{{ product.name }}</text>
            <text class="product-spec" v-if="product.spec">{{ product.spec }}</text>
            <view class="product-bottom">
              <text class="product-price">¥{{ product.price }}</text>
              <text class="product-original-price" v-if="product.originalPrice">¥{{ product.originalPrice }}</text>
              <text class="product-quantity">x{{ product.quantity }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 备注信息 -->
      <view class="remark-section">
        <view class="remark-header">
          <van-icon name="edit" size="16" color="#666" />
          <text class="remark-title">订单备注</text>
        </view>
        <textarea v-model="orderRemark" class="remark-input" placeholder="选填，请输入您要备注的信息" maxlength="200" :show-count="true" />
      </view>

      <!-- 支付方式选择 -->
      <view class="payment-section">
        <view class="payment-header">
          <van-icon name="gold-coin-o" size="16" color="#666" />
          <text class="payment-title">支付方式</text>
        </view>
        <view class="payment-options">
          <view class="payment-option" :class="{ active: selectedPayment === 'wechat' }" @click="selectPayment('wechat')">
            <view class="payment-info">
              <van-icon name="wechat" size="24" color="#07c160" />
              <text class="payment-name">微信支付</text>
            </view>
            <van-icon :name="selectedPayment === 'wechat' ? 'success' : 'circle'" size="20" :color="selectedPayment === 'wechat' ? '#007aff' : '#ddd'" />
          </view>
          <!--          <view class="payment-option" :class="{ active: selectedPayment === 'wallet' }" @click="selectPayment('wallet')">-->
          <!--            <view class="payment-info">-->
          <!--              <van-icon name="balance-o" size="24" color="#ff9500" />-->
          <!--              <view class="payment-details">-->
          <!--                <text class="payment-name">钱包支付</text>-->
          <!--                <text class="wallet-balance">余额：¥{{ walletBalance }}</text>-->
          <!--              </view>-->
          <!--            </view>-->
          <!--            <van-icon :name="selectedPayment === 'wallet' ? 'success' : 'circle'" size="20" :color="selectedPayment === 'wallet' ? '#007aff' : '#ddd'" />-->
          <!--          </view>-->
        </view>
      </view>

      <!-- 底部合计 -->
      <view class="total-section">
        <text class="total-text">合计：¥{{ totalAmount }}</text>
      </view>
    </scroll-view>

    <!-- 底部操作栏 -->
    <view class="confirm-footer">
      <view class="footer-left">
        <view class="payment-info-footer">
          <text class="payment-method">{{ selectedPayment === "wechat" ? "微信支付" : "钱包支付" }}</text>
          <view class="total-line">
            <text class="total-label">合计:</text>
            <text class="total-price">¥{{ totalAmount }}</text>
          </view>
        </view>
      </view>
      <view class="submit-btn" :class="{ disabled: !selectedAddress || !selectedPayment }" @click="submitOrder">
        <text class="submit-text">立即支付</text>
      </view>
    </view>

    <!-- 地址选择弹窗 -->
    <van-popup :show="showAddressPicker" position="bottom" round @close="showAddressPicker = false">
      <view class="address-picker">
        <view class="picker-header">
          <text class="picker-title">选择收货地址</text>
          <van-icon name="cross" size="20" @click="showAddressPicker = false" />
        </view>
        <view class="address-list">
          <view v-for="(address, index) in addresses" :key="index" class="address-item" :class="{ active: selectedAddress && selectedAddress.id === address.id }" @click="selectAddress(address)">
            <view class="address-content">
              <view class="address-header">
                <text class="receiver-name">{{ address.name }}</text>
                <text class="receiver-phone">{{ address.phone }}</text>
                <text v-if="address.isDefault" class="default-tag">默认</text>
              </view>
              <text class="address-detail">{{ address.fullAddress || address.address }}</text>
            </view>
            <van-icon v-if="selectedAddress && selectedAddress.id === address.id" name="success" size="16" color="#007aff" />
          </view>
        </view>
        <view class="address-actions">
          <van-button type="primary" block @click="addNewAddress">添加新地址</van-button>
        </view>
      </view>
    </van-popup>
  </view>
</template>

<script lang="ts">
import Vue from "vue";
import { Component } from "vue-property-decorator";
import NavigationBar from "@/components/common/NavigationBar.vue";
import { submitOrderFromCart, getDefaultAddress, getAddressList, payOrder } from "@/api/mall.api";
import { Code } from "@/constants/enum/code.enum";
import { getAppId, getWechatLoginCode } from "@/api/auth.api";
import { checkWechatPayEnvironment } from "@/utils/wechat-pay";
import {getLayoutHeights} from "@/utils/get-page-height.util";

interface OrderProduct {
  id: number;
  name: string;
  price: number;
  originalPrice?: number;
  quantity: number;
  image: string;
  spec?: string;
}

@Component({
  name: "order-confirm",
  components: { NavigationBar },
})
export default class OrderConfirm extends Vue {
  topHeight = 88;
  contentHeight = 0;
  showAddressPicker = false;
  selectedAddress: any = null;
  selectedDeliveryOption = 0;
  orderRemark = "";
  selectedPayment = "wechat";
  walletBalance = 0.0;
  requestId = ""; // 页面级别的 requestId，只生成一次
  cartOrderData: any = null; // 从购物车传递过来的订单数据
  fromBuyNow = false; // 标识是否来自立即购买

  orderProducts: OrderProduct[] = [];

  addresses = [];

  get totalAmount() {
    let total = this.orderProducts.reduce((sum, product) => sum + product.price * product.quantity, 0);

    return total.toFixed(2);
  }

  // 生成请求ID
  generateRequestId(): string {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 10000);
    return `req_${timestamp}_${random}`;
  }

  async onLoad(options: any) {
    // 生成请求ID，页面级别只生成一次
    this.requestId = this.generateRequestId();
    console.log("订单确认页面 - 生成请求ID:", this.requestId);

    // 检查是否是立即购买
    if (options.from === "buyNow") {
      console.log("订单确认页面 - 来自立即购买");
      await this.handleBuyNowData(options);
    } else if (options.orderData) {
      // 解析购物车传递的订单数据
      try {
        this.cartOrderData = JSON.parse(decodeURIComponent(options.orderData));
        console.log("订单确认页面 - 接收到购物车数据:", this.cartOrderData);

        // 处理商品数据
        if (this.cartOrderData.cartItems && this.cartOrderData.cartItems.length > 0) {
          this.orderProducts = this.cartOrderData.cartItems.map((item: any, index: number) => ({
            id: item.productId || item.id || index + 1,
            name: item.name,
            price: parseFloat(item.price),
            originalPrice: item.originalPrice ? parseFloat(item.originalPrice) : undefined,
            quantity: parseInt(item.quantity || 1),
            image: item.image || "/static/images/test.jpeg",
          }));
        }

        // 处理地址数据
        if (this.cartOrderData.defaultAddress) {
          const cartAddress = {
            id: this.cartOrderData.defaultAddress.id,
            name: this.cartOrderData.defaultAddress.name,
            phone: this.cartOrderData.defaultAddress.phone,
            fullAddress: this.cartOrderData.defaultAddress.fullAddress,
            isDefault: true,
          };

          this.selectedAddress = cartAddress;

          // 将购物车地址添加到地址列表中（如果不存在）
          const existingAddress = this.addresses.find((addr) => addr.id === cartAddress.id);
          if (!existingAddress) {
            this.addresses.unshift(cartAddress);
          }
        } else {
          // 没有默认地址，尝试从API获取
          await this.loadDefaultAddress();
        }
      } catch (e) {
        console.error("解析购物车订单数据失败:", e);
        uni.showToast({
          title: "订单数据解析失败",
          icon: "none",
        });
      }
    } else {
      // 兼容旧的数据格式
      if (options.data) {
        try {
          const data = JSON.parse(decodeURIComponent(options.data));
          if (Array.isArray(data.items) && data.items.length > 0) {
            this.orderProducts = data.items.map((item: any, index: number) => ({
              id: item.id || index + 1,
              name: item.name,
              price: parseFloat(item.price),
              originalPrice: item.originalPrice ? parseFloat(item.originalPrice) : undefined,
              quantity: parseInt(item.quantity || 1),
              image: item.image || "/static/images/test.jpeg",
            }));
          }
        } catch (e) {
          console.warn("解析订单数据失败:", e);
        }
      }

      // 加载默认地址
      await this.loadDefaultAddress();
    }

    // 加载地址列表用于地址选择弹窗
    await this.loadAddressList();
  }

  // 处理立即购买的数据
  async handleBuyNowData(options: any) {
    try {
      // 设置立即购买标识
      this.fromBuyNow = true;

      // 从 URL 参数中获取商品信息
      const productId = options.productId;
      const productName = options.productName;
      const productPrice = options.productPrice;
      const productQuantity = options.productQuantity || 1;
      const productImage = options.productImage;
      const productSpec = options.productSpec || "";

      console.log("订单确认页面 - 立即购买商品信息:", {
        productId,
        productName,
        productPrice,
        productQuantity,
        productImage,
        productSpec,
      });

      // 验证必要的商品信息
      if (!productId || !productName || !productPrice) {
        console.error("订单确认页面 - 立即购买商品信息不完整");
        uni.showToast({
          title: "商品信息异常",
          icon: "none",
        });
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
        return;
      }

      // 构造商品数据
      this.orderProducts = [
        {
          id: parseInt(productId),
          name: decodeURIComponent(productName),
          price: parseFloat(productPrice),
          quantity: parseInt(productQuantity),
          image: productImage ? decodeURIComponent(productImage) : "/static/images/test.jpeg",
          spec: productSpec ? decodeURIComponent(productSpec) : "",
        },
      ];

      console.log("订单确认页面 - 构造的商品数据:", this.orderProducts);

      // 加载默认地址
      await this.loadDefaultAddress();
    } catch (error) {
      console.error("订单确认页面 - 处理立即购买数据失败:", error);
      uni.showToast({
        title: "商品数据处理失败",
        icon: "none",
      });
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    }
  }

  // 页面显示时刷新地址信息
  onShow() {
    const { topHeight, contentHeight } = getLayoutHeights();
    this.topHeight = topHeight;
    this.contentHeight = contentHeight;
    // 如果当前没有选中地址，尝试重新加载默认地址
    if (!this.selectedAddress) {
      this.loadDefaultAddress();
    }
    // 刷新地址列表
    this.loadAddressList();
  }

  // 页面销毁时清除 requestId
  onUnload() {
    console.log("订单确认页面 - 页面销毁，清除请求ID:", this.requestId);
    this.requestId = "";
  }

  mounted() {
    // 监听地址选择事件
    uni.$on("addressSelected", (address: any) => {
      this.selectedAddress = address;
      // 重新加载地址列表以确保数据同步
      this.loadAddressList();
    });
  }

  beforeDestroy() {
    // 移除事件监听
    uni.$off("addressSelected");
  }

  goBack() {
    uni.navigateBack();
  }

  selectAddress(address: any) {
    this.selectedAddress = address;
    this.showAddressPicker = false;
  }

  selectPayment(paymentType: string) {
    this.selectedPayment = paymentType;
  }

  addNewAddress() {
    this.showAddressPicker = false;
    uni.navigateTo({
      url: "/pages/address/address?select=1",
    });
  }

  goAddAddress() {
    uni.navigateTo({
      url: "/pages/address/address?select=1",
    });
  }

  async submitOrder() {
    if (!this.selectedAddress) {
      uni.showToast({ title: "请选择收货地址", icon: "none" });
      return;
    }

    if (!this.selectedPayment) {
      uni.showToast({ title: "请选择支付方式", icon: "none" });
      return;
    }

    // 检查钱包余额
    if (this.selectedPayment === "wallet") {
      const totalAmount = parseFloat(this.totalAmount);
      if (totalAmount > this.walletBalance) {
        uni.showModal({
          title: "余额不足",
          content: `当前余额¥${this.walletBalance}，订单金额¥${this.totalAmount}，请选择其他支付方式或充值后再试。`,
          showCancel: false,
        });
        return;
      }
    }

    try {
      uni.showLoading({ title: "提交订单中..." });

      // 检查 requestId 是否存在
      if (!this.requestId) {
        console.error("订单确认页面 - requestId 为空，无法提交订单");
        uni.hideLoading();
        uni.showToast({
          title: "页面状态异常，请重新进入",
          icon: "none",
        });
        return;
      }

      let orderRequest: any;
      // 获取微信code
      const vxCodeRes = await getWechatLoginCode();
      // 检查是否是立即购买
      if (this.fromBuyNow) {
        // 立即购买：构造订单数据
        const customerId = this.getUserId();
        const appId = getAppId();
        if (!customerId) {
          uni.hideLoading();
          uni.showToast({
            title: "请先登录",
            icon: "none",
          });
          return;
        }

        if (!appId || !customerId) {
          uni.hideLoading();
          uni.showToast({
            title: "用户信息异常，请重新登录",
            icon: "none",
          });
          return;
        }
        orderRequest = {
          customerId: customerId, // 保持字符串格式，避免精度丢失
          addressId: this.selectedAddress.id,
          paymentMethod: this.selectedPayment === "wechat" ? "WECHAT" : this.selectedPayment.toUpperCase(),
          deliveryFee: 0.0, // 免配送费
          appId: appId,
          code: vxCodeRes.code,
          requestId: this.requestId,
          cartItemIds: [], // 立即购买没有购物车项ID
          cartItems: this.orderProducts.map((product) => ({
            productId: product.id,
            name: product.name,
            spec: product.spec || "",
            price: product.price,
            quantity: product.quantity,
            image: product.image,
          })),
        };

        console.log("订单确认页面 - 立即购买订单数据:", orderRequest);
      } else {
        // 购物车：使用原有逻辑
        orderRequest = {
          code: vxCodeRes.code,
          ...this.cartOrderData,
          addressId: this.selectedAddress.id,
          paymentMethod: this.selectedPayment === "wechat" ? "WECHAT" : this.selectedPayment.toUpperCase(),
          requestId: this.requestId, // 使用页面级别的请求ID
        };

        console.log("订单确认页面 - 购物车原始数据:", this.cartOrderData);
        console.log("订单确认页面 - 提交购物车订单数据:", orderRequest);
      }

      console.log("订单确认页面 - 关键字段检查:", {
        appId: orderRequest.appId,
        code: orderRequest.code,
        customerId: orderRequest.customerId,
        addressId: orderRequest.addressId,
        paymentMethod: orderRequest.paymentMethod,
        requestId: orderRequest.requestId,
      });

      // 调用购物车提交订单API
      const response = await submitOrderFromCart(orderRequest);
      uni.hideLoading();

      if (response && response.code === 0 && response.data) {
        const orderResult = response.data;
        console.log("订单确认页面 - 购物车订单提交成功，订单信息:", orderResult);
        console.log("订单确认页面 - 支付参数详情:", orderResult.paymentParams);

        // 如果选择微信支付，发起支付
        if (this.selectedPayment === "wechat") {
          console.log("订单确认页面 - 开始处理微信支付");
          await this.handleWechatPayment(orderResult);
        } else {
          // 其他支付方式或跳转到订单列表
          console.log("订单确认页面 - 非微信支付，跳转到订单列表");
          this.navigateToOrderList();
        }
      } else {
        console.error("订单确认页面 - 订单提交失败:", response);
        uni.showToast({
          title: response?.msg || "订单提交失败",
          icon: "none",
        });
      }
    } catch (error) {
      uni.hideLoading();
      console.error("创建订单失败:", error);
      uni.showToast({
        title: "网络错误，请重试",
        icon: "none",
      });
    }
  }

  // 处理微信支付
  async handleWechatPayment(orderResult: any) {
    console.log("订单确认页面 - 开始处理微信支付，订单结果:", orderResult);

    // 获取订单ID
    const orderId = orderResult.orderId || orderResult.id;
    if (!orderId) {
      console.error("订单确认页面 - 订单ID为空");
      uni.showToast({
        title: "订单信息异常",
        icon: "none",
      });
      return;
    }
    // 检查是否有支付参数
    const paymentParams = orderResult.paymentParams;
    if (paymentParams && paymentParams.timeStamp && paymentParams.nonceStr && paymentParams.packageValue && paymentParams.paySign) {
      console.log("订单确认页面 - 使用返回的支付参数直接发起支付");
      await this.proceedToPaymentWithParams(paymentParams);
      return;
    }
    uni.showToast({
      title: "支付异常，请重试",
      icon: "none",
    });
  }
  // 使用返回的支付参数直接发起支付
  async proceedToPaymentWithParams(paymentParams: any) {
    try {
      console.log("订单确认页面 - 使用支付参数发起支付:", paymentParams);

      // 检查微信支付环境
      if (!checkWechatPayEnvironment()) {
        uni.showToast({
          title: "当前环境不支持微信支付",
          icon: "none",
        });
        return;
      }

      // 构造微信支付参数
      const wxPayParams = {
        timeStamp: paymentParams.timeStamp,
        nonceStr: paymentParams.nonceStr,
        package: paymentParams.packageValue,
        signType: paymentParams.signType || "RSA",
        paySign: paymentParams.paySign,
      };

      console.log("订单确认页面 - 微信支付参数:", wxPayParams);

      // 调用微信支付
      uni.requestPayment({
        provider: "wxpay",
        ...wxPayParams,
        success: (res) => {
          console.log("订单确认页面 - 支付成功:", res);
          uni.showToast({
            title: "支付成功",
            icon: "success",
          });
          setTimeout(() => {
            this.navigateToOrderList();
          }, 1500);
        },
        fail: (err) => {
          console.error("订单确认页面 - 支付失败:", err);
          if (err.errMsg && err.errMsg.includes("cancel")) {
            uni.showToast({
              title: "支付已取消",
              icon: "none",
            });
          } else {
            uni.showToast({
              title: "支付失败",
              icon: "none",
            });
          }
          // 支付失败也跳转到订单列表，用户可以重新支付
          setTimeout(() => {
            this.navigateToOrderList();
          }, 1500);
        },
      });
    } catch (error) {
      console.error("订单确认页面 - 发起支付异常:", error);
      uni.showToast({
        title: "支付异常，请重试",
        icon: "none",
      });
      setTimeout(() => {
        this.navigateToOrderList();
      }, 1500);
    }
  }

  // 处理钱包支付
  async processWalletPayment(orderResult: any) {
    // 钱包支付逻辑（如果需要的话）
    uni.showToast({
      title: "钱包支付成功",
      icon: "success",
    });
    setTimeout(() => {
      this.navigateToOrderList();
    }, 1500);
  }

  // 跳转到订单列表
  navigateToOrderList() {
    uni.navigateTo({
      url: "/pages/orders/orders",
    });
  }

  // 获取用户ID
  getUserId(): string {
    try {
      const userInfo = uni.getStorageSync("userInfo");
      if (userInfo && userInfo.id) {
        // 确保返回字符串，避免大整数精度丢失
        return String(userInfo.id);
      } else {
        console.warn("订单确认页面 - 未找到用户信息");
        return "";
      }
    } catch (error) {
      console.error("订单确认页面 - 获取用户信息失败:", error);
      return "";
    }
  }

  // 加载默认收货地址
  async loadDefaultAddress() {
    const customerId = this.getUserId();
    if (!customerId) {
      console.warn("用户ID为空，无法加载默认地址");
      return;
    }

    try {
      const response = await getDefaultAddress(customerId);
      if (response && response.code === Code.OK.code && response.data) {
        const defaultAddress = response.data;
        this.selectedAddress = {
          id: defaultAddress.id,
          name: defaultAddress.name,
          phone: defaultAddress.phone,
          fullAddress: defaultAddress.fullAddress || `${defaultAddress.province}${defaultAddress.city}${defaultAddress.district}${defaultAddress.detail}`,
          isDefault: true,
        };
        console.log("订单确认页面 - 成功加载默认地址:", this.selectedAddress);
      } else {
        console.log("订单确认页面 - 未找到默认地址");
        this.selectedAddress = null;
      }
    } catch (error) {
      console.error("加载默认地址失败:", error);
      this.selectedAddress = null;
    }
  }

  // 加载地址列表
  async loadAddressList() {
    const customerId = this.getUserId();
    if (!customerId) {
      console.warn("用户ID为空，无法加载地址列表");
      return;
    }

    try {
      const response = await getAddressList(customerId);
      if (response && response.code === Code.OK.code && response.data) {
        this.addresses = response.data.map((addr: any) => ({
          id: addr.id,
          name: addr.name,
          phone: addr.phone,
          fullAddress: addr.fullAddress || `${addr.province}${addr.city}${addr.district}${addr.detail}`,
          isDefault: addr.isDefault === 1,
        }));
        console.log("订单确认页面 - 成功加载地址列表:", this.addresses);
      } else {
        console.log("订单确认页面 - 地址列表为空");
        this.addresses = [];
      }
    } catch (error) {
      console.error("加载地址列表失败:", error);
      this.addresses = [];
    }
  }
}
</script>

<style lang="scss" scoped>
.order-confirm-page {
  height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

/* 内容区域 */
.confirm-content {
  flex: 1;
  overflow-y: auto;
  padding-top: 10px;
}

/* 配送方式 */
.delivery-section {
  background-color: #fff;
  margin-bottom: 10px;
}

.delivery-tabs {
  display: flex;
  padding: 0 15px;
  border-bottom: 1px solid #f0f0f0;
}

.delivery-tab {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px 0;
  position: relative;

  &.active {
    .tab-text {
      color: #ff4444;
      font-weight: bold;
    }
  }
}

.tab-text {
  font-size: 16px;
  color: #666;
}

.tab-underline {
  position: absolute;
  bottom: 0;
  width: 30px;
  height: 2px;
  background-color: #ff4444;
}

/* 地址选择 */
.address-section {
  padding: 15px;
}

.address-prompt {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.prompt-text {
  font-size: 16px;
  color: #333;
}

/* 自提点 */
.pickup-section {
  padding: 15px;
}

.pickup-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.pickup-text {
  font-size: 16px;
  color: #333;
}

.pickup-location {
  background-color: #e8f4ff;
  color: #007aff;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.pickup-map {
  margin-left: auto;
  font-size: 14px;
  color: #666;
}

/* 分割线 */
.divider-line {
  height: 1px;
  background: linear-gradient(to right, #ff4444 0%, #007aff 20%, #ff4444 40%, #007aff 60%, #ff4444 80%, #007aff 100%);
  margin: 0 15px;
}

/* 商品列表 */
.products-section {
  background-color: #fff;
  margin-top: 10px;
}

.product-item {
  display: flex;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
  gap: 15px;

  &:last-child {
    border-bottom: none;
  }
}

.product-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  flex-shrink: 0;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.product-name {
  font-size: 14px;
  color: #333;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-spec {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  line-height: 1.2;
}

.product-tags {
  display: flex;
  gap: 8px;
}

.product-tag {
  background-color: #f5f5f5;
  color: #666;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.product-bottom {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: auto;
}

.product-price {
  font-size: 18px;
  color: #ff4444;
  font-weight: bold;
}

.product-original-price {
  font-size: 12px;
  color: #999;
  text-decoration: line-through;
}

.product-quantity {
  margin-left: auto;
  font-size: 14px;
  color: #666;
}

/* 合计 */
.total-section {
  background-color: #fff;
  padding: 15px;
  text-align: right;
  margin-top: 10px;
}

.total-text {
  font-size: 18px;
  color: #ff4444;
  font-weight: bold;
}

/* 备注信息 */
.remark-section {
  background-color: #fff;
  margin-top: 10px;
  padding: 15px;
}

.remark-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 15px;
}

.remark-title {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.remark-input {
  width: 100%;
  min-height: 60px;
  padding: 10px 12px;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  font-size: 14px;
  color: #333;
  background-color: #fafafa;
  resize: none;
  line-height: 1.4;
}

.remark-input::placeholder {
  color: #999;
}

/* 支付方式 */
.payment-section {
  background-color: #fff;
  margin-top: 10px;
  padding: 15px;
}

.payment-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 15px;
}

.payment-title {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.payment-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.payment-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  background-color: #fafafa;
  transition: all 0.3s ease;

  &.active {
    border-color: #007aff;
    background-color: #f0f8ff;
  }
}

.payment-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.payment-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.payment-name {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.wallet-balance {
  font-size: 12px;
  color: #666;
}

/* 底部操作栏 */
.confirm-footer {
  background-color: #fff;
  padding: 10px 15px;
  border-radius: 50px;
  margin: 10px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.06);
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
}

.footer-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.payment-info-footer {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.payment-method {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

.total-line {
  display: flex;
  align-items: baseline;
}

.total-label {
  font-size: 14px;
  color: #666;
  margin-right: 8px;
}

.total-price {
  color: #ff4444;
  font-weight: bold;
  font-size: 18px;
}

.submit-btn {
  background: linear-gradient(135deg, #007aff 0%, #5ac8fa 100%);
  border-radius: 25px;
  min-width: 120px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
  transition: all 0.3s ease;
}

.submit-btn.disabled {
  background: #f5f5f5;
  color: #ccc;
  box-shadow: none;
}

.submit-text {
  color: #fff;
  font-size: 16px;
  font-weight: 600;
  text-align: center;
}

.submit-btn.disabled .submit-text {
  color: #ccc;
}

/* 地址相关样式 */
.address-card {
  background-color: #f8f8f8;
  border-radius: 8px;
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.address-info {
  flex: 1;
}

.address-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
}

.receiver-name {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.receiver-phone {
  font-size: 14px;
  color: #666;
}

.address-detail {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.address-prompt {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 20px;
  margin: 15px;
  border: 2px dashed #ddd;
  border-radius: 8px;
}

.prompt-text {
  font-size: 14px;
  color: #666;
}

/* 配送方式选择 */
.delivery-options {
  margin: 15px;
  background-color: #f8f8f8;
  border-radius: 8px;
  padding: 15px;
}

.options-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 15px;
}

.delivery-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.delivery-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background-color: #fff;
  border-radius: 6px;
  border: 1px solid #eee;

  &.active {
    border-color: #007aff;
    background-color: #f0f8ff;
  }
}

.option-info {
  flex: 1;
}

.option-name {
  font-size: 14px;
  color: #333;
  font-weight: bold;
  margin-bottom: 4px;
}

.option-desc {
  font-size: 12px;
  color: #999;
}

.option-fee {
  font-size: 14px;
  color: #ff4444;
  font-weight: bold;
}

/* 地址选择弹窗 */
.address-picker {
  padding: 20px;
  max-height: 70vh;
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.picker-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.address-list {
  max-height: 400px;
  overflow-y: auto;
  margin-bottom: 20px;
}

.address-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;

  &.active {
    background-color: #f0f8ff;
    border-radius: 8px;
    padding: 15px;
    margin: 5px 0;
  }

  &:last-child {
    border-bottom: none;
  }
}

.address-content {
  flex: 1;
}

.default-tag {
  background-color: #ff4444;
  color: #fff;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 8px;
}

.address-actions {
  margin-top: 10px;
}

.address-actions-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #f0f0f0;
}

.address-edit,
.address-add {
  font-size: 14px;
  color: #007aff;
  padding: 8px 16px;
  border: 1px solid #007aff;
  border-radius: 20px;
  background-color: transparent;
}

.address-edit:active,
.address-add:active {
  background-color: #f0f8ff;
}
</style>
