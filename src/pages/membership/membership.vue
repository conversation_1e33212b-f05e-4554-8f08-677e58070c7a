<template>
  <view class="membership-page" :style="{ height: contentHeight + 'px', paddingTop: topHeight + 'px' }">
    <navigation-bar style="width: 100%; z-index: 99" :showIcon="true" :showText="true" text="会员中心"></navigation-bar>

    <view class="membership-content">
      <!-- 会员卡片 -->
      <view class="member-card" :class="'level-' + currentLevel.level">
        <view class="card-bg">
          <!--          <image :src="currentLevel.bgImage" class="bg-image" mode="aspectFill" />-->
          <view class="card-overlay"></view>
        </view>

        <view class="card-content">
          <view class="member-info">
            <view class="avatar-section">
              <image :src="wxUserInfo.headimgurl" class="user-avatar" mode="aspectFill" />
              <view class="level-badge" :class="'badge-level-' + currentLevel.level">
                <text class="level-text">{{ currentLevel.level }}</text>
              </view>
            </view>

            <view class="user-details">
              <text class="user-name">{{ userInfo.username || wxUserInfo.nickname || "暂无" }}</text>
              <text class="member-level">{{ currentLevel.name }}</text>
              <text class="member-id">会员号：{{ userInfo.memberId || "暂无" }}</text>
            </view>
          </view>

          <view class="member-benefits">
            <view class="benefit-item">
              <text class="benefit-value">{{ currentLevel.discount || 0 }}折</text>
              <text class="benefit-label">专享折扣</text>
            </view>
            <view class="benefit-item">
              <text class="benefit-value">{{ userInfo.points || 0 }}</text>
              <text class="benefit-label">积分余额</text>
            </view>
            <view class="benefit-item">
              <text class="benefit-value">{{ userInfo.coupons || 0 }}</text>
              <text class="benefit-label">优惠券</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 升级进度 -->
      <view class="upgrade-section">
        <view class="section-title">
          <text class="title-text">升级进度</text>
          <text class="upgrade-tip" @click="showLevelRules">等级规则 ></text>
        </view>

        <view class="progress-info">
          <text class="current-exp">当前经验：{{ userInfo.experience || 0 }}</text>
          <text class="next-level" v-if="nextLevel"> 距离{{ nextLevel.name }}还需{{ nextLevel.requiredExp - userInfo.experience || 0 }}经验 </text>
          <text class="max-level" v-else>已达到最高等级</text>
        </view>

        <view class="progress-bar" v-if="nextLevel">
          <view class="progress-fill" :style="{ width: progressPercent + '%' }"></view>
        </view>
      </view>

      <!-- 会员权益 -->
      <view class="benefits-section">
        <view class="section-title">
          <text class="title-text">会员权益</text>
        </view>

        <view class="benefits-grid">
          <view v-for="(benefit, index) in currentLevel.benefits" :key="index" class="benefit-card">
            <van-icon :name="benefit.icon" size="20" :color="benefit.color" />
            <text class="benefit-name">{{ benefit.name }}</text>
          </view>
        </view>
      </view>

      <!-- 获取经验 -->
      <view class="exp-section">
        <view class="section-title">
          <text class="title-text">获取经验</text>
        </view>
        <van-empty v-if="!expTasks.length" description="暂无可获取经验的任务" />

        <view class="exp-list">
          <view v-for="(task, index) in expTasks" :key="index" class="exp-item" @click="doExpTask(task)">
            <view class="task-info">
              <van-icon :name="task.icon" size="20" :color="task.iconColor" />
              <view class="task-details">
                <text class="task-name">{{ task.name }}</text>
                <text class="task-desc">{{ task.description }}</text>
              </view>
            </view>
            <view class="task-reward">
              <text class="exp-value">+{{ task.exp }}</text>
              <text class="exp-label">经验</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 会员记录 -->
      <view class="records-section">
        <view class="section-title">
          <text class="title-text">会员记录</text>
          <text class="more-btn" @click="goRecords">查看全部 ></text>
        </view>
        <van-empty v-if="!memberRecords.length" description="暂无会员记录" />

        <view class="record-list">
          <view v-for="(record, index) in memberRecords" :key="index" class="record-item">
            <view class="record-info">
              <text class="record-title">{{ record.title }}</text>
              <text class="record-time">{{ record.time }}</text>
            </view>
            <text class="record-exp" :class="record.type"> {{ record.type === "gain" ? "+" : "-" }}{{ record.exp }} </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 等级规则弹窗 -->
    <van-popup :show="showRulesPopup" position="bottom" round @close="showRulesPopup = false">
      <view class="rules-popup">
        <view class="rules-header">
          <text class="rules-title">会员等级规则</text>
          <van-icon name="cross" size="20" @click="showRulesPopup = false" />
        </view>

        <view class="rules-content">
          <view v-for="(level, index) in memberLevels" :key="index" class="level-rule">
            <view class="level-info">
              <image :src="level.icon" class="rule-icon" mode="aspectFill" />
              <view class="level-details">
                <text class="level-name">{{ level.name }}</text>
                <text class="level-requirement">
                  {{ level.requiredExp === 0 ? "注册即可获得" : `需要${level.requiredExp}经验` }}
                </text>
              </view>
            </view>

            <view class="level-benefits">
              <text class="benefits-title">专享权益：</text>
              <text v-for="(benefit, bIndex) in level.benefits" :key="bIndex" class="benefit-tag">
                {{ benefit.name }}
              </text>
            </view>
          </view>
        </view>
      </view>
    </van-popup>
  </view>
</template>

<script lang="ts">
import Vue from "vue";
import { Component } from "vue-property-decorator";
import NavigationBar from "@/components/common/NavigationBar.vue";
import { getLayoutHeights } from "@/utils/get-page-height.util";

interface MemberLevel {
  level: number;
  name: string;
  requiredExp: number;
  discount: number;
  icon: string;
  bgImage: string;
  benefits: Benefit[];
}

interface Benefit {
  name: string;
  description: string;
  icon: string;
  color: string;
}

interface ExpTask {
  id: number;
  name: string;
  description: string;
  exp: number;
  icon: string;
  iconColor: string;
  action: string;
}

interface MemberRecord {
  id: number;
  title: string;
  time: string;
  exp: number;
  type: "gain" | "use";
}

@Component({
  name: "membership",
  components: { NavigationBar },
})
export default class Membership extends Vue {
  showRulesPopup = false;
  topHeight = 88;
  contentHeight = 0;
  wxUserInfo = uni.getStorageSync("wxUserInfo");
  userInfo = uni.getStorageSync("userInfo");

  memberLevels: MemberLevel[] = [
    {
      level: 0,
      name: "普通会员",
      requiredExp: 0,
      discount: 0,
      icon: "/static/images/vip/VIP-01.png",
      bgImage: "/static/images/vip/VIP-01.png",
      benefits: [
        { name: "生日特权", description: "生日当月专享优惠", icon: "gift-o", color: "#ff6b6b" },
        { name: "积分奖励", description: "购物获得积分", icon: "gold-coin-o", color: "#ffd700" },
      ],
    },
    {
      level: 1,
      name: "青铜会员",
      requiredExp: 0,
      discount: 9.8,
      icon: "/static/images/vip/VIP-02.png",
      bgImage: "/static/images/vip/VIP-02.png",
      benefits: [
        { name: "生日特权", description: "生日当月专享优惠", icon: "gift-o", color: "#ff6b6b" },
        { name: "积分奖励", description: "购物获得积分", icon: "gold-coin-o", color: "#ffd700" },
      ],
    },
    {
      level: 2,
      name: "白银会员",
      requiredExp: 500,
      discount: 9.5,
      icon: "/static/images/vip/VIP-03.png",
      bgImage: "/static/images/vip/VIP-03.png",
      benefits: [
        { name: "生日特权", description: "生日当月专享优惠", icon: "gift-o", color: "#ff6b6b" },
        { name: "积分奖励", description: "购物获得双倍积分", icon: "gold-coin-o", color: "#ffd700" },
        { name: "专属客服", description: "优先客服服务", icon: "service-o", color: "#4ecdc4" },
      ],
    },
    {
      level: 3,
      name: "黄金会员",
      requiredExp: 1000,
      discount: 9.0,
      icon: "/static/images/vip/VIP-04.png",
      bgImage: "/static/images/vip/VIP-04.png",
      benefits: [
        { name: "生日特权", description: "生日当月专享优惠", icon: "gift-o", color: "#ff6b6b" },
        { name: "积分奖励", description: "购物获得三倍积分", icon: "gold-coin-o", color: "#ffd700" },
        { name: "专属客服", description: "优先客服服务", icon: "service-o", color: "#4ecdc4" },
        { name: "免费配送", description: "全场免配送费", icon: "logistics", color: "#52c41a" },
      ],
    },
    {
      level: 4,
      name: "钻石会员",
      requiredExp: 2000,
      discount: 8.5,
      icon: "/static/images/vip/VIP-05.png",
      bgImage: "/static/images/vip/VIP-05.png",
      benefits: [
        { name: "生日特权", description: "生日当月专享优惠", icon: "gift-o", color: "#ff6b6b" },
        { name: "积分奖励", description: "购物获得五倍积分", icon: "gold-coin-o", color: "#ffd700" },
        { name: "专属客服", description: "专属客服经理", icon: "service-o", color: "#4ecdc4" },
        { name: "免费配送", description: "全场免配送费", icon: "logistics", color: "#52c41a" },
        { name: "专享活动", description: "专属促销活动", icon: "fire-o", color: "#ff4757" },
        { name: "优先发货", description: "订单优先处理", icon: "clock-o", color: "#3742fa" },
      ],
    },
  ];

  expTasks: ExpTask[] = [
    // {
    //   id: 1,
    //   name: "每日签到",
    //   description: "连续签到获得更多经验",
    //   exp: 10,
    //   icon: "calendar-o",
    //   iconColor: "#52c41a",
    //   action: "signin",
    // },
    // {
    //   id: 2,
    //   name: "完成购买",
    //   description: "每消费1元获得1经验",
    //   exp: 50,
    //   icon: "shopping-cart-o",
    //   iconColor: "#fa8c16",
    //   action: "purchase",
    // },
    // {
    //   id: 3,
    //   name: "邀请好友",
    //   description: "邀请好友注册",
    //   exp: 100,
    //   icon: "friends-o",
    //   iconColor: "#722ed1",
    //   action: "invite",
    // },
    // {
    //   id: 4,
    //   name: "商品评价",
    //   description: "对购买商品进行评价",
    //   exp: 20,
    //   icon: "star-o",
    //   iconColor: "#1890ff",
    //   action: "review",
    // },
  ];

  memberRecords: MemberRecord[] = [];

  get currentLevel() {
    for (let i = this.memberLevels.length - 1; i >= 0; i--) {
      if (this.userInfo.experience >= this.memberLevels[i].requiredExp) {
        return this.memberLevels[i];
      }
    }
    return this.memberLevels[0];
  }

  get nextLevel() {
    const currentIndex = this.memberLevels.findIndex((level) => level.level === this.currentLevel.level);
    return currentIndex < this.memberLevels.length - 1 ? this.memberLevels[currentIndex + 1] : null;
  }

  get progressPercent() {
    if (!this.nextLevel) return 100;
    const currentExp = this.userInfo.experience - this.currentLevel.requiredExp;
    const totalExp = this.nextLevel.requiredExp - this.currentLevel.requiredExp;
    return Math.min((currentExp / totalExp) * 100, 100);
  }

  showLevelRules() {
    this.showRulesPopup = true;
  }

  doExpTask(task: ExpTask) {
    switch (task.action) {
      case "signin":
        this.handleSignin(task);
        break;
      case "purchase":
        this.handlePurchase();
        break;
      case "invite":
        this.handleInvite();
        break;
      case "review":
        this.handleReview();
        break;
    }
  }

  handleSignin(task: ExpTask) {
    this.userInfo.experience += task.exp;
    this.addRecord("每日签到", task.exp, "gain");
    uni.showToast({ title: `签到成功，获得${task.exp}经验`, icon: "success" });
  }

  handlePurchase() {
    uni.switchTab({
      url: "/pages/index/index",
    });
  }

  handleInvite() {
    uni.showToast({ title: "邀请功能开发中", icon: "none" });
  }

  handleReview() {
    uni.navigateTo({
      url: "/pages/orders/orders",
    });
  }

  addRecord(title: string, exp: number, type: "gain" | "use") {
    const newRecord: MemberRecord = {
      id: Date.now(),
      title,
      time: new Date().toLocaleString(),
      exp,
      type,
    };
    this.memberRecords.unshift(newRecord);
  }

  goRecords() {
    uni.navigateTo({
      url: "/pages/membership/records",
    });
  }
  onShow() {
    const { topHeight, contentHeight } = getLayoutHeights();
    this.topHeight = topHeight;
    this.contentHeight = contentHeight;
  }
}
</script>

<style lang="scss" scoped>
.membership-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.membership-content {
  padding: 15px;
}

/* 会员卡片样式 */
.member-card {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  margin-bottom: 20px;
  height: 200px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);

  &.level-0 {
    /* 普通会员 - 淡蓝色渐变 */
    background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 50%, #91d5ff 100%);
    box-shadow: 0 8px 24px rgba(145, 213, 255, 0.2);
  }

  &.level-1 {
    /* 青铜会员 - 古铜色渐变 */
    background: linear-gradient(135deg, #8b4513 0%, #cd853f 50%, #d2691e 100%);
    box-shadow: 0 8px 24px rgba(139, 69, 19, 0.3);
  }

  &.level-2 {
    /* 白银会员 - 银色渐变 */
    background: linear-gradient(135deg, #708090 0%, #c0c0c0 50%, #e6e6fa 100%);
    box-shadow: 0 8px 24px rgba(192, 192, 192, 0.3);
  }

  &.level-3 {
    /* 黄金会员 - 金色渐变 */
    background: linear-gradient(135deg, #b8860b 0%, #ffd700 50%, #ffa500 100%);
    box-shadow: 0 8px 24px rgba(255, 215, 0, 0.4);
  }

  &.level-4 {
    /* 钻石会员 - 钻石蓝紫渐变 */
    background: linear-gradient(135deg, #4b0082 0%, #8a2be2 30%, #9370db 60%, #dda0dd 100%);
    box-shadow: 0 8px 24px rgba(138, 43, 226, 0.4);
  }
}

.card-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.bg-image {
  width: 100%;
  height: 100%;
  opacity: 0.3;
}

.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.2);
}

.card-content {
  position: relative;
  z-index: 2;
  padding: 20px 20px 15px 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  color: #fff;
}

.member-info {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.avatar-section {
  position: relative;
  margin-right: 15px;
}

.user-avatar {
  width: 60px;
  height: 60px;
  border-radius: 30px;
  border: 3px solid rgba(255, 255, 255, 0.3);
}

.level-badge {
  position: absolute;
  bottom: -5px;
  right: -5px;
  width: 24px;
  height: 24px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid rgba(255, 255, 255, 0.8);

  &.badge-level-0 {
    background: linear-gradient(135deg, #91d5ff, #74c5ff);
  }

  &.badge-level-1 {
    background: linear-gradient(135deg, #8b4513, #cd853f);
  }

  &.badge-level-2 {
    background: linear-gradient(135deg, #708090, #c0c0c0);
  }

  &.badge-level-3 {
    background: linear-gradient(135deg, #b8860b, #ffd700);
  }

  &.badge-level-4 {
    background: linear-gradient(135deg, #4b0082, #8a2be2);
  }
}

.level-text {
  color: #fff;
  font-size: 12px;
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 3px;
  display: block;
}

.member-level {
  font-size: 14px;
  opacity: 0.9;
  margin-bottom: 2px;
  display: block;
}

.member-id {
  font-size: 11px;
  opacity: 0.8;
}

.member-benefits {
  display: flex;
  justify-content: space-around;
  padding-top: 15px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  bottom: 15px;
  left: 20px;
  right: 20px;
}

.benefit-item {
  text-align: center;
  flex: 1;
}

.benefit-value {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 3px;
  display: block;
}

.benefit-label {
  font-size: 12px;
  opacity: 0.9;
}

/* 通用区块样式 */
.upgrade-section,
.benefits-section,
.exp-section,
.records-section {
  background-color: #fff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 15px;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.title-text {
  font-size: 18px;
  color: #333;
  font-weight: bold;
}

.upgrade-tip,
.more-btn {
  font-size: 14px;
  color: #007aff;
}

/* 升级进度样式 */
.progress-info {
  margin-bottom: 15px;
}

.current-exp {
  font-size: 14px;
  color: #333;
  margin-bottom: 5px;
  display: block;
}

.next-level,
.max-level {
  font-size: 12px;
  color: #666;
}

.progress-bar {
  height: 8px;
  background-color: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #007aff, #40a9ff);
  transition: width 0.3s ease;
}

/* 权益网格样式 */
.benefits-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.benefit-card {
  width: calc(45% - 7px);
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 12px 8px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  }
}

.benefit-name {
  font-size: 12px;
  color: #333;
  font-weight: 500;
  margin-top: 6px;
  display: block;
  line-height: 1.2;
}

/* 经验任务样式 */
.exp-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.exp-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border: 1px solid #e9ecef;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  }
}

.task-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.task-details {
  margin-left: 12px;
}

.task-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  margin-bottom: 3px;
  display: block;
}

.task-desc {
  font-size: 12px;
  color: #666;
}

.task-reward {
  text-align: center;
}

.exp-value {
  font-size: 16px;
  color: #007aff;
  font-weight: bold;
  margin-bottom: 3px;
  display: block;
}

.exp-label {
  font-size: 12px;
  color: #666;
}

/* 记录列表样式 */
.record-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.record-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.record-info {
  flex: 1;
}

.record-title {
  font-size: 14px;
  color: #333;
  margin-bottom: 3px;
  display: block;
}

.record-time {
  font-size: 12px;
  color: #999;
}

.record-exp {
  font-size: 14px;
  font-weight: bold;

  &.gain {
    color: #52c41a;
  }

  &.use {
    color: #ff4444;
  }
}

/* 规则弹窗样式 */
.rules-popup {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

.rules-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.rules-title {
  font-size: 18px;
  color: #333;
  font-weight: bold;
}

.rules-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.level-rule {
  border: 1px solid #f0f0f0;
  border-radius: 12px;
  padding: 15px;
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #ddd, #f5f5f5);
  }

  &:nth-child(2)::before {
    /* 青铜会员 */
    background: linear-gradient(90deg, #8b4513, #cd853f, #d2691e);
  }

  &:nth-child(3)::before {
    /* 白银会员 */
    background: linear-gradient(90deg, #708090, #c0c0c0, #e6e6fa);
  }

  &:nth-child(4)::before {
    /* 黄金会员 */
    background: linear-gradient(90deg, #b8860b, #ffd700, #ffa500);
  }

  &:nth-child(5)::before {
    /* 钻石会员 */
    background: linear-gradient(90deg, #4b0082, #8a2be2, #9370db, #dda0dd);
  }
}

.level-info {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.rule-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  margin-right: 12px;
}

.level-details {
  flex: 1;
}

.level-name {
  font-size: 16px;
  color: #333;
  font-weight: bold;
  margin-bottom: 3px;
  display: block;
}

.level-requirement {
  font-size: 12px;
  color: #666;
}

.level-benefits {
  padding-top: 10px;
  border-top: 1px solid #f5f5f5;
}

.benefits-title {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
  display: block;
}

.benefit-tag {
  display: inline-block;
  background-color: #f0f8ff;
  color: #007aff;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 8px;
  margin-right: 6px;
  margin-bottom: 4px;
}
</style>
