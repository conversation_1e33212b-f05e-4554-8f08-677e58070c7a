<template>
  <view class="records-page">
    <navigation-bar :showIcon="true" :showText="true" text="会员记录"></navigation-bar>
    
    <view class="filter-bar">
      <van-dropdown-menu>
        <van-dropdown-item v-model="filterType" :options="typeOptions" />
        <van-dropdown-item v-model="filterTime" :options="timeOptions" />
      </van-dropdown-menu>
    </view>
    
    <view class="records-list">
      <view v-for="(record, index) in records" :key="index" class="record-item">
        <view class="record-info">
          <text class="record-title">{{ record.title }}</text>
          <text class="record-time">{{ record.time }}</text>
        </view>
        <text class="record-exp" :class="record.type">
          {{ record.type === 'gain' ? '+' : '-' }}{{ record.exp }}
        </text>
      </view>
      
      <van-empty v-if="!records.length && !loading" description="暂无会员记录" />
      
      <van-loading v-if="loading" size="24px" vertical>加载中...</van-loading>
      <view v-if="!hasMore" class="no-more">没有更多了</view>
    </view>
  </view>
</template>

<script lang="ts">
import Vue from "vue";
import { Component } from "vue-property-decorator";
import NavigationBar from "@/components/common/NavigationBar.vue";

interface MemberRecord {
  id: number;
  title: string;
  time: string;
  exp: number;
  type: "gain" | "use";
}

@Component({
  name: "membership-records",
  components: { NavigationBar },
})
export default class MembershipRecords extends Vue {
  loading = false;
  hasMore = true;
  page = 1;
  pageSize = 10;
  
  filterType = "all";
  filterTime = "all";
  
  typeOptions = [
    { text: "全部类型", value: "all" },
    { text: "获得经验", value: "gain" },
    { text: "消耗经验", value: "use" },
  ];
  
  timeOptions = [
    { text: "全部时间", value: "all" },
    { text: "最近一周", value: "week" },
    { text: "最近一月", value: "month" },
    { text: "最近三月", value: "quarter" },
  ];
  
  records: MemberRecord[] = [];
  
  mounted() {
    this.loadRecords();
  }
  
  async loadRecords() {
    this.loading = true;
    try {
      const res = await uni.request({
        url: '/api/membership/records',
        method: 'GET',
        data: {
          page: this.page,
          pageSize: this.pageSize,
          type: this.filterType === 'all' ? undefined : this.filterType,
          time: this.filterTime === 'all' ? undefined : this.filterTime
        }
      });
      
      if (res.statusCode === 200) {
        this.records = [...this.records, ...res.data.data];
        this.hasMore = res.data.hasMore;
      } else {
        uni.showToast({ title: '获取会员记录失败', icon: 'none' });
      }
    } catch (error) {
      console.error(error);
      uni.showToast({ title: '网络错误', icon: 'none' });
    } finally {
      this.loading = false;
    }
  }
  
  onReachBottom() {
    if (!this.loading && this.hasMore) {
      this.page++;
      this.loadRecords();
    }
  }
}
</script>

<style lang="scss" scoped>
.records-page {
  padding-top: 88px;
  
  .filter-bar {
    padding: 16rpx;
    background: #fff;
    position: sticky;
    top: 88px;
    z-index: 99;
  }
  
  .records-list {
    padding: 16rpx;
    
    .record-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24rpx;
      margin-bottom: 16rpx;
      background: #fff;
      border-radius: 16rpx;
      
      .record-info {
        flex: 1;
        
        .record-title {
          font-size: 28rpx;
          color: #333;
          margin-bottom: 8rpx;
          display: block;
        }
        
        .record-time {
          font-size: 24rpx;
          color: #999;
        }
      }
      
      .record-exp {
        font-size: 28rpx;
        font-weight: bold;
        
        &.gain {
          color: #52c41a;
        }
        
        &.use {
          color: #f5222d;
        }
      }
    }
    
    .no-more {
      text-align: center;
      padding: 24rpx;
      color: #999;
      font-size: 24rpx;
    }
  }
}
</style>