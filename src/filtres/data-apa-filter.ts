const dataApa = (str: string, frontLen: number, endLen: number, wildcard = "*") => {
  if (!str) {
    return "--";
  } else if (str.length < 9) {
    return str;
  } else {
    const len = str.length - frontLen - endLen;
    let asterisk = "";
    for (let i = 0; i < len; i++) {
      asterisk += wildcard;
    }
    return str.substring(0, frontLen) + asterisk + str.substring(str.length - endLen);
  }
};
export default dataApa;
