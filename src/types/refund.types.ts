/**
 * 退款相关类型定义
 */

export interface RefundOrder {
  id: string
  refundNo: string
  orderId: string
  orderNumber: string
  customerId: string
  customerName?: string
  refundType: 'FULL' // 当前只支持全额退款
  refundAmount: number
  refundReason: string
  refundDescription?: string
  refundImages?: string[] // 退款凭证图片URL列表
  refundStatus: RefundStatus
  applyTime: string
  auditResult?: 'APPROVED' | 'REJECTED'
  auditRemark?: string
  auditTime?: string
  auditUserId?: number
  auditUserName?: string
  wxRefundId?: string
  wxSuccessTime?: string
  createTime: string
  updateTime: string
}

export type RefundStatus = 
  | 'PENDING'     // 待审核
  | 'APPROVED'    // 已同意
  | 'REJECTED'    // 已拒绝
  | 'PROCESSING'  // 退款处理中
  | 'SUCCESS'     // 退款成功
  | 'FAILED'      // 退款失败

export interface RefundEligibilityResult {
  eligible: boolean
  reason?: string
  autoApprove?: boolean
}

export interface RefundListResponse {
  records: RefundOrder[]
  total: number
  pageNum: number
  pageSize: number
}

export const REFUND_STATUS_MAP: Record<RefundStatus, { label: string; color: string }> = {
  PENDING: { label: '待审核', color: '#fa8c16' },
  APPROVED: { label: '已同意', color: '#52c41a' },
  REJECTED: { label: '已拒绝', color: '#ff4d4f' },
  PROCESSING: { label: '退款中', color: '#1890ff' },
  SUCCESS: { label: '退款成功', color: '#52c41a' },
  FAILED: { label: '退款失败', color: '#ff4d4f' }
}

export const REFUND_REASONS = [
  '商品质量问题',
  '商品与描述不符',
  '商品损坏',
  '不喜欢/不合适',
  '重复下单',
  '其他原因'
]