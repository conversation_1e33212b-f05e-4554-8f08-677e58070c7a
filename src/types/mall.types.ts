/**
 * 商城相关类型定义
 *
 * <AUTHOR>
 * @date 2025-08-17
 */

// ==================== 基础类型 ====================

export interface BaseResponse<T = any> {
  code: number;
  data: T;
  msg: string;
}

export interface PageResponse<T = any> {
  records: T[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

// ==================== 商品相关类型 ====================

export interface Product {
  id: string;
  name: string;
  subtitle?: string;
  description?: string;
  price: number;
  originalPrice?: number;
  stock: number;
  image: string;
  images?: string[];
  detailImages?: string[];
  categoryId: string;
  categoryName?: string;
  specs?: string;
  tags?: string[];
  promotions?: string[];
  isActive: number;
  salesCount: number;
  isFavorite?: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Category {
  id: string;
  name: string;
  parentId?: string;
  level: number;
  sortOrder: number;
  icon?: string;
  image?: string;
  children?: Category[];
}

export interface ProductSearchParams {
  pageNum?: number;
  pageSize?: number;
  keyword?: string;
  categoryId?: string;
  isActive?: number;
}

// ==================== 购物车相关类型 ====================

export interface CartItem {
  id: string;
  customerId: string;
  productId: string;
  name: string;
  spec?: string;
  price: number;
  quantity: number;
  image: string;
  selected: boolean;
  isFavorite?: boolean;
  stock?: number;
  isActive?: boolean;
  createdAt: string;
}

export interface CartAddParams {
  customerId: string;
  productId: string;
  name: string;
  spec?: string;
  price: number;
  quantity: number;
  image: string;
  selected?: number;
}

// ==================== 订单相关类型 ====================

export interface Order {
  id: string;
  orderNumber: string;
  customerId: string;
  orderStatus: string;
  statusText: string;
  totalAmount: number;
  totalQuantity: number;
  paymentMethod: string;
  deliveryFee: number;
  receiverName: string;
  receiverPhone: string;
  receiverAddress: string;
  createTime: string;
  payTime?: string; // 支付时间
  paidAt?: string;
  shippedAt?: string;
  completedAt?: string;
  items?: OrderItem[];
  // 退款相关字段
  refundId?: string; // 退款记录ID
  refundStatus?: string; // 退款状态
  refundAmount?: number; // 退款金额
}

export interface OrderItem {
  id: string;
  productId: string;
  name: string;
  spec?: string;
  price: number;
  quantity: number;
  image: string;
  subtotal: number;
}

export interface OrderQueryParams {
  customerId: string;
  orderStatus?: string;
  pageNum?: number;
  pageSize?: number;
}

// ==================== 地址相关类型 ====================

export interface Address {
  id: string;
  customerId: string;
  name: string;
  phone: string;
  province: string;
  city: string;
  district: string;
  detail: string;
  fullAddress?: string;
  tag?: string;
  isDefault: number;
  createdAt?: string;
  updatedAt?: string;
}

export interface AddressSaveParams {
  id?: string;
  customerId: string;
  name: string;
  phone: string;
  province: string;
  city: string;
  district: string;
  detail: string;
  tag?: string;
  isDefault?: number;
}

export interface AddressSaveParams {
  id?: string;
  customerId: string;
  name: string;
  phone: string;
  province: string;
  city: string;
  district: string;
  detail: string;
  tag?: string;
  isDefault?: number;
}

// ==================== 收藏相关类型 ====================

export interface Favorite {
  id: string;
  customerId: string;
  productId: string;
  name: string;
  price: number;
  image: string;
  favoriteTime: string;
  type: string;
  selected?: boolean;
}

export interface FavoriteProduct {
  id: string;
  name: string;
  subtitle?: string;
  description?: string;
  price: number;
  originalPrice?: number;
  stock: number;
  image: string;
  images?: string;
  categoryId: string;
  categoryName?: string;
  specs?: string;
  tags?: string;
  promotions?: string;
  isActive: number;
  salesCount: number;
  createdAt: string;
  updatedAt?: string;
  favoriteTime: string;
}

// ==================== 浏览足迹相关类型 ====================

export interface Footprint {
  id: string;
  customerId: string;
  productId: string;
  name: string;
  price: number;
  image: string;
  visitTime: string;
  product?: Product;
}

// ==================== 搜索历史相关类型 ====================

export interface SearchHistory {
  id: string;
  customerId: string;
  keyword: string;
  searchTime: string;
}

// ==================== 钱包相关类型 ====================

export interface Wallet {
  id: string;
  customerId: string;
  balance: number;
  totalRecharge: number;
  totalExpense: number;
  createdAt: string;
  updatedAt: string;
}

export interface WalletTransaction {
  id: string;
  customerId: string;
  title: string;
  amount: number;
  type: string; // income-收入, expense-支出
  transactionStatus: string; // success-成功, pending-处理中, failed-失败
  statusText: string;
  icon: string;
  iconColor: string;
  relatedOrderId?: string;
  paymentMethod?: string;
  time: string;
}

export interface WalletOperationParams {
  customerId: string;
  amount: number;
  type: string;
  description?: string;
  orderId?: string;
}

export interface TransactionQueryParams {
  customerId: string;
  type?: string;
  pageNum?: number;
  pageSize?: number;
}

// ==================== 优惠券相关类型 ====================

export interface Coupon {
  id: number;
  name: string;
  type: string; // discount-折扣券, cash-现金券
  discountValue: number;
  minAmount: number;
  maxDiscount?: number;
  totalQuantity: number;
  usedQuantity: number;
  startTime: string;
  endTime: string;
  description?: string;
}

export interface CustomerCoupon {
  id: number;
  customerId: number;
  couponId: number;
  couponName: string;
  couponType: string;
  discountValue: number;
  minAmount: number;
  couponStatus: string; // unused-未使用, used-已使用, expired-已过期
  receiveTime: string;
  useTime?: string;
  expireTime: string;
  orderId?: number;
}

// ==================== 用户统计相关类型 ====================

export interface CustomerStats {
  id: number;
  customerId: number;
  totalOrders: number;
  favoritesCount: number;
  footprintsCount: number;
  points: number;
  createdAt: string;
  updatedAt: string;
}

// ==================== 推荐商品相关类型 ====================

export interface RecommendProduct {
  id: number;
  name: string;
  price: number;
  image: string;
  productId: number;
  sortOrder: number;
  product?: Product;
}
