import { get, post } from "@/utils/http.request";
import { HeaderType } from "@/constants/enum/header-type.enum";
import { Result } from "@/model/domain/Result";

const baseURL = (process.env.VUE_APP_ENABLE_GATEWAY === "on" ? process.env.VUE_APP_MS_GATEWAY_API + process.env.VUE_APP_BUSINESS_API : process.env.VUE_APP_BUSINESS_API) + process.env.VUE_APP_API_PREFIX;

const prefix = "camp-period";

/**
 * @description: 新增营期
 */
export const addCampGroup = (data = {}): Promise<Result> => {
  return post(`${baseURL}/${prefix}`, data, HeaderType.AUTH.code);
};

/**
 * @description: 删除营期
 */
export const deleteCampGroup = (data = {}): Promise<Result> => {
  return post(`${baseURL}/${prefix}/deleteById`, data, HeaderType.AUTH.code);
};

/**
 * @description: 修改视频
 */
export const updateCampGroup = (data = {}): Promise<Result> => {
  return post(`${baseURL}/${prefix}/updateById`, data, HeaderType.AUTH.code);
};

/**
 * @description: 查询营期分组列表通过id
 */
export const queryCampGroupListById = (params = {}): Promise<Result> => {
  return get(`${baseURL}/${prefix}/`, params, HeaderType.AUTH.code);
};

/**
 * @description: 查询营期分组列表通过分页
 */
export const queryCampGroupListByPage = (params = {}): Promise<Result> => {
  return get(`${baseURL}/${prefix}/page`, params, HeaderType.AUTH.code);
};

/**
 * @description: 查询课程分组列表
 */
export const queryCourseGroupList = (params = {}): Promise<Result> => {
  return get(`${baseURL}/${prefix}/list`, params, HeaderType.AUTH.code);
};

/**
 * @description: 查询课程分组列表通过课程id
 */
export const queryCourseGroupListByGroupId = (params = {}): Promise<Result> => {
  return get(`${baseURL}/${prefix}/courseVideoList`, params, HeaderType.AUTH.code);
};

/**
 * @description: 根据companyId和salesId查询课程视频
 */
export const queryCampCourseListByPage = (params = {}): Promise<Result> => {
  return get(`${baseURL}/${prefix}/getCourseByCompanyIdANDSalesId`, params, HeaderType.AUTH.code);
};

/**
 * @description: 根据companyId查询营期
 */
export const getCampPeriodsByCompanyId = (params = {}): Promise<Result> => {
  return get(`${baseURL}/${prefix}/getCampPeriodsByCompanyId`, params, HeaderType.AUTH.code);
};
