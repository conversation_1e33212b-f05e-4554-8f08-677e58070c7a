/**
 * 用户API集中管理
 */
import { get, post } from "@/utils/http.request";
import { HeaderType } from "@/constants/enum/header-type.enum";
import { Result } from "@/model/domain/Result";

const baseURL = (process.env.VUE_APP_ENABLE_GATEWAY === "on" ? process.env.VUE_APP_MS_GATEWAY_API + process.env.VUE_APP_ADMIN_API : process.env.VUE_APP_ADMIN_API) + process.env.VUE_APP_API_PREFIX;
const prefix = "user";

/**
 * @Description 获取用户手机号
 */
export const getUserPhone = (data = {}): Promise<Result> => {
  return get(`${baseURL}/${prefix}/get-user-phone`, data, HeaderType.AUTH.code);
};
