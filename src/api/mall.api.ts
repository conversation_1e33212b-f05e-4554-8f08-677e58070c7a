import { get, post } from "@/utils/http.request";
import { HeaderType } from "@/constants/enum/header-type.enum";
import { Result } from "@/model/domain/Result";

const baseURL = (process.env.VUE_APP_ENABLE_GATEWAY === "on" ? process.env.VUE_APP_MS_GATEWAY_API + process.env.VUE_APP_MALL_API : process.env.VUE_APP_MALL_API) + process.env.VUE_APP_API_PREFIX;
const prefix = "mall";

/**
 * @Description: 分页查询商品列表
 */
export const getProductList = (data = {}): Promise<Result> => {
  return post(`${baseURL}/${prefix}/product/page`, data, HeaderType.AUTH.code);
};

/**
 * @Description: 获取商品详情
 */
export const getProductDetail = (productId: string): Promise<Result> => {
  return get(`${baseURL}/${prefix}/product/${productId}`, {}, HeaderType.AUTH.code);
};

/**
 * @Description: 根据分类查询商品
 */
export const getProductsByCategory = (categoryId: number): Promise<Result> => {
  return get(`${baseURL}/${prefix}/product/category/${categoryId}`, {}, HeaderType.AUTH.code);
};

/**
 * @Description: 搜索商品
 */
export const searchProducts = (keyword: string): Promise<Result> => {
  return get(`${baseURL}/${prefix}/product/search`, { keyword }, HeaderType.AUTH.code);
};

/**
 * @Description: 获取推荐商品
 */
export const getRecommendProducts = (limit: number = 10): Promise<Result> => {
  return get(`${baseURL}/${prefix}/recommend-product/list`, { limit }, HeaderType.AUTH.code);
};

/**
 * @Description: 获取分类树形结构
 */
export const getCategoryTree = (): Promise<Result> => {
  return get(`${baseURL}/${prefix}/category/tree`, {}, HeaderType.AUTH.code);
};

/**
 * @Description: 获取一级分类列表
 */
export const getTopCategories = (): Promise<Result> => {
  return get(`${baseURL}/${prefix}/category/top`, {}, HeaderType.AUTH.code);
};

/**
 * @Description: 根据父分类ID获取子分类列表
 */
export const getCategoriesByParentId = (parentId: number): Promise<Result> => {
  return get(`${baseURL}/${prefix}/category/children/${parentId}`, {}, HeaderType.AUTH.code);
};

/**
 * @Description: 添加商品到购物车
 */
export const addToCart = (data: {
  customerId: string
  productId: string
  name: string
  spec?: string
  price: string | number  // 支持字符串和数字类型，后端会自动转换
  quantity: number
  image: string
  selected?: number
}): Promise<Result> => {
  return post(`${baseURL}/${prefix}/cart/add`, data, HeaderType.AUTH.code);
};

/**
 * @Description: 获取购物车列表
 */
export const getCartList = (customerId: string): Promise<Result> => {
  return get(`${baseURL}/${prefix}/cart/list/${customerId}`, {}, HeaderType.AUTH.code);
};

/**
 * @Description: 更新购物车商品数量
 */
export const updateCartQuantity = (data: {
  cartItemId: string
  customerId: string
  quantity: number
}): Promise<Result> => {
  return post(`${baseURL}/${prefix}/cart/update-quantity`, data, HeaderType.AUTH.code);
};

/**
 * @Description: 删除购物车商品
 */
export const removeCartItem = (cartItemId: string, customerId: string): Promise<Result> => {
  return post(`${baseURL}/${prefix}/cart/remove/${cartItemId}`, { customerId }, HeaderType.AUTH.code);
};

/**
 * @Description: 批量删除购物车商品
 */
export const batchRemoveCartItems = (data: {
  customerId: string
  cartItemIds: string[]
}): Promise<Result> => {
  return post(`${baseURL}/${prefix}/cart/batch-remove`, data, HeaderType.AUTH.code);
};

/**
 * @Description: 从购物车提交订单
 */
export const submitOrderFromCart = (data: {
  customerId: string
  addressId: string
  paymentMethod: string
  deliveryFee?: number
  appId: string
  code: string
  requestId: string
  cartItemIds: string[]
  cartItems: Array<{
    productId: string
    name: string
    spec?: string
    price: number
    quantity: number
    image: string
  }>
}): Promise<Result> => {
  return post(`${baseURL}/${prefix}/cart/submit-order`, data, HeaderType.AUTH.code);
};



/**
 * @Description: 批量选择购物车商品
 */
export const batchSelectCartItems = (data: {
  customerId: string
  cartItemIds: string[]
  selected: number
}): Promise<Result> => {
  return post(`${baseURL}/${prefix}/cart/batch-update-selected`, data, HeaderType.AUTH.code);
};

/**
 * @Description: 全选/取消全选购物车
 */
export const selectAllCartItems = (customerId: string, selected: number): Promise<Result> => {
  return post(`${baseURL}/${prefix}/cart/update-all-selected`, { customerId, selected }, HeaderType.AUTH.code);
};

/**
 * @Description: 获取购物车商品数量
 */
export const getCartItemCount = (customerId: string): Promise<Result> => {
  return get(`${baseURL}/${prefix}/cart/count/${customerId}`, {}, HeaderType.AUTH.code);
};

/**
 * @Description: 分页查询订单列表
 */
export const getOrderList = (data: {
  customerId: string
  orderStatus?: string
  pageNum?: number
  pageSize?: number
}): Promise<Result> => {
  return post(`${baseURL}/${prefix}/order/page`, data, HeaderType.AUTH.code);
};

/**
 * @Description: 获取订单详情
 */
export const getOrderDetail = (orderId: string, customerId: string): Promise<Result> => {
  return get(`${baseURL}/${prefix}/order/${orderId}`, { customerId }, HeaderType.AUTH.code);
};

/**
 * @Description: 取消订单
 */
export const cancelOrder = (orderId: string, customerId: string): Promise<Result> => {
  return post(`${baseURL}/${prefix}/order/cancel`, { orderId, customerId }, HeaderType.AUTH.code);
};

/**
 * @Description: 支付订单
 */
export const payOrder = (orderId: string, customerId: string, appId: string, openid: string): Promise<Result> => {
  return post(`${baseURL}/${prefix}/order/pay`, { orderId, customerId, appId, openid }, HeaderType.AUTH.code);
};

/**
 * @Description: 重新发起支付
 */
export const repayOrder = (orderId: string, customerId: string, appId: string, openid: string): Promise<Result> => {
  return post(`${baseURL}/${prefix}/order/repay`, { orderId, customerId, appId, openid }, HeaderType.AUTH.code);
};

/**
 * @Description: 确认收货
 */
export const confirmOrder = (orderId: string, customerId: string): Promise<Result> => {
  return post(`${baseURL}/${prefix}/order/confirm`, { orderId, customerId }, HeaderType.AUTH.code);
};

/**
 * @Description: 获取订单支付状态
 */
export const getOrderPaymentStatus = (orderId: string, customerId: string, appId: string): Promise<Result> => {
  const params: any = { customerId, appId };
  return get(`${baseURL}/${prefix}/order/payment-status/${orderId}`, params, HeaderType.AUTH.code);
};

// ==================== 地址管理相关接口 ====================

/**
 * @Description: 获取用户地址列表
 */
export const getAddressList = (customerId: string): Promise<Result> => {
  return get(`${baseURL}/${prefix}/address/list/${customerId}`, {}, HeaderType.AUTH.code);
};

/**
 * @Description: 获取默认地址
 */
export const getDefaultAddress = (customerId: string): Promise<Result> => {
  return get(`${baseURL}/${prefix}/address/default/${customerId}`, {}, HeaderType.AUTH.code);
};

/**
 * @Description: 获取地址详情
 */
export const getAddressDetail = (addressId: string, customerId: string): Promise<Result> => {
  return get(`${baseURL}/${prefix}/address/${addressId}`, { customerId }, HeaderType.AUTH.code);
};

/**
 * @Description: 保存地址（新增或更新）
 */
export const saveAddress = (data: {
  id?: string
  customerId: string
  name: string
  phone: string
  province: string
  city: string
  district: string
  detail: string
  tag?: string
  isDefault?: number
}): Promise<Result> => {
  return post(`${baseURL}/${prefix}/address/save`, data, HeaderType.AUTH.code);
};

/**
 * @Description: 删除地址
 */
export const deleteAddress = (addressId: string, customerId: string): Promise<Result> => {
  return post(`${baseURL}/${prefix}/address/delete/${addressId}`, { customerId }, HeaderType.AUTH.code);
};

/**
 * @Description: 设置默认地址
 */
export const setDefaultAddress = (addressId: string, customerId: string): Promise<Result> => {
  return post(`${baseURL}/${prefix}/address/set-default/${addressId}`, { customerId }, HeaderType.AUTH.code);
};



/**
 * @Description: 添加收藏
 */
export const addFavorite = (customerId: string, productId: string): Promise<Result> => {
  console.log("前端API - 添加收藏请求");
  console.log("前端API - customerId:", customerId, typeof customerId);
  console.log("前端API - productId:", productId, typeof productId);
  console.log("前端API - baseURL:", baseURL);
  console.log("前端API - prefix:", prefix);

  // 使用 encodeURIComponent 确保参数正确编码
  const encodedCustomerId = encodeURIComponent(customerId);
  const encodedProductId = encodeURIComponent(productId);
  const url = `${baseURL}/${prefix}/favorite/add?customerId=${encodedCustomerId}&productId=${encodedProductId}`;

  console.log("前端API - 编码后的参数:");
  console.log("前端API - encodedCustomerId:", encodedCustomerId);
  console.log("前端API - encodedProductId:", encodedProductId);
  console.log("前端API - 最终请求URL:", url);

  return post(url, {}, HeaderType.AUTH.code);
};

/**
 * @Description: 取消收藏
 */
export const removeFavorite = (customerId: string, productId: string): Promise<Result> => {
  return post(`${baseURL}/${prefix}/favorite/remove?customerId=${customerId}&productId=${productId}`, {}, HeaderType.AUTH.code);
};

/**
 * @Description: 检查是否已收藏
 */
export const checkFavorite = (customerId: string, productId: string): Promise<Result> => {
  return get(`${baseURL}/${prefix}/favorite/check`, { customerId, productId }, HeaderType.AUTH.code);
};

/**
 * @Description: 获取收藏列表
 */
export const getFavoriteList = (customerId: string, pageNum: number = 1, pageSize: number = 10): Promise<Result> => {
  return get(`${baseURL}/${prefix}/favorite/list/${customerId}`, { pageNum, pageSize }, HeaderType.AUTH.code);
};

/**
 * @Description: 批量取消收藏
 */
export const batchRemoveFavorites = (customerId: string, productIds: string[]): Promise<Result> => {
  return post(`${baseURL}/${prefix}/favorite/batch-remove?customerId=${customerId}`, productIds, HeaderType.AUTH.code);
};

/**
 * @Description: 获取收藏数量
 */
export const getFavoriteCount = (customerId: string): Promise<Result> => {
  return get(`${baseURL}/${prefix}/favorite/count/${customerId}`, {}, HeaderType.AUTH.code);
};
