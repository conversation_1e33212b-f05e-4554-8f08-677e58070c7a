import { get, post } from "@/utils/http.request";
import { HeaderType } from "@/constants/enum/header-type.enum";
import { Result } from "@/model/domain/Result";

const baseURL = (process.env.VUE_APP_ENABLE_GATEWAY === "on" ? process.env.VUE_APP_MS_GATEWAY_API + process.env.VUE_APP_MALL_API : process.env.VUE_APP_MALL_API) + process.env.VUE_APP_API_PREFIX;
const prefix = "mall";

/**
 * @Description: 添加浏览足迹
 */
export const addFootprint = (customerId: string, productId: string): Promise<Result> => {
  return post(`${baseURL}/${prefix}/footprint/add`, { customerId, productId }, HeaderType.AUTH.code);
};

/**
 * @Description: 获取浏览足迹列表
 */
export const getFootprintList = (customerId: string, pageNum = 1, pageSize = 10): Promise<Result> => {
  return get(`${baseURL}/${prefix}/footprint/list/${customerId}`, { pageNum, pageSize }, HeaderType.AUTH.code);
};

/**
 * @Description: 删除浏览足迹
 */
export const removeFootprint = (customerId: string, productId: string): Promise<Result> => {
  return post(`${baseURL}/${prefix}/footprint/remove`, { customerId, productId }, HeaderType.AUTH.code);
};

/**
 * @Description: 批量删除浏览足迹
 */
export const batchRemoveFootprints = (customerId: string, productIds: string[]): Promise<Result> => {
  return post(`${baseURL}/${prefix}/footprint/batch-remove`, { customerId, productIds }, HeaderType.AUTH.code);
};

/**
 * @Description: 清空浏览足迹
 */
export const clearFootprints = (customerId: string): Promise<Result> => {
  return post(`${baseURL}/${prefix}/footprint/clear/${customerId}`, {}, HeaderType.AUTH.code);
};

/**
 * @Description: 获取浏览足迹数量
 */
export const getFootprintCount = (customerId: string): Promise<Result> => {
  return get(`${baseURL}/${prefix}/footprint/count/${customerId}`, {}, HeaderType.AUTH.code);
};

/**
 * @Description: 添加搜索历史
 */
export const addSearchHistory = (customerId: string, keyword: string): Promise<Result> => {
  return post(`${baseURL}/${prefix}/search-history/add`, { customerId, keyword }, HeaderType.AUTH.code);
};

/**
 * @Description: 获取搜索历史
 */
export const getSearchHistory = (customerId: string, limit = 10): Promise<Result> => {
  return get(`${baseURL}/${prefix}/search-history/list/${customerId}`, { limit }, HeaderType.AUTH.code);
};

/**
 * @Description: 删除搜索历史
 */
export const removeSearchHistory = (customerId: string, keyword: string): Promise<Result> => {
  return post(`${baseURL}/${prefix}/search-history/remove`, { customerId, keyword }, HeaderType.AUTH.code);
};

/**
 * 清空搜索历史
 */
export const clearSearchHistory = (customerId: string): Promise<Result> => {
  return post(`${baseURL}/${prefix}/search-history/clear/${customerId}`, {}, HeaderType.AUTH.code);
};

/**
 * @Description: 获取热门搜索关键词
 */
export const getHotKeywords = (limit = 10): Promise<Result> => {
  return get(`${baseURL}/${prefix}/search-history/hot-keywords`, { limit }, HeaderType.AUTH.code);
};

/**
 * @Description: 获取用户钱包信息
 */
export const getWalletInfo = (customerId: string): Promise<Result> => {
  return get(`${baseURL}/${prefix}/wallet/${customerId}`, {}, HeaderType.AUTH.code);
};

/**
 * 充值
 */
export const rechargeWallet = (params: { customerId: string; amount: number; type: string; description?: string }): Promise<Result> => {
  return post(`${baseURL}/${prefix}/wallet/recharge`, params, HeaderType.AUTH.code);
};

/**
 * 支付
 */
export const walletPayment = (params: { customerId: string; amount: number; type: string; description?: string; orderId?: string }): Promise<Result> => {
  return post(`${baseURL}/${prefix}/wallet/payment`, params, HeaderType.AUTH.code);
};

/**
 * 退款
 */
export const walletRefund = (params: { customerId: string; amount: number; type: string; description?: string; orderId?: string }): Promise<Result> => {
  return post(`${baseURL}/${prefix}/wallet/refund`, params, HeaderType.AUTH.code);
};

/**
 * 检查余额是否充足
 */
export const checkWalletBalance = (customerId: string, amount: number): Promise<Result> => {
  return get(`${baseURL}/${prefix}/wallet/check-balance`, { customerId, amount }, HeaderType.AUTH.code);
};

/**
 * 获取交易记录
 */
export const getTransactionHistory = (params: { customerId: string; type?: string; pageNum?: number; pageSize?: number }): Promise<Result> => {
  return post(`${baseURL}/${prefix}/wallet/transaction-history`, params, HeaderType.AUTH.code);
};

// ==================== 优惠券相关API ====================

/**
 * 获取可领取的优惠券列表
 */
export const getAvailableCoupons = (): Promise<Result> => {
  return get(`${baseURL}/${prefix}/coupon/available`, {}, HeaderType.AUTH.code);
};

/**
 * 领取优惠券
 */
export const receiveCoupon = (customerId: string, couponId: string): Promise<Result> => {
  return post(`${baseURL}/${prefix}/coupon/receive?customerId=${customerId}&couponId=${couponId}`, {}, HeaderType.AUTH.code);
};

/**
 * 获取用户优惠券列表
 */
export const getUserCoupons = (customerId: string, status?: string, pageNum = 1, pageSize = 10): Promise<Result> => {
  return get(`${baseURL}/${prefix}/coupon/user-coupons/${customerId}`, { status, pageNum, pageSize }, HeaderType.AUTH.code);
};

/**
 * 获取用户优惠券统计
 */
export const getUserCouponsStats = (customerId: string): Promise<Result> => {
  return get(`${baseURL}/${prefix}/coupon/user-coupons-stats/${customerId}`, {}, HeaderType.AUTH.code);
};

/**
 * 获取可用于订单的优惠券
 */
export const getAvailableCouponsForOrder = (customerId: string, orderAmount: number): Promise<Result> => {
  return get(`${baseURL}/${prefix}/coupon/available-for-order`, { customerId, orderAmount }, HeaderType.AUTH.code);
};

/**
 * 使用优惠券
 */
export const useCoupon = (customerCouponId: string, orderId: string): Promise<Result> => {
  return post(`${baseURL}/${prefix}/coupon/use`, { customerCouponId, orderId }, HeaderType.AUTH.code);
};

/**
 * 计算优惠券优惠金额
 */
export const calculateCouponDiscount = (customerCouponId: string, orderAmount: number): Promise<Result> => {
  return get(`${baseURL}/${prefix}/coupon/calculate-discount`, { customerCouponId, orderAmount }, HeaderType.AUTH.code);
};

// ==================== 用户统计相关API ====================

/**
 * 获取用户统计信息
 */
export const getUserStats = (customerId: string): Promise<Result> => {
  return get(`${baseURL}/${prefix}/customer-stats/${customerId}`, {}, HeaderType.AUTH.code);
};

/**
 * 初始化用户统计信息
 */
export const initUserStats = (customerId: string): Promise<Result> => {
  return post(`${baseURL}/${prefix}/customer-stats/init/${customerId}`, {}, HeaderType.AUTH.code);
};

/**
 * 更新积分
 */
export const updateUserPoints = (customerId: string, increment: number): Promise<Result> => {
  return post(`${baseURL}/${prefix}/customer-stats/update-points`, { customerId, increment }, HeaderType.AUTH.code);
};

/**
 * 刷新用户统计数据
 */
export const refreshUserStats = (customerId: string): Promise<Result> => {
  return post(`${baseURL}/${prefix}/customer-stats/refresh/${customerId}`, {}, HeaderType.AUTH.code);
};

// ==================== 商品评价相关接口 ====================

/**
 * 提交订单评价
 */
export const submitOrderReview = (data: { orderId: string; customerId: string; rating: number; content: string; images?: string[]; anonymous?: boolean; tagIds?: string[] }): Promise<Result> => {
  return post(`${baseURL}/${prefix}/review/order-review/submit`, data, HeaderType.AUTH.code);
};

/**
 * 获取订单评价列表
 */
export const getOrderReviews = (orderId: string): Promise<Result> => {
  return get(`${baseURL}/${prefix}/review/order-review/list/${orderId}`, HeaderType.AUTH.code);
};

/**
 * 获取商品评价列表
 */
export const getProductReviews = (productId: string, pageNum = 1, pageSize = 10): Promise<Result> => {
  return get(`${baseURL}/${prefix}/review/product-review/list`, { productId, pageNum, pageSize }, HeaderType.AUTH.code);
};

/**
 * 获取用户的评价列表
 */
export const getUserReviews = (customerId: string, pageNum = 1, pageSize = 10): Promise<Result> => {
  return get(`${baseURL}/${prefix}/review/user-review/list`, { customerId, pageNum, pageSize }, HeaderType.AUTH.code);
};

/**
 * 删除评价
 */
export const deleteReview = (reviewId: string, customerId: string): Promise<Result> => {
  return post(`${baseURL}/${prefix}/review/delete`, { reviewId, customerId }, HeaderType.AUTH.code);
};

/**
 * 检查订单是否已评价
 */
export const checkOrderReviewed = (orderId: string, customerId: string): Promise<Result> => {
  return get(`${baseURL}/${prefix}/review/order-review/check`, { orderId, customerId }, HeaderType.AUTH.code);
};

/**
 * 获取评价标签列表
 */
export const getReviewTags = (): Promise<Result> => {
  return get(`${baseURL}/${prefix}/review/tags`, {}, HeaderType.AUTH.code);
};

/**
 * 上传评价图片
 */
export const uploadReviewImage = (file: File): Promise<Result> => {
  const formData = new FormData();
  formData.append("file", file);
  formData.append("path", "review-images");

  // 使用business服务的上传接口
  const businessBaseURL = (process.env.VUE_APP_ENABLE_GATEWAY === "on" ? process.env.VUE_APP_MS_GATEWAY_API + process.env.VUE_APP_BUSINESS_API : process.env.VUE_APP_BUSINESS_API) + process.env.VUE_APP_API_PREFIX;

  return post(`${businessBaseURL}/video_upload/upload`, formData, HeaderType.FORM.code);
};
