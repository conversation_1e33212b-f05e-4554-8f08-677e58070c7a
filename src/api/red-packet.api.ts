/**
 * 奖励API集中管理
 */
import { get, post } from "@/utils/http.request";
import { HeaderType } from "@/constants/enum/header-type.enum";
import { Result } from "@/model/domain/Result";
const baseURL = (process.env.VUE_APP_ENABLE_GATEWAY === "on" ? process.env.VUE_APP_MS_GATEWAY_API + process.env.VUE_APP_BUSINESS_API : process.env.VUE_APP_BUSINESS_API) + process.env.VUE_APP_API_PREFIX;
const prefix = "course-redpacket";

/**
 * @Description 获取客户奖励汇总信息
 */
export const getCustomerRedPacketSummary = (data: { customerId: string | number }): Promise<Result> => {
  return get(`${baseURL}/${prefix}/summary/${data.customerId}`, {}, HeaderType.AUTH.code);
};
