import { get, post } from "@/utils/http.request";
import { HeaderType } from "@/constants/enum/header-type.enum";
import { Result } from "@/model/domain/Result";
const baseURL = (process.env.VUE_APP_ENABLE_GATEWAY === "on" ? process.env.VUE_APP_MS_GATEWAY_API + process.env.VUE_APP_ADMIN_API : process.env.VUE_APP_ADMIN_API) + process.env.VUE_APP_API_PREFIX;
const prefix = "customer-assignment";

/**
 * @Description: 客户分配记录
 * <AUTHOR>
 * @date 2025/4/19
 * @time 15:41
 */
export const getCustomerAssignmentList = (data = {}): Promise<Result> => {
  return get(`${baseURL}/${prefix}/page`, data, HeaderType.AUTH.code);
};
