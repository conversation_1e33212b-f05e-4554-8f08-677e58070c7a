/**
 * 公司API集中管理
 */
import { get, post } from "@/utils/http.request";
import { HeaderType } from "@/constants/enum/header-type.enum";
import { Result } from "@/model/domain/Result";

const baseURL = (process.env.VUE_APP_ENABLE_GATEWAY === "on" ? process.env.VUE_APP_MS_GATEWAY_API + process.env.VUE_APP_ADMIN_API : process.env.VUE_APP_ADMIN_API) + process.env.VUE_APP_API_PREFIX;
const prefix = "company";
const suffix = "headquarters";
/**
 * @Description 获取公司下销售组
 */
export const getCompanyGroup = (data = {}): Promise<Result> => {
  return get(`${baseURL}/${prefix}/get-company-group`, data, HeaderType.AUTH.code);
};
/**
 * @Description 查询总部树形结构
 */
export const queryHeadquartersCompanyList = (data = {}): Promise<Result> => {
  return get(`${baseURL}/${suffix}/tree`, data, HeaderType.AUTH.code);
};
