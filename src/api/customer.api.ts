/**
 * 课程API集中管理
 */
import { get, post } from "@/utils/http.request";
import { HeaderType } from "@/constants/enum/header-type.enum";
import { Result } from "@/model/domain/Result";
const baseURL = (process.env.VUE_APP_ENABLE_GATEWAY === "on" ? process.env.VUE_APP_MS_GATEWAY_API + process.env.VUE_APP_ADMIN_API : process.env.VUE_APP_ADMIN_API) + process.env.VUE_APP_API_PREFIX;
const prefix = "customer";

/**
 * @Description 更新课程观看信息
 */
export const updCourseCustomerRel = (data = {}): Promise<Result> => {
  return post(`${baseURL}/${prefix}/upd-course-customer-rel`, data, HeaderType.AUTH.code);
};

/**
 * @Description 获取客户课程信息
 */
export const getCourseVideo = (data = {}): Promise<Result> => {
  return post(`${baseURL}/${prefix}/get-course-video`, data, HeaderType.AUTH.code);
};

/**
 * @Description 提交答题 submit-answer
 */
export const submitAnswer = (data = {}): Promise<Result> => {
  return post(`${baseURL}/${prefix}/submit-answer`, data, HeaderType.AUTH.code);
};
/**
 * @Description 更新手机号 update-mobile
 */
export const updateMobile = (data = {}): Promise<Result> => {
  return post(`${baseURL}/${prefix}/update-mobile`, data, HeaderType.AUTH.code);
};
/**
 * @Description: 更新课程观看时长
 * <AUTHOR>
 * @date 2025/6/6
 * @time 22:15
 */
export const updCourseDuration = (data = {}): Promise<Result> => {
  return post(`${baseURL}/${prefix}/upd-course-duration`, data, HeaderType.AUTH.code);
};
export const queryCustomerPage = (data = {}, query = {}): Promise<Result> => {
  return post(`${baseURL}/${prefix}/query/page`, data, HeaderType.AUTH.code, undefined, query);
};
export const updCustomerStatus = (data = {}): Promise<Result> => {
  return post(`${baseURL}/${prefix}/upd-status`, data, HeaderType.AUTH.code);
};
export const delCustomer = (data = {}): Promise<Result> => {
  return post(`${baseURL}/${prefix}/del-customer`, data, HeaderType.AUTH.code);
};

/**
 * @Description 删除客户账号
 */
export const deleteCustomer = (data = {}): Promise<Result> => {
  return post(`${baseURL}/${prefix}/del-customer`, data, HeaderType.AUTH.code);
};
