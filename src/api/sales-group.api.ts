import { get, post } from "@/utils/http.request";
import { HeaderType } from "@/constants/enum/header-type.enum";
import { Result } from "@/model/domain/Result";

const baseURL = (process.env.VUE_APP_ENABLE_GATEWAY === "on" ? process.env.VUE_APP_MS_GATEWAY_API + process.env.VUE_APP_ADMIN_API : process.env.VUE_APP_ADMIN_API) + process.env.VUE_APP_API_PREFIX;

const prefix = "sales-group";

/**
 * @description: 分页查询销售组
 */
export const querySalesGroupByPage = (data = {}): Promise<Result> => {
  return post(`${baseURL}/${prefix}/page`, data, HeaderType.AUTH.code);
};

/**
 * @description: 新增销售组
 */
export const saveSalesGroup = (data = {}): Promise<Result> => {
  return post(`${baseURL}/${prefix}`, data, HeaderType.AUTH.code);
};

/**
 * @description: 更新销售组
 */
export const updateSalesGroup = (data = {}): Promise<Result> => {
  return post(`${baseURL}/${prefix}`, data, HeaderType.AUTH.code);
};

/**
 * @description: 删除销售组信息
 */
export const deleteSalesGroup = (data = {}): Promise<Result> => {
  return post(`${baseURL}/${prefix}/delete`, data, HeaderType.AUTH.code);
};

/**
 * @description: 更新销售组状态
 */
export const updateSalesGroupStatus = (data = {}): Promise<Result> => {
  return post(`${baseURL}/${prefix}/update-status`, data, HeaderType.AUTH.code);
};

/**
 * @description: 根据公司ID查询销售组列表
 */
export const listByCompanyId = (params = {}): Promise<Result> => {
  return get(`${baseURL}/${prefix}/list-by-company-id`, params, HeaderType.AUTH.code);
};
