import { get, post } from "@/utils/http.request";
import { HeaderType } from "@/constants/enum/header-type.enum";
import { Result } from "@/model/domain/Result";

const baseURL = (process.env.VUE_APP_ENABLE_GATEWAY === "on" ? process.env.VUE_APP_MS_GATEWAY_API + process.env.VUE_APP_MALL_API : process.env.VUE_APP_MALL_API) + process.env.VUE_APP_API_PREFIX;
const prefix = "app";

/**
 * @Description: 小程序首页
 * @pram { index: number } 1-首页轮播，2-消息通知，3-个人中心
 */
export const getIndexData = (data = {}): Promise<Result> => {
    return get(`${baseURL}/${prefix}/banners/type/${data.index}`, {}, HeaderType.BASE.code);
};