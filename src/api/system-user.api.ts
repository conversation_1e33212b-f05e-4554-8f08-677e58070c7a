/**
 * 用户API集中管理
 */
import { get, post } from "@/utils/http.request";
import { HeaderType } from "@/constants/enum/header-type.enum";
import { Result } from "@/model/domain/Result";
const baseURL = (process.env.VUE_APP_ENABLE_GATEWAY === "on" ? process.env.VUE_APP_MS_GATEWAY_API + process.env.VUE_APP_ADMIN_API : process.env.VUE_APP_ADMIN_API) + process.env.VUE_APP_API_PREFIX;
const prefix = "system-user";

/**
 * @Description 用户提交申请信息
 */
export const updateWxUser = (data = {}): Promise<Result> => {
  return post(`${baseURL}/${prefix}/wx-update-user`, data, HeaderType.AUTH.code);
};
export const getUserInfo = (data = {}): Promise<Result> => {
  return get(`${baseURL}/${prefix}/get-user`, data, HeaderType.AUTH.code);
};
export const queryUserByPage = (data = {}): Promise<Result> => {
  return post(`${baseURL}/${prefix}/query-user-by-page`, data, HeaderType.AUTH.code);
}
export const resetUnionId = (data = {}): Promise<Result> => {
  return post(`${baseURL}/${prefix}/update-union-id`, data, HeaderType.AUTH.code);
}