import { get, post } from "@/utils/http.request";
import { HeaderType } from "@/constants/enum/header-type.enum";
import { Result } from "@/model/domain/Result";

const baseURL = (process.env.VUE_APP_ENABLE_GATEWAY === "on" ? process.env.VUE_APP_MS_GATEWAY_API + process.env.VUE_APP_ADMIN_API : process.env.VUE_APP_ADMIN_API) + process.env.VUE_APP_API_PREFIX;

const prefix = "customer-tags";

/**
 * @Description: 保存手动标签
 */
export const saveManualTag = (data = {}): Promise<Result> => {
  return post(`${baseURL}/${prefix}/save-manual-tag`, data, HeaderType.AUTH.code);
};
