/**
 * 用户登录API集中管理
 */
import { get, post } from "@/utils/http.request";
import { HeaderType } from "@/constants/enum/header-type.enum";
import { Result } from "@/model/domain/Result";
import { envVersion, getStorageEnv } from "@/utils/config";
import { Code } from "@/constants/enum/code.enum";
const baseURL = process.env.VUE_APP_ENABLE_GATEWAY === "on" ? process.env.VUE_APP_MS_GATEWAY_API + process.env.VUE_APP_AUTH_API : process.env.VUE_APP_AUTH_API;

const authPrefix = "auth";

/**
 * @Description: 登录
 */
export const login = (params: any, header?: any): Promise<Result> => {
  return post(`${baseURL}/${authPrefix}/login`, params, HeaderType.AUTH.code, header);
};

/**
 * 业务登录
 */
export const businessLogin = (params: any, header?: any): Promise<Result> => {
  return post(`${baseURL}/${authPrefix}/web-login`, params, HeaderType.AUTH.code, header).then((res: any) => {
    if (res.code === Code.OK.code) {
      // 存储用户信息
      saveData(res);
    }
    return res;
  });
};

/**
 * 获取当前小程序的appid
 * @returns string 当前小程序的appid
 */
export const getAppId = (): string => {
  try {
    const accountInfo = uni.getAccountInfoSync();
    return accountInfo.miniProgram.appId;
  } catch (error) {
    console.error("获取appid失败：", error);
    uni.showToast({
      title: "APPID获取失败，请尝试重新进入小程序",
      icon: "none",
    });
    return "";
  }
};

export const handleLogin = async (userType?: string): Promise<any> => {
  try {
    // 获取微信code
    const vxCodeRes = await getWechatLoginCode();
    const wxUserInfo = await uni.getStorageSync("wxUserInfo");
    const storedUserInfo = await uni.getStorageSync("userInfo");
    const userInfo = storedUserInfo && typeof storedUserInfo === "object" ? { ...storedUserInfo } : {};
    console.error("wxUserInfo", wxUserInfo);
    // 调用业务登录接口
    const loginResp = await login({
      userType: userType ? userType : "1",
      registerCode: vxCodeRes.code,
      appId: getAppId(),
      nickname: wxUserInfo?.nickname,
      avatarUrl: wxUserInfo?.headimgurl,
      gender: wxUserInfo?.sex,
      unionId: wxUserInfo.unionid,
      openid: wxUserInfo.openid,
      columnId: userInfo?.columnId,
      companyId: userInfo?.companyId,
      campPeriodId: userInfo?.campPeriodId,
      salesGroupId: userInfo?.salesGroupId,
      salesId: userInfo?.salesId,
      courseId: userInfo?.courseId,
    });
    if (loginResp.code !== Code.OK.code) {
      throw new Error(loginResp.msg);
    }
    saveData(loginResp);
    return loginResp.data.userInfoResponse;
  } catch (error) {
    console.error("登录流程失败:", error);
    uni.showToast({
      title: error.message || "登录失败，请重试",
      icon: "none",
    });
    throw error;
  }
};

// 封装微信登录逻辑
export const getWechatLoginCode = (): Promise<any> => {
  return new Promise((resolve, reject) => {
    uni.login({
      success: resolve,
      fail: (err) => {
        console.error("[微信登录失败]", err);
        reject(err);
      },
    });
  });
};

export const saveData = (data: any): void => {
  console.log(data);
  // 存储用户信息
  const token = data.data.token;
  const expires = 7 * 24 * 60 * 60 * 1000;
  const item = {
    value: token,
    expires: Date.now() + expires,
  };
  uni.setStorageSync("token", item);
  uni.setStorageSync("userInfo", data.data.userInfoResponse);
};
