/**
 * 公司API集中管理
 */
import { get, post } from "@/utils/http.request";
import { HeaderType } from "@/constants/enum/header-type.enum";
import { Result } from "@/model/domain/Result";

const baseURL = (process.env.VUE_APP_ENABLE_GATEWAY === "on" ? process.env.VUE_APP_MS_GATEWAY_API + process.env.VUE_APP_BUSINESS_API : process.env.VUE_APP_BUSINESS_API) + process.env.VUE_APP_API_PREFIX;
const prefix = "mini-program";

/**
 * @Description 获取营期分享链接
 */
export const getCampPeriodUrl = (data = {}): Promise<Result> => {
  return post(`${baseURL}/${prefix}/get-camp-period-url`, data, HeaderType.AUTH.code);
};

/**
 * @Description: 发起微信转账
 */
export const wxPay = (data = {}): Promise<Result> => {
  return post(`${baseURL}/${prefix}/wx-pay`, data, HeaderType.AUTH.code);
};
