import { get, post } from "@/utils/http.request";
import { HeaderType } from "@/constants/enum/header-type.enum";
import { Result } from "@/model/domain/Result";
const baseURL = (process.env.VUE_APP_ENABLE_GATEWAY === "on" ? process.env.VUE_APP_MS_GATEWAY_API + process.env.VUE_APP_ADMIN_API : process.env.VUE_APP_ADMIN_API) + process.env.VUE_APP_API_PREFIX;
const prefix = "customer-course-relation";

/**
 * @Description: 根据客户id查询已参加营期信息
 * @param {Object} params - 查询参数
 * @returns {Promise<Result>}
 */
export const getCampPeriodInfoByCustomerId = (params: any): Promise<Result> => {
  return get(`${baseURL}/${prefix}/get-camp-period-info-by-customer-id`, params, HeaderType.AUTH.code);
};

/**
 * @Description: 根据客户id查询客户课程关联信息
 * @param {Object} params - 查询参数
 * @returns {Promise<Result>}
 */
export const getCourseRelationByCustomerId = (params: any): Promise<Result> => {
  return get(`${baseURL}/${prefix}/get-course-relation-by-customer-id`, params, HeaderType.AUTH.code);
};
