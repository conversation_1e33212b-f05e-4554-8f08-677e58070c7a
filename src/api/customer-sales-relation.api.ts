import { get, post } from "@/utils/http.request";
import { HeaderType } from "@/constants/enum/header-type.enum";
import { Result } from "@/model/domain/Result";

const baseURL = (process.env.VUE_APP_ENABLE_GATEWAY === "on" ? process.env.VUE_APP_MS_GATEWAY_API + process.env.VUE_APP_ADMIN_API : process.env.VUE_APP_ADMIN_API) + process.env.VUE_APP_API_PREFIX;

const prefix = "customer-sales-relation";

/**
 * @Description: 根据客户ID获取栏目信息
 */
export const listByCustomerId = (params: any): Promise<Result> => {
  return get(`${baseURL}/${prefix}/list-by-customer-id`, params, HeaderType.AUTH.code);
};
/**
 * @Description: 分配客户给销售人员
 */
export const assignCustomer = (params: any): Promise<Result> => {
  return post(`${baseURL}/${prefix}/assign`, params, HeaderType.AUTH.code);
};
