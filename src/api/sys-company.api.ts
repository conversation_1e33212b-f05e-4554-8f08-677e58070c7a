import { get, post } from "@/utils/http.request";
import { HeaderType } from "@/constants/enum/header-type.enum";
import { Result } from "@/model/domain/Result";

const baseURL = (process.env.VUE_APP_ENABLE_GATEWAY === "on" ? process.env.VUE_APP_MS_GATEWAY_API + process.env.VUE_APP_ADMIN_API : process.env.VUE_APP_ADMIN_API) + process.env.VUE_APP_API_PREFIX;

const companyPrefix = "company";
const headquartersPrefix = "headquarters";

/**
 * @description: 查询公司下所有部门
 */
export const queryCompanyListByCompanyId = (params = {}): Promise<Result> => {
  return get(`${baseURL}/${companyPrefix}/get-company-group`, params, HeaderType.AUTH.code);
};

/**
 * @description: 新增公司
 */
export const saveCompany = (params = {}): Promise<Result> => {
  return get(`${baseURL}/${companyPrefix}`, params, HeaderType.AUTH.code);
};

/**
 * @description: 更新公司
 */
export const updateCompany = (params = {}): Promise<Result> => {
  return get(`${baseURL}/${companyPrefix}`, params, HeaderType.AUTH.code);
};

/**
 * @description: 查询总部树形结构
 */
export const queryHeadquartersCompanyList = (params = {}): Promise<Result> => {
  return get(`${baseURL}/${headquartersPrefix}/tree`, params, HeaderType.AUTH.code);
};

/**
 * @description: 查询总部分页
 */
export const queryCompanyByPage = (data = {}): Promise<Result> => {
  return post(`${baseURL}/${companyPrefix}/page`, data, HeaderType.AUTH.code);
};

/**
 * @description: 更新公司状态
 */
export const updateCompanyStatus = (data = {}): Promise<Result> => {
  return post(`${baseURL}/${companyPrefix}/update-status`, data, HeaderType.AUTH.code);
};

/**
 * @description: 删除公司信息
 */
export const deleteCompany = (data = {}): Promise<Result> => {
  return post(`${baseURL}/${companyPrefix}/delete`, data, HeaderType.AUTH.code);
};
