/**
 * 训练营课程API集中管理
 */
import { get, post } from "@/utils/http.request";
import { HeaderType } from "@/constants/enum/header-type.enum";
import { Result } from "@/model/domain/Result";
const baseURL = (process.env.VUE_APP_ENABLE_GATEWAY === "on" ? process.env.VUE_APP_MS_GATEWAY_API + process.env.VUE_APP_BUSINESS_API : process.env.VUE_APP_BUSINESS_API) + process.env.VUE_APP_API_PREFIX;
const prefix = "camp-period";
const prefixUrl = "camp-period-data";

/**
 * @Description 获取公司下销售组
 */
export const getCourseByCompanyIdANDSalesId = (data = {}): Promise<Result> => {
  return get(`${baseURL}/${prefix}/getCourseByCompanyIdANDSalesId`, data, HeaderType.AUTH.code);
};
export const setCampPeriodRedPacketAmount = (data = {}): Promise<Result> => {
  return get(`${baseURL}/${prefix}/setCampPeriodRedPacketAmount`, data, HeaderType.AUTH.code);
};
export const queryCampCourseListData = (data = {}): Promise<Result> => {
  return post(`${baseURL}/${prefixUrl}/getCampCourseList`, data, HeaderType.AUTH.code);
};
export const queryCampPeriodData = (data = {}): Promise<Result> => {
  return post(`${baseURL}/${prefixUrl}/getCampPeriodData`, data, HeaderType.AUTH.code);
};
export const queryCampPeriodTableData = (data = {}): Promise<Result> => {
  return post(`${baseURL}/${prefixUrl}/getCampCourseData`, data, HeaderType.AUTH.code);
};
export const queryCustomerDataTableData = (data = {}): Promise<Result> => {
  return post(`${baseURL}/${prefixUrl}/getCustomerData`, data, HeaderType.AUTH.code);
};
export const queryCustomerCourseListData = (data = {}): Promise<Result> => {
  return post(`${baseURL}/${prefixUrl}/getCustomerCourseList`, data, HeaderType.AUTH.code);
};
