<template>
  <view class="empty d-flex flex-column align-items-center">
    <!--    <van-image width="156" height="175" src="/static/images/empty.png"></van-image>-->
    <view class="empty-unlogin-area" v-if="unLogin"
      ><slot>
        <view class="text">身份信息已失效，请您点击按钮重新登录</view>
        <view><van-button type="info" @click="toLogin">立即登录</van-button></view>
      </slot></view
    >
    <text class="empty-text" v-else>
      <slot>{{ emptyText }}</slot>
    </text>
  </view>
</template>

<script lang="ts">
import { Component, Prop } from "vue-property-decorator";
import Vue from "vue";

@Component({
  name: "Empty",
})
export default class Empty extends Vue {
  @Prop() unLogin = false;
  @Prop({ default: () => "暂无数据" }) emptyText = "";
  toLogin() {
    uni.showModal({
      title: "登录",
      content: "请登录后再使用系统",
      cancelText: "一键登录",
      confirmText: "账号登录",
      success: (res) => {
        if (res.confirm) {
          this.$emit("login");
        } else if (res.cancel) {
          this.$emit("refresh");
        }
      },
    });
  }
}
</script>

<style scoped>
.empty {
  padding: 32px;
  background-color: #ffffff;
  margin: 20px;
  border-radius: 15px;
  height: 50vh;
  justify-content: center;
}
.empty-text {
  padding-top: 16px;
  font-size: 16px;
  color: #999;
  background-color: #ffffff;
  font-family: PingFang SC;
  font-weight: 500;
}
.empty-unlogin-area {
  text-align: center;
  font-size: 14px;
  color: #666;
}
.empty-unlogin-area .text {
  margin: 16px 0 10px 0;
}
</style>
