<template>
  <view class="main-view" role="button" @click="onBtnClicked" @touchmove.stop.prevent="doNothing" :style="{ left: `${moveX}px`, top: `${moveY}px`, opacity: opacity }" :class="{ 'btn-trans': isEnd }" @touchstart="onBtnTouchStart($event)" @touchmove="onBtnMove($event)" @touchend="onBtnTouchEnd">
    <view class="float-btn-view">
      <text class="btn-label">营期</text>
      <text class="btn-label">数据</text>
    </view>
  </view>
</template>
<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
/**
 * @Description: 悬浮按钮
 */
@Component({
  name: "CampData",
})
export default class CampData extends Vue {
  @Prop() parent!: any; // 传入父类对象
  @Prop() text!: string; // 按钮名称
  @Prop() setY!: number; // 设定的Y值
  isEnd = false; // 是否结束拖动
  startX = 0; // 按钮开始时的X坐标
  startY = 0; // 按钮开始时的Y坐标
  moveX = 0; // 移动的X坐标
  moveY = 0; // 移动的Y坐标
  windowWidth = 0; // 窗口宽度
  windowHeight = 0; // 窗口高度
  radius = 25; // 按钮的半径长度
  opacity = 0; // 初始化时透明度为0
  mounted(): void {
    this.init();
    console.log(this.parent);
  }
  /**
   * @Description: 初始化【监听按钮】
   */
  private init(): void {
    const res = uni.getSystemInfoSync();
    this.windowWidth = res.windowWidth;
    this.windowHeight = res.windowHeight;
    this.moveX = this.windowWidth - 80;
    this.moveY = this.windowHeight - (this.setY || 290);
    this.opacity = 1;
  }
  /**
   * @Description: 按钮被触摸时触发
   */
  onBtnTouchStart(e: { touches: any[] }): void {
    this.startX = e.touches[0].clientX;
    this.startY = e.touches[0].clientY;
  }
  /**
   * @Description: 手在按钮上移动时触发
   */
  onBtnMove(e: { touches: any[]; stopPropagation: any }): void {
    e.stopPropagation();
    this.moveX = e.touches[0].clientX - this.radius;
    this.moveY = e.touches[0].clientY - this.radius;
  }
  /**
   * @Description: 停止触摸时触发
   */
  onBtnTouchEnd(): void {
    this.isEnd = true;
    setTimeout(() => {
      this.isEnd = false;
    }, 200);
    this.resetPosition();
  }
  /**
   * @Description: 阻止touchmove冒泡
   */
  doNothing(): void {}
  /**
   * @Description:
   */
  resetPosition(): void {
    const maxNum = 25;
    let x = 0;
    let y = 0;
    const endX = this.moveX;
    const endY = this.moveY;
    if (endX > this.windowWidth - 80) {
      x = this.windowWidth - 80;
    }
    if (endY > this.windowHeight - 70) {
      y = this.windowHeight - 70;
    }
    if (endX < maxNum) {
      x = maxNum;
    }
    if (endY < maxNum) {
      y = maxNum;
    }
    this.moveX = x || endX;
    this.moveY = y || endY;
  }
  /**
   * @Description: 监听截图按钮
   */
  onBtnClicked(): void {
    this.$emit("click");
  }
}
</script>
<style lang="scss" scoped>
.main-view {
  width: 60px;
  height: 60px;
  background: linear-gradient(to right, #0289ea, #95c4f6);
  display: inline-block;
  border-radius: 50%;
  box-shadow: 0 0 15px 6px rgb(176, 219, 237);
  cursor: pointer;
  user-select: none;
  position: absolute;
  z-index: 998;
  &:active {
    opacity: 0.8;
  }
}

.float-btn-view {
  width: 100%;
  height: 100%;
  user-select: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.btn-icon {
  width: 30px;
  height: 30px;
}

.btn-label {
  font-family: "PingFangSC-Semibold";
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
  margin-top: 4px;
}

.btn-trans {
  transition: 0.2s;
}
</style>
