<template>
  <van-dialog
    use-slot
    title="隐私协议"
    :show="showPrivacyModal"
    show-cancel-button
    confirmButtonOpenType="agreePrivacyAuthorization"
    confirmButtonText="同意"
    cancelButtonText="拒绝"
    @cancel="handleRejectPrivacy"
    @confirm="handleResolvePrivacy"
    :before-close="beforeClose"
    confirm-button-color="#1989fa"
  >
    <view class="privacy-modal-body">
      <view class="text"> 感谢您使用社群管理系统小程序。在您使用前，请认真阅读 </view>
      <view class="text doc" @click="handleOpenPrivacyContract">{{ privacyName }}</view>
      <view class="text">。 当您点击同意后，即代表您已理解并同意该条款内容，并可正常使用服务。 如您拒绝，将无法使用本服务。 </view>
    </view>
  </van-dialog>
</template>

<script lang="ts">
import { Component, Prop } from "vue-property-decorator";
import Vue from "vue";

// eslint-disable-next-line no-undef
type GetPrivacySettingSuccessCallbackResult = WechatMiniprogram.GetPrivacySettingSuccessCallbackResult;

@Component({
  name: "Empty",
})
export default class PrivacyModal extends Vue {
  showPrivacyModal = false;
  privacyName = "";

  mounted() {
    this.checkPrivacy();
  }

  checkPrivacy() {
    if (!wx.getPrivacySetting) return;
    if (!wx.openPrivacyContract) return;

    wx.getPrivacySetting({
      success: (res: GetPrivacySettingSuccessCallbackResult) => {
        console.log(res); // 返回结果为: res = { needAuthorization: true/false, privacyContractName: '《xxx隐私保护指引》' }
        if (res.needAuthorization) {
          // 需要弹出隐私协议
          this.privacyName = res.privacyContractName;
          this.showPrivacyModal = true;
          this.$emit("needPrivacy", true);
        } else {
          // 用户已经同意过隐私协议，所以不需要再弹出隐私协议，也能调用已声明过的隐私接口
          this.$emit("unNeedPrivacy", true);
        }
      },
      fail: () => {},
      complete: () => {},
    });
  }

  beforeClose() {}

  handleOpenPrivacyContract() {
    // 打开隐私协议页面
    wx.openPrivacyContract({
      success: () => {}, // 打开成功
      fail: () => {}, // 打开失败
      complete: () => {},
    });
  }

  // 点击同意, 上送 privacyResolved 事件
  handleResolvePrivacy() {
    this.showPrivacyModal = false;
    this.$emit("privacyResolved", false);
  }

  handleRejectPrivacy() {
    wx.exitMiniProgram({
      success() {},
      fail() {
        wx.showToast({
          title: "退出小程序失败, 请手动关闭小程序",
          icon: "none",
          duration: 3000,
        });
      },
    });
  }
}
</script>

<style scoped lang="scss">
.privacy-modal-body {
  padding: 20rpx 30rpx;
  .text {
    display: inline;
    line-height: 40rpx;
    font-size: 28rpx;
  }
  .doc {
    color: rgba(60, 122, 233, 1);
  }
}
</style>
