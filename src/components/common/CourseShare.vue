<template>
  <van-popup :show="_show" position="bottom" round custom-style="width:100vw; height: 200px;" @close="closePopup">
    <view class="content px-3 flex-column">
      <text class="title">分享课程</text>
      <view class="flex-row">
        <view class="card-item flex-column align-items-center" @click="onItemTap(1)">
          <button class="btn-share" open-type="share">
            <view class="icon-container">
              <van-image width="24" height="24" src="/static/images/wechat-fill.svg" />
            </view>
            <text>分享给微信好友</text>
          </button>
        </view>
        <view class="card-item flex-column align-items-center" @click="onCopyLink">
          <view class="btn-share">
            <view class="icon-container">
              <van-image width="24" height="24" src="/static/images/links-line.svg" />
            </view>
            <text>复制分享链接</text>
          </view>
        </view>
      </view>
    </view>
  </van-popup>
</template>

<script lang="ts">
import Vue from "vue";
import { Component, Prop } from "vue-property-decorator";
import { getCampPeriodUrl } from "@/api/vx.api";
@Component({
  name: "CourseShare",
})
export default class CourseShare extends Vue {
  @Prop() value = false;
  @Prop() position = "bottom";
  @Prop() shareCourseInfo!: { params: Record<string, any> };

  get _show() {
    return this.value;
  }

  closePopup(): void {
    this.$emit("input", false);
  }
  onItemTap(val: any): void {
    console.debug(val);
    this.$emit("input", false);
    // 触发分享
  }

  onCopyLink(): void {
    getCampPeriodUrl(this.shareCourseInfo.params)
      .then((response) => {
        const shareUrl = response?.data;
        uni.setClipboardData({
          data: shareUrl,
          success: () => {
            uni.showToast({ title: "链接已复制", icon: "none" });
          },
        });
      })
      .catch((error) => {
        console.error("错误:", error);
        uni.showToast({ title: "链接获取失败", icon: "none" });
      });
    this.$emit("input", false);
  }
}
</script>

<style lang="scss" scoped>
.content {
  z-index: 10;
  .title {
    color: #333;
    font-weight: 700;
    text-align: center;
    line-height: 60px;
  }
}
.card-item {
  width: 100%;
  font-size: 12px;
  color: #333;
  text-align: center; // 居中对齐
  .icon-container {
    display: flex;
    width: 50px;
    height: 50px;
    border: 1px solid rgba(204, 204, 204, 1);
    border-radius: 10px;
    justify-content: center; // 图标居中
    van-image {
      height: 24px;
      margin: auto;
    }
    &:active {
      background: #f2f2f2;
    }
  }
  text {
    line-height: 26px;
  }
}
.btn-share {
  background: transparent;
  font-size: 12px;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  &:after {
    content: none;
  }
  &::after {
    border: none;
  }
}
</style>
