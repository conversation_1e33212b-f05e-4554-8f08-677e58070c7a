<template>
  <view class="container">
    <view class="statusbar" :style="'height:' + statusBarHeight + 'px;'"></view>
    <view class="navbar flex-row align-items-center justify-content-center" :style="'height:' + titleBarHeight + 'px'">
      <van-icon v-if="showIcon" class="nav-icon" @click="navBack" size="20px" color="white" name="arrow-left"></van-icon>
      <view v-if="showText" class="card-view1">
        <text>{{ text }}</text>
      </view>
    </view>
  </view>
</template>

<script lang="ts">
import { Vue, Component, Prop } from "vue-property-decorator";
/**
 * 导航栏
 */
@Component({
  name: "NavigationBar",
})
export default class NavigationBar extends Vue {
  @Prop({ default: false }) showText!: boolean; // 是否显示文字
  @Prop({ default: false }) showIcon!: boolean; // 是否显示图标
  @Prop({ default: "" }) text!: string; // 是否显示文字
  @Prop({ default: "" }) path!: string; // 返回的路径
  @Prop({ default: false }) backLevel!: boolean; // 返回上一级

  titleBarHeight = 0;
  statusBarHeight = 0;
  topHeight = 0;
  mounted() {
    let menuButtonObject = uni.getMenuButtonBoundingClientRect(); //获取菜单按钮（右上角胶囊按钮）的布局位置信息。坐标信息以屏幕左上角为原点。
    uni.getSystemInfo({
      //获取系统信息
      success: (res: any) => {
        let navHeight = menuButtonObject.height + (menuButtonObject.top - res.statusBarHeight) * 2; //导航栏高度=菜单按钮高度+（菜单按钮与顶部距离-状态栏高度）*2
        this.titleBarHeight = navHeight;
        this.statusBarHeight = res.statusBarHeight;
        this.topHeight = this.titleBarHeight + this.statusBarHeight;

        // 将导航栏总高度存储到全局，供其他页面使用
        uni.setStorageSync('navigationBarHeight', this.topHeight);
      },
      fail(err) {
        console.log(err);
      },
    });
  }

  // 获取导航栏总高度的静态方法
  static getNavigationBarHeight(): number {
    return uni.getStorageSync('navigationBarHeight') || 88; // 默认88px
  }
  /**
   * 返回
   */
  navBack() {
    // 检查是否有页面可以返回
    const pages = getCurrentPages();
    console.log('NavigationBar navBack - 页面栈信息:');
    console.log('- 页面栈长度:', pages.length);
    console.log('- backLevel:', this.backLevel, typeof this.backLevel);
    console.log('- path:', this.path, typeof this.path, 'length:', this.path?.length);
    console.log('- 页面栈:', pages.map(p => p.route));
    console.log('- 判断条件: pages.length > 1 =', pages.length > 1, ', !this.path =', !this.path);

    // 优先使用 navigateBack 返回上一级
    // 条件：有上一级页面就返回，除非明确设置了跳转路径
    if (pages.length > 1) {
      // 如果设置了非空的 path，则跳转到指定页面
      if (this.path && this.path.trim() !== '') {
        console.log('🔄 有指定路径，使用 reLaunch 跳转');
        this.fallbackToReLaunch();
        return;
      }
      // 否则返回上一级
      console.log('✅ 使用 navigateBack 返回上一级');
      try {
        uni.navigateBack({
          animationType: "auto",
          success: () => {
            console.log('navigateBack 成功');
          },
          fail: (err) => {
            console.error('navigateBack 失败:', err);
            // 如果 navigateBack 失败，fallback 到 reLaunch
            this.fallbackToReLaunch();
          }
        });
      } catch (error) {
        console.error('navigateBack 异常:', error);
        this.fallbackToReLaunch();
      }
      return;
    }

    // 使用 reLaunch 跳转
    this.fallbackToReLaunch();
  }

  fallbackToReLaunch() {
    console.log('🔄 使用 reLaunch 跳转到指定页面');
    let url = "/pages/index/index";
    this.path && (url = `/pages/${this.path}`);
    console.log('跳转到:', url);
    uni.reLaunch({ url: url });
  }
}
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  font-size: 14px;
  line-height: 24px;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
}
.statusbar {
  background: $customize-navigation-bar-color;
}
.navbar {
  background: $customize-navigation-bar-color;
  position: relative;
  width: 100%;
}
.nav-icon {
  position: absolute;
  left: 1vw;
}
.card-view1 {
  color: white;
  font-size: 16px;
}
</style>
