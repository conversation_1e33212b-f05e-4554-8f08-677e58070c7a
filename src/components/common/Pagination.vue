<template>
  <view>
    <!-- 分页组件 -->
    <view class="custom-pagination">
      <span class="total-count">共{{ total }}条数据</span>
      <van-button :disabled="currentPage === 1" type="info" size="small" @click="handlePageChange(currentPage - 1)"> 上一页 </van-button>
      <view class="page-input-wrap">
        <span class="page-input-label">{{ totalPages }}页/</span>
        <input type="text" confirm-type="done" :value="inputPage" input-align="center" class="page-input" @confirm="handleJump" />
      </view>
      <!--      <van-field v-model="currentPage" :label="totalPages + '页/'" type="number" input-align="left" class="page-input" @search="handleJump" />-->
      <van-button :disabled="currentPage === totalPages" type="info" size="small" @click="handlePageChange(currentPage + 1)"> 下一页 </van-button>
    </view>
  </view>
</template>

<script lang="ts">
import { Vue, Component, Prop, Watch } from "vue-property-decorator";

@Component
export default class CustomPagination extends Vue {
  @Prop({ required: true }) readonly total!: number;
  @Prop({ default: 10 }) readonly pageSize!: number;
  @Prop({ default: 1 }) currentPage!: number;

  inputPage = 1;

  // 计算总页数
  get totalPages(): number {
    return Math.ceil(this.total / this.pageSize);
  }
  // mounted() {
  //   this.$set(this, "inputPage", this.currentPage);
  //   console.error("mounted", this.currentPage, this.inputPage);
  // }

  // 翻页操作
  handlePageChange(newPage: number) {
    const validPage = Math.max(1, Math.min(newPage, this.totalPages));
    this.$emit("pageChange", validPage);
  }

  // 跳转功能
  handleJump(event: any) {
    // 判断输入是否为数字
    const page = parseInt(event.detail.value);
    if (!isNaN(page)) {
      this.inputPage = page;
      const validPage = Math.max(1, Math.min(page, this.totalPages));
      this.handlePageChange(validPage);
    } else {
      uni.showToast({ title: "请输入正确的页码", icon: "none" });
      this.inputPage = 1;
      this.$emit("pageChange", 1);
    }
  }

  // 监听总页数变化
  @Watch("currentPage", { deep: true, immediate: true })
  handleCurrentPageChange(newVal: number) {
    this.$set(this, "inputPage", newVal);
  }
}
</script>

<style scoped lang="scss">
.custom-pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 10px 0;
  border-radius: 4px;
}

.page-input {
  width: 60px;
  height: 28px;
  border: 1px solid #ebedf0;
  border-radius: 4px;
  padding: 0 !important;
  text-align: center;
}
.total-count {
  color: #666666;
  font-size: 12px;
}
.page-input-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  font-size: 12px;
  .page-input-label {
    color: #666666;
  }
}
</style>
