<template>
  <view class="annex-card-container">
    <!--左侧内容区域-->
    <view class="annex-card-wrap">
      <!--标题部分-->
      <view class="card-title-wrap">
        <view class="card-title-left">
          <image v-if="campData.icon" :src="campData.icon" style="width: 50px; height: 50px; border-radius: 50%" />
          <view class="card-title-left-text">
            <text class="card-title">{{ campData.title }}</text>
            <text v-if="campData.status" class="card-value"
              >状态：
              <text class="card-status" :class="{ 'card-status-fail': campData.status !== '1' }">
                {{ campData.status === "1" ? "有效" : "无效" }}
              </text>
            </text>
          </view>
        </view>
        <view v-if="campData.salesGroup" class="card-title-right">
          <view
            >所属销售：
            <text class="card-value">{{ campData.salesGroup }}</text>
          </view>
        </view>
      </view>
      <!--字段展示部分-->
      <view class="flex-card-wrap">
        <view class="card-content-wrap" v-for="(item, index) in label" :key="index">
          <view class="content-filed">{{ item }}</view>
          <view>{{ campData.content[index] || "--" }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import Vue from "vue";
import { Component, Prop } from "vue-property-decorator";

@Component({
  name: "InfoCard",
})
export default class InfoCard extends Vue {
  @Prop() label = [] as string[];
  @Prop() campData = {} as any;
}
</script>

<style scoped lang="scss">
.annex-card-container {
  background-color: #fff;
  padding: 10px;
  margin: 10px;
  border-radius: 10px;
  border: 1px solid #999999;

  .annex-card-wrap {
    .card-title-wrap {
      display: flex;
      justify-content: space-between; /* 两端对齐 */
      align-items: center; /* 垂直居中 */
      line-height: 25px;

      .card-title-left {
        display: inline-flex;
        align-items: center; /* 垂直居中 */
        .card-title {
          margin: 0 10px 0 6px;
          font-size: 16px;
          font-weight: 700;
        }
        .card-title-left-text {
          display: flex;
          align-items: center; /* 垂直居中 */
          flex-direction: column;

          .card-value {
            margin: 0 10px 0 6px;
            font-size: 14px;
            color: #999;

            .card-status {
              color: #60c54a;
            }

            .card-status-fail {
              color: #f5222d;
            }
          }
        }
      }
      .card-title-right {
        display: flex;
        align-items: center; /* 垂直居中 */
        .card-value {
          font-size: 14px;
          color: #128feb;
          font-weight: 700;
        }
      }
    }

    .card-content-wrap {
      display: flex;
      align-items: center;
      margin-top: 10px;
      font-size: 14px;

      .content-filed {
        min-width: 120px !important;
        color: #999;
      }
    }

    .btn-wrap {
      height: 30px;
      margin-top: 10px;

      .btn-style {
        min-width: 76px;
        height: 30px;
        float: right;
        line-height: 28px;
        padding: 0 10px;
        margin-left: 10px;
        font-size: 12px;
        border-radius: 15px;
        border: 1px solid #0154a6;
      }

      .btn-white {
        color: #0154a6;
        background-color: #fff;
      }

      .btn-blue {
        color: #fff;
        background-color: #0154a6;
      }
    }
  }
}

.flex-card-wrap {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  align-items: center;
  justify-content: space-between;
}
</style>
