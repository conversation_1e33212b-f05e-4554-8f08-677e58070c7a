<template>
  <van-popup :show="show" position="bottom" round custom-style="width:100vw; max-height: 60vh;" @close="closePopup" close-on-click-overlay>
    <div class="qy-user-selector">
      <div class="header">
        <div class="title">选择企微用户</div>
        <van-icon name="cross" size="20" @click="closePopup" />
      </div>

      <div class="content">
        <div class="description">检测到您绑定了多个企微用户，请选择要使用的企微用户进行分享：</div>

        <div class="user-list">
          <div v-for="(user, index) in qyUsers" :key="index" class="user-item" :class="{ selected: selectedIndex === index }" @click="selectUser(index)">
            <div class="user-info">
              <div class="corp-name">{{ user.corpName }}</div>
              <div class="user-name">{{ user.qyName }}</div>
            </div>
            <van-icon v-if="selectedIndex === index" name="success" color="#1989fa" size="20" />
          </div>
        </div>
      </div>

      <div class="footer">
        <van-button type="info" block :disabled="selectedIndex === -1" @click="confirmSelection"> 确认选择 </van-button>
      </div>
    </div>
  </van-popup>
</template>

<script lang="ts">
import Vue from "vue";
import { Component, Prop } from "vue-property-decorator";

interface QyUser {
  id: string;
  systemUserId: string;
  qyUserId: string;
  qyName: string;
  corpName: string;
  corpId: string;
}

@Component({
  name: "QyUserSelector",
})
export default class QyUserSelector extends Vue {
  @Prop({ default: false }) show!: boolean;
  @Prop({ default: () => [] }) qyUsers!: QyUser[];

  selectedIndex = -1;

  closePopup(): void {
    this.selectedIndex = -1;
    this.$emit("close");
  }

  selectUser(index: number): void {
    this.selectedIndex = index;
  }

  confirmSelection(): void {
    if (this.selectedIndex >= 0 && this.selectedIndex < this.qyUsers.length) {
      const selectedUser = this.qyUsers[this.selectedIndex];
      this.$emit("confirm", {
        corpId: selectedUser.corpId,
        qyUserId: selectedUser.qyUserId,
        corpName: selectedUser.corpName,
        qyName: selectedUser.qyName,
      });
      this.selectedIndex = -1;
    }
  }
}
</script>

<style lang="scss" scoped>
.qy-user-selector {
  padding: 0;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px 16px;
    border-bottom: 1px solid #f0f0f0;

    .title {
      font-size: 18px;
      font-weight: bold;
      color: #333;
    }
  }

  .content {
    padding: 20px 24px;
    max-height: 40vh;
    overflow-y: auto;

    .description {
      font-size: 14px;
      color: #666;
      margin-bottom: 20px;
      line-height: 1.5;
    }

    .user-list {
      .user-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px;
        margin-bottom: 12px;
        background: #f8f9fa;
        border-radius: 8px;
        border: 2px solid transparent;
        cursor: pointer;
        transition: all 0.3s ease;

        &:last-child {
          margin-bottom: 0;
        }

        &.selected {
          background: #e6f4ff;
          border-color: #1989fa;
        }

        &:active {
          background: #e0e0e0;
        }

        .user-info {
          flex: 1;

          .corp-name {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 4px;
          }

          .user-name {
            font-size: 14px;
            color: #666;
          }
        }
      }
    }
  }

  .footer {
    padding: 16px 24px 24px;
    border-top: 1px solid #f0f0f0;
  }
}
</style>
