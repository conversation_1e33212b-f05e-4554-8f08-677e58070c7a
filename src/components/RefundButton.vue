<template>
  <view class="refund-button-container">
    <!-- 申请退款按钮 -->
    <van-button v-if="showRefundButton" :size="size" :type="buttonType" :loading="loading" :disabled="disabled" @click="handleRefundClick">
      {{ buttonText }}
    </van-button>

    <!-- 查看退款详情按钮 -->
    <van-button v-if="showDetailButton" :size="size" type="default" @click="handleDetailClick">
      {{ detailButtonText }}
    </van-button>
  </view>
</template>

<script lang="ts">
import Vue from "vue";
import { Component, Prop } from "vue-property-decorator";
import { Order } from "@/types/mall.types";
import { RefundManager } from "@/utils/refund-manager.ts";

@Component({
  name: "RefundButton",
})
export default class RefundButton extends Vue {
  @Prop({ required: true })
  order!: Order;

  @Prop({ required: true })
  customerId!: string;

  @Prop({ default: "small" })
  size!: string;

  @Prop({ default: false })
  disabled!: boolean;

  @Prop({ default: "申请退款" })
  buttonText!: string;

  @Prop({ default: "查看详情" })
  detailButtonText!: string;

  loading = false;

  get showRefundButton(): boolean {
    // 只有已完成的订单才显示申请退款按钮
    return this.order.orderStatus === "completed";
  }

  get showDetailButton(): boolean {
    // 退款相关状态显示查看详情按钮
    return RefundManager.canViewRefundDetail(this.order.orderStatus);
  }

  get buttonType(): string {
    return this.showRefundButton ? "default" : "primary";
  }

  async handleRefundClick() {
    if (this.loading || this.disabled) {
      return;
    }

    try {
      this.loading = true;

      // 检查退款资格
      const eligibility = await RefundManager.checkRefundEligibility(this.order, this.customerId);

      if (!eligibility.eligible) {
        uni.showToast({
          title: eligibility.reason || "不符合退款条件",
          icon: "none",
          duration: 2000,
        });
        return;
      }

      // 显示退款选择对话框
      this.showRefundOptions(eligibility);
    } catch (error) {
      console.error("退款操作失败:", error);
      uni.showToast({
        title: "操作失败，请重试",
        icon: "none",
      });
    } finally {
      this.loading = false;
    }
  }

  showRefundOptions(eligibility: any) {
    const options = ["快速退款", "详细申请"];

    uni.showActionSheet({
      itemList: options,
      success: (res) => {
        if (res.tapIndex === 0) {
          // 快速退款
          this.handleQuickRefund(eligibility);
        } else if (res.tapIndex === 1) {
          // 详细申请
          this.handleDetailedRefund();
        }
      },
    });
  }

  handleQuickRefund(eligibility: any) {
    // 显示退款原因选择
    RefundManager.showRefundConfirmDialog(this.order, async (reason: string) => {
      try {
        uni.showLoading({ title: "提交中..." });

        const result = await RefundManager.quickApplyRefund(this.order, this.customerId, reason, "快速退款申请");

        if (result.success) {
          uni.showToast({
            title: result.message,
            icon: "success",
          });

          // 通知父组件刷新订单列表
          this.$emit("refund-applied", {
            orderId: this.order.id,
            refundData: result.data,
          });
        } else {
          uni.showToast({
            title: result.message,
            icon: "none",
          });
        }
      } catch (error) {
        console.error("快速退款失败:", error);
        uni.showToast({
          title: "退款申请失败",
          icon: "none",
        });
      } finally {
        uni.hideLoading();
      }
    });
  }

  handleDetailedRefund() {
    try {
      RefundManager.navigateToRefundApply(this.order, this.customerId);
    } catch (error) {
      // 错误已在navigateToRefundApply中处理
    }
  }

  handleDetailClick() {
    // 如果有退款ID，跳转到退款详情；否则跳转到退款列表
    if (this.order.refundId) {
      RefundManager.navigateToRefundDetail(this.order.refundId, this.customerId);
    } else {
      RefundManager.navigateToRefundList(this.customerId);
    }
  }
}
</script>

<style lang="scss" scoped>
.refund-button-container {
  display: inline-block;
}
</style>
