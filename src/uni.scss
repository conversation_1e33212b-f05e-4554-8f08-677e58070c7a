/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */

/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */

/* 颜色变量 */

/* 行为相关颜色 */
$uni-color-primary: #007aff;
$uni-color-success: #4cd964;
$uni-color-warning: #f0ad4e;
$uni-color-error: #0289ea;

/* 文字基本颜色 */
// 标准
$standard-color: linear-gradient(to right, #0289ea, #95c4f6);
$customize-navigation-bar-color: linear-gradient(to right, #0289ea, #95c4f6);
$standard-text-color: rgba(0, 218, 255, 0.51);

// 辅助
$subsidiary-color-1: #2baad3;
$subsidiary-color-2: #a17e2c;
$subsidiary-color-3: #e9cf92;

$gold-color: #bb9f70; // 金色
$silver-color: #b5b5b5; // 银色


/* 背景颜色 */
$uni-bg-color:#ffffff;
$uni-bg-color-grey:#f8f8f8;
$uni-bg-color-hover:#f1f1f1;//点击状态颜色
$uni-bg-color-mask:rgba(0, 0, 0, 0.4);//遮罩颜色

/* 边框颜色 */
$uni-border-color:#c8c7cc;

/* 尺寸变量 */

/* 文字尺寸 */
$uni-font-size-sm:24rpx;
$uni-font-size-base:28rpx;
$uni-font-size-lg:32rpx;

/* 图片尺寸 */
$uni-img-size-sm:40rpx;
$uni-img-size-base:52rpx;
$uni-img-size-lg:80rpx;

/* Border Radius */
$uni-border-radius-sm: 4rpx;
$uni-border-radius-base: 6rpx;
$uni-border-radius-lg: 12rpx;
$uni-border-radius-circle: 50%;

/* 水平间距 */
$uni-spacing-row-sm: 10px;
$uni-spacing-row-base: 20rpx;
$uni-spacing-row-lg: 30rpx;

/* 垂直间距 */
$uni-spacing-col-sm: 8rpx;
$uni-spacing-col-base: 16rpx;
$uni-spacing-col-lg: 24rpx;

/* 透明度 */
$uni-opacity-disabled: 0.3; // 组件禁用态的透明度

/* 导航栏相关 */
$navigation-bar-height: 88px; // 固定导航栏高度

/* 文章场景相关 */
$uni-color-title: #2C405A; // 文章标题颜色
$uni-font-size-title:40rpx;
$uni-color-subtitle: #555555; // 二级标题颜色
$uni-font-size-subtitle:36rpx;
$uni-color-paragraph: #3F536E; // 文章段落颜色
$uni-font-size-paragraph:30rpx;

/* 全局导航栏间距类 */
.page-with-navbar {
  padding-top: $navigation-bar-height !important;
}

.page-with-navbar-center {
  padding-top: calc($navigation-bar-height + 20px) !important;
}

/* 标准导航栏间距（88px） */
.nav-bar-spacing {
  padding-top: 88px !important;
}

/* 页面级导航栏间距（推荐使用） */
.page-with-fixed-navbar {
  padding-top: 88px !important;
  min-height: calc(100vh - 88px);
  box-sizing: border-box;
}

/* 内容区域导航栏间距 */
.content-with-navbar {
  padding-top: 88px !important;
}