/* 统一按钮样式配置 */

/* 主要按钮样式 - 蓝色渐变 */
.btn-primary {
  background: linear-gradient(90deg, #0289ea 0%, #95c4f6 100%) !important;
  color: #fff !important;
  border: none !important;
  border-radius: 22px !important;
  height: 44px !important;
  font-size: 15px !important;
  font-weight: 500 !important;
}

/* 次要按钮样式 - 橙色渐变 */
.btn-secondary {
  background: linear-gradient(90deg, #ff9500 0%, #ffb84d 100%) !important;
  color: #fff !important;
  border: none !important;
  border-radius: 22px !important;
  height: 44px !important;
  font-size: 15px !important;
  font-weight: 500 !important;
}

/* 小尺寸按钮 */
.btn-small {
  height: 36px !important;
  font-size: 14px !important;
  border-radius: 18px !important;
}

/* 大尺寸按钮 */
.btn-large {
  height: 48px !important;
  font-size: 16px !important;
  border-radius: 24px !important;
}

/* 圆形按钮 */
.btn-round {
  border-radius: 50% !important;
  width: 44px !important;
  height: 44px !important;
}

/* 文本按钮 */
.btn-text {
  background: transparent !important;
  color: #666 !important;
  border: 1px solid #ddd !important;
  border-radius: 22px !important;
  height: 44px !important;
  font-size: 15px !important;
}

/* 危险按钮 */
.btn-danger {
  background: linear-gradient(90deg, #ff4444 0%, #ff6666 100%) !important;
  color: #fff !important;
  border: none !important;
  border-radius: 22px !important;
  height: 44px !important;
  font-size: 15px !important;
  font-weight: 500 !important;
}

/* 成功按钮 */
.btn-success {
  background: linear-gradient(90deg, #52c41a 0%, #73d13d 100%) !important;
  color: #fff !important;
  border: none !important;
  border-radius: 22px !important;
  height: 44px !important;
  font-size: 15px !important;
  font-weight: 500 !important;
}

/* 禁用状态 */
.btn-disabled {
  background: #f5f5f5 !important;
  color: #ccc !important;
  border: none !important;
  opacity: 0.6 !important;
}

/* 按钮组 */
.btn-group {
  display: flex;
  gap: 12px;
  align-items: center;
}

.btn-group .btn-item {
  flex: 1;
}

/* 浮动按钮 */
.btn-float {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 999;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 底部操作栏按钮 */
.btn-bottom-action {
  width: 120px;
  height: 40px;
  border-radius: 20px !important;
  font-size: 14px !important;
}

/* 全宽按钮 */
.btn-full-width {
  width: 100% !important;
}

/* 按钮悬浮效果 */
.btn-hover:active {
  transform: scale(0.95);
  transition: transform 0.1s ease;
}

/* 图标按钮 */
.btn-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

/* 渐变边框按钮 */
.btn-gradient-border {
  background: #fff !important;
  color: #0289ea !important;
  border: 2px solid transparent !important;
  background-clip: padding-box !important;
  position: relative;
}

.btn-gradient-border::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 22px;
  padding: 2px;
  background: linear-gradient(90deg, #0289ea 0%, #95c4f6 100%);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  z-index: -1;
}

/* 响应式按钮 */
@media (max-width: 375px) {
  .btn-responsive {
    height: 40px !important;
    font-size: 14px !important;
    border-radius: 20px !important;
  }
}

/* 按钮加载状态 */
.btn-loading {
  position: relative;
  color: transparent !important;
}

.btn-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  border: 2px solid #fff;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: btn-spin 1s linear infinite;
}

@keyframes btn-spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* 按钮组合样式 */
.btn-combination {
  display: flex;
  border-radius: 22px;
  overflow: hidden;
  border: 1px solid #ddd;
}

.btn-combination .btn-item {
  flex: 1;
  border-radius: 0 !important;
  border: none !important;
  border-right: 1px solid #ddd;
}

.btn-combination .btn-item:last-child {
  border-right: none;
}

/* 特殊场景按钮 */
.btn-cart {
  background: linear-gradient(90deg, #ff9500 0%, #ffb84d 100%) !important;
}

.btn-buy {
  background: linear-gradient(90deg, #0289ea 0%, #95c4f6 100%) !important;
}

.btn-checkout {
  background: linear-gradient(90deg, #0289ea 0%, #95c4f6 100%) !important;
}

.btn-submit {
  background: linear-gradient(90deg, #52c41a 0%, #73d13d 100%) !important;
}

.btn-cancel {
  background: #f5f5f5 !important;
  color: #666 !important;
}

.btn-delete {
  background: linear-gradient(90deg, #ff4444 0%, #ff6666 100%) !important;
}
