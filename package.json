{"name": "hxsy-vx-ui", "version": "0.1.0", "private": true, "scripts": {"serve": "npm run dev", "build": "cross-env NODE_ENV=prod UNI_PLATFORM=mp-weixin vue-cli-service uni-build", "build:dev": "cross-env NODE_ENV=dev UNI_PLATFORM=mp-weixin vue-cli-service uni-build", "local": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch --mode local", "uat": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch --mode hxsy-uat", "prod": "cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch --mode hxsy-prod", "info": "node node_modules/@dcloudio/vue-cli-plugin-uni/commands/info.js"}, "dependencies": {"@dcloudio/uni-h5": "^2.0.1-33520211229002", "@dcloudio/uni-helper-json": "*", "@dcloudio/uni-i18n": "^2.0.1-33520211229002", "@dcloudio/uni-mp-vue": "^2.0.1-33520211229002", "@dcloudio/uni-mp-weixin": "^2.0.1-33520211229002", "@dcloudio/uni-stat": "^2.0.1-33520211229002", "@tencentcloud/chat": "^3.5.2", "@vue/shared": "^3.0.0", "core-js": "^3.6.5", "crypto-js": "^4.1.1", "flyio": "^0.6.2", "postcss": "^7.0.39", "regenerator-runtime": "^0.12.1", "sm-crypto": "^0.3.13", "stompjs": "^2.3.3", "tim-upload-plugin": "^1.4.2", "tim-wx-sdk": "^2.19.1", "trtc-wx-sdk": "^1.1.1", "vue": "^2.6.11", "vue-class-component": "^6.3.2", "vue-property-decorator": "^8.0.0", "vuex": "^3.2.0"}, "devDependencies": {"@babel/plugin-syntax-typescript": "^7.2.0", "@babel/runtime": "~7.12.0", "@dcloudio/types": "*", "@dcloudio/uni-automator": "^2.0.1-33520211229002", "@dcloudio/uni-cli-i18n": "^2.0.1-33520211229002", "@dcloudio/uni-cli-shared": "^2.0.1-33520211229002", "@dcloudio/uni-migration": "^2.0.1-33520211229002", "@dcloudio/uni-template-compiler": "^2.0.1-33520211229002", "@dcloudio/vue-cli-plugin-hbuilderx": "^2.0.1-33520211229002", "@dcloudio/vue-cli-plugin-uni": "^2.0.1-33520211229002", "@dcloudio/vue-cli-plugin-uni-optimize": "^2.0.1-33520211229002", "@dcloudio/webpack-uni-mp-loader": "^2.0.1-33520211229002", "@dcloudio/webpack-uni-pages-loader": "^2.0.1-33520211229002", "@typescript-eslint/eslint-plugin": "^4.18.0", "@typescript-eslint/parser": "^4.18.0", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "^4.5.0", "@vue/cli-plugin-typescript": "^4.5.15", "@vue/cli-service": "~4.5.0", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^7.0.0", "babel-plugin-import": "^1.11.0", "cross-env": "^7.0.2", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-vue": "^6.2.2", "jest": "^25.4.0", "mini-types": "*", "miniprogram-api-typings": "^4.0.7", "postcss-comment": "^2.0.0", "sass": "^1.26.5", "sass-loader": "^8.0.2", "typescript": "~4.1.5", "vue-template-compiler": "^2.6.11"}, "browserslist": ["Android >= 4.4", "ios >= 9"], "uni-app": {"scripts": {}}}